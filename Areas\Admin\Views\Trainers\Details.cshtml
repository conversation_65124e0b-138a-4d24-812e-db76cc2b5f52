﻿@model Roya.Models.Trainer

@{
    ViewData["Title"] = "تفاصيل المدرب";
}

<div class="container mt-5" style="max-width: 800px;">
    <h2 class="text-center mb-4" style="font-weight: bold; color: #333;">📄 تفاصيل المدرب</h2>

    <div class="card shadow rounded p-4">
        <div class="row">
            <div class="col-md-4 text-center">
                <img src="@(!string.IsNullOrEmpty(Model.Image) ? Url.Content(Model.Image) : "/images/default-user.png")"
                     class="img-fluid rounded-circle mb-3" style="max-width: 200px; height: 200px; object-fit: cover; border: 3px solid #eee;">
                <h5 class="fw-bold">@Model.FullName</h5>
                <span class="badge rounded-pill px-3 py-2 mt-2"
                      style="background-color:@GetStatusColor(Model.Status); color:#fff;">
                    @GetStatusText(Model.Status)
                </span>
            </div>

            <div class="col-md-8">
                <table class="table table-borderless">
                    <tr>
                        <th>البريد الإلكتروني:</th>
                        <td>@Model.Email</td>
                    </tr>
                    <tr>
                        <th>رقم الهاتف:</th>
                        <td>@Model.PhoneNum</td>
                    </tr>
                    <tr>
                        <th>المدينة:</th>
                        <td>@Model.City</td>
                    </tr>
                    <tr>
                        <th>العمر:</th>
                        <td>@Model.Age</td>
                    </tr>
                    <tr>
                        <th>سنوات الخبرة:</th>
                        <td>@Model.ExperienceYears</td>
                    </tr>
                    <tr>
                        <th>السيرة الذاتية:</th>
                        <td>
                            @if (!string.IsNullOrEmpty(Model.CV))
                            {
                                <a href="@Url.Content(Model.CV)" class="btn btn-outline-primary btn-sm" target="_blank">📄 تحميل</a>
                            }
                            else
                            {
                                <span class="text-muted">غير متوفر</span>
                            }
                        </td>
                    </tr>
                </table>

                <a asp-action="AllTrainersWithStatus" class="btn btn-secondary mt-3">🔙 العودة</a>
            </div>
        </div>
    </div>
</div>

@functions {
    string GetStatusText(TrainerStatus status)
    {
        return status switch
        {
            TrainerStatus.Pending => "⏳ قيد المراجعة",
            TrainerStatus.Approved => "✔ مقبول",
            TrainerStatus.Rejected => "✘ مرفوض",
            _ => "غير معروف"
        };
    }

    string GetStatusColor(TrainerStatus status)
    {
        return status switch
        {
            TrainerStatus.Pending => "#ffc107",   // أصفر
            TrainerStatus.Approved => "#28a745",  // أخضر
            TrainerStatus.Rejected => "#dc3545",  // أحمر
            _ => "#6c757d"                          // رمادي
        };
    }
}


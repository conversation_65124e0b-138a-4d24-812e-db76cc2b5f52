﻿@model IEnumerable<Roya.Models.Course>
@{
    ViewData["Title"] = "طلبات الكورسات";
}

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<h2 class="text-center my-4">📥 طلبات الكورسات (قيد المراجعة)</h2>

<table class="table table-bordered table-striped table-hover text-center shadow-sm">
    <thead class="table-light">
        <tr>
            <th>📘 اسم الكورس</th>
            <th>👨‍🏫 اسم المدرب</th>
            <th>📂 التصنيف</th>
            <th>🗓️ تاريخ الإضافة</th>
            <th>🔧 إجراءات</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var course in Model)
        {
            <tr>
                <td>@course.CourseName</td>
                <td>@course.Trainer?.FullName</td>
                <td>@course.Category?.CategoryName</td>
                <td>@course.CreatedDate.ToString("yyyy/MM/dd")</td>
                <td>
                    <a asp-action="Edit" asp-route-id="@course.CourseId" class="btn btn-sm btn-warning">✏️ تعديل</a>

                    <!-- زر قبول -->
                    <form id="<EMAIL>" asp-action="Approve" asp-route-id="@course.CourseId" method="post" class="d-inline">
                        <button type="button" class="btn btn-sm btn-success" onclick="confirmAction('@course.CourseId', 'approve')">✅ قبول</button>
                    </form>

                    <!-- زر رفض -->
                    <form id="<EMAIL>" asp-action="Reject" asp-route-id="@course.CourseId" method="post" class="d-inline">
                        <button type="button" class="btn btn-sm btn-danger" onclick="confirmAction('@course.CourseId', 'reject')">❌ رفض</button>
                    </form>
                </td>
            </tr>
        }
    </tbody>
</table>

@section Scripts {
    <script>
        // ✅ تأكيد قبل قبول أو رفض الدورة
        function confirmAction(courseId, action) {
            let title = (action === 'approve') ? 'هل تريد قبول الدورة؟' : 'هل تريد رفض الدورة؟';
            let confirmButtonText = (action === 'approve') ? 'نعم، قبول' : 'نعم، رفض';
            let formId = (action === 'approve') ? '#approveForm-' + courseId : '#rejectForm-' + courseId;

            Swal.fire({
                title: title,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: confirmButtonText,
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.querySelector(formId).submit();
                }
            });
        }

        // ✅ عرض رسالة نجاح بعد الإجراء
        @if (TempData["Success"] != null)
        {
            <text>
                    Swal.fire({
                        icon: 'success',
                        title: 'تم بنجاح',
                        text: '@TempData["Success"]',
                        confirmButtonText: 'حسنًا'
                    });
            </text>
        }

        @if (TempData["Error"] != null)
        {
            <text>
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: '@TempData["Error"]',
                        confirmButtonText: 'حسنًا'
                    });
            </text>
        }
    </script>
}

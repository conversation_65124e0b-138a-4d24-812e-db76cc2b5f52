﻿@model List<Roya.Models.Course>
@using Roya.Models
@{
    ViewData["Title"] = "جميع الكورسات";
    var selectedField = ViewBag.SelectedField as string ?? "Trainer";
    var searchQuery = ViewBag.TrainerName as string;
}

<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" />

<style>
    #coursesTable tbody tr:hover {
        background-color: #f0f8ff;
        transition: background-color 0.3s ease;
        cursor: pointer;
    }
</style>

<div class="container mt-5" style="max-width: 1000px;">
    <h2 class="text-center mb-4">📚 جميع الكورسات</h2>

    <form method="get" class="row g-3 mb-4 align-items-end">
        <div class="col-md-4">
            <input type="text" name="trainerName" class="form-control" placeholder="اكتب للبحث..."
                   value="@searchQuery" />
        </div>

        <div class="col-md-2 d-flex justify-content-end gap-2">
            <button type="submit" class="btn btn-primary">🔍</button>
            <a asp-action="AllCourses" class="btn btn-secondary">↻</a>
        </div>

        <div class="col-md-3">
            <select name="field" class="form-select">
                <option value="Trainer" selected="@(selectedField == "Trainer")">بحث باسم المدرب</option>
                <option value="Course" selected="@(selectedField == "Course")">بحث باسم الدورة</option>
            </select>
        </div>

        <div class="col-md-3">
            <select name="status" class="form-select">
                <option value="">كل الحالات</option>
                <option value="Pending" selected="@(ViewBag.SelectedStatus == CourseStatus.Pending.ToString())">قيد المراجعة</option>
                <option value="Approved" selected="@(ViewBag.SelectedStatus == CourseStatus.Approved.ToString())">مقبولة</option>
                <option value="Rejected" selected="@(ViewBag.SelectedStatus == CourseStatus.Rejected.ToString())">مرفوضة</option>

            </select>
        </div>
    </form>

    @if (Model == null || !Model.Any())
    {
        <div class="alert alert-warning text-center">😔 لا توجد نتائج تطابق البحث</div>
    }
    else
    {
        <table id="coursesTable" class="table table-striped table-bordered table-hover text-center align-middle" style="width:100%">
            <thead class="table-light">
                <tr>
                    <th>اسم الدورة</th>
                    <th>اسم المدرب</th>
                    <th>الحالة</th>
                    <th>تفاصيل</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var course in Model)
                {
                    <tr>
                        <td>@course.CourseName</td>
                        <td>@course.Trainer?.FullName</td>
                        <td>
                            @switch (course.Status)
                            {
                                case CourseStatus.Pending:
                                    <span class="badge bg-warning text-dark">قيد المراجعة</span>
                                    break;
                                case CourseStatus.Approved:
                                    <span class="badge bg-success">مقبولة</span>
                                    break;
                                case CourseStatus.Rejected:
                                    <span class="badge bg-danger">مرفوضة</span>
                                    break;
                            }
                        </td>
                        <td>
                            <a asp-action="Edit" asp-route-id="@course.CourseId" class="btn btn-sm btn-outline-primary">عرض التفاصيل</a>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    }
</div>

<!-- DataTables JS -->
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#coursesTable').DataTable({
                "language": {
                    "search": "🔎 بحث:",
                    "lengthMenu": "عرض _MENU_ عنصر لكل صفحة",
                    "info": "عرض _START_ إلى _END_ من أصل _TOTAL_ عنصر",
                    "paginate": {
                        "first": "الأول",
                        "last": "الأخير",
                        "next": "التالي",
                        "previous": "السابق"
                    },
                    "zeroRecords": "😔 لا توجد نتائج",
                    "infoEmpty": "لا توجد عناصر لعرضها"
                }
            });
        });
    </script>
}

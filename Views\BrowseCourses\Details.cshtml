﻿@model Roya.Models.Course
@{
    ViewData["Title"] = "تفاصيل الدورة";
}

<section class="container py-5" dir="rtl">
    <!-- ✅ العنوان مع مسافة من الناف بار -->
    <div class="row justify-content-center mt-5 mb-4">
        <div class="col-lg-8 text-center">
            <h2 class="fw-bold text-primary">@Model.CourseName</h2>
            <p class="text-muted">اكتشف الدورات المتاحة واشترك في ما يناسبك</p>
        </div>
    </div>

    <!-- ✅ صورة الدورة -->
    <div class="row justify-content-center mb-4">
        <div class="col-md-8 text-center">
            <img src="~/Pictures/@Model.Image" alt="صورة الدورة" class="img-fluid rounded shadow" style="max-height: 350px;" />
        </div>
    </div>

    <!-- ✅ معلومات أساسية -->
    <div class="row justify-content-center mb-4">
        <div class="col-md-10 bg-light rounded p-4 shadow-sm text-end">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <strong>👨‍🏫 المدرب:</strong><br />
                    @Model.Trainer?.FullName
                </div>
                <div class="col-md-3 mb-3">
                    <strong>⏳ المدة:</strong><br />
                    @Model.Duration
                </div>
                <div class="col-md-3 mb-3">
                    <strong>📅 تبدأ:</strong><br />
                    @Model.StartDate.ToString("yyyy/MM/dd")
                </div>
                <div class="col-md-3 mb-3">
                    <strong>📅 تنتهي:</strong><br />
                    @Model.EndDate.ToString("yyyy/MM/dd")
                </div>
            </div>
        </div>
    </div>

    <!-- ✅ عن الدورة -->
    <div class="row justify-content-center mb-4">
        <div class="col-md-10">
            <div class="card border-0 shadow-sm text-end">
                <div class="card-body">
                    <h5 class="card-title fw-bold mb-2 text-primary">📖 عن الدورة</h5>
                    <p class="card-text text-muted">@Model.Description</p>
                </div>
            </div>
        </div>
    </div>

    <!-- ✅ محاور الدورة -->
    @if (!string.IsNullOrEmpty(Model.CourseTopics))
    {
        <div class="row justify-content-center mb-4">
            <div class="col-md-10">
                <div class="card border-0 shadow-sm text-end">
                    <div class="card-body">
                        <h5 class="card-title fw-bold mb-2 text-primary">📋 محاور الدورة</h5>
                        <ul class="list-unstyled text-muted">
                            @foreach (var topic in Model.CourseTopics.Split('\n'))
                            {
                                if (!string.IsNullOrWhiteSpace(topic))
                                {
                                    <li>• @topic</li>
                                }
                            }
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- ✅ الفئة المستهدفة -->
    @if (!string.IsNullOrEmpty(Model.Targetpeople))
    {
        <div class="row justify-content-center mb-4">
            <div class="col-md-10">
                <div class="card border-0 shadow-sm text-end">
                    <div class="card-body">
                        <h5 class="card-title fw-bold mb-2 text-primary">👥 الفئة المستهدفة</h5>
                        <p class="card-text text-muted">@Model.Targetpeople</p>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- ✅ المكان -->
    <div class="row justify-content-center mb-4">
        <div class="col-md-10">
            <div class="card border-0 shadow-sm text-end">
                <div class="card-body">
                    <h5 class="card-title fw-bold mb-2 text-primary">📍 المكان</h5>
                    <p class="card-text text-muted">@Model.Location</p>
                </div>
            </div>
        </div>
    </div>

    <!-- ✅ زر الاشتراك -->
    <div class="row justify-content-center">
        <div class="col-md-10 text-center" dir="rtl">

            @if (!User.Identity.IsAuthenticated)
            {
                <div class="alert alert-warning text-center mt-4">
                    🔐 <strong>
                        للاشتراك في هذه الدورة، يجب أولاً
                        <a asp-controller="Account" asp-action="Login" class="text-decoration-underline fw-bold">تسجيل الدخول</a>
                        أو
                        <a asp-controller="Account" asp-action="RegisterTrainee" class="text-decoration-underline fw-bold">إنشاء حساب</a>.
                    </strong>
                </div>
            }
            else if (User.IsInRole("Trainee"))
            {
                <a asp-action="Join" asp-route-id="@Model.CourseId" class="btn btn-success btn-lg mt-3">
                    📝 الاشتراك في الدورة
                </a>
            }

        </div>
    </div>

    
</section>

﻿@model List<Roya.ViewModels.TrainerCourseVM>
@{
    ViewData["Title"] = "دوراتي التي تحتوي على مشاركين";
}

<div class="container mt-5" dir="rtl">
    <h2 class="text-center text-primary mb-4">📚 دورات تحتوي على مشتركين</h2>

    @if (Model.Any())
    {
        <table class="table table-bordered text-center shadow-sm">
            <thead class="table-light">
                <tr>
                    <th>اسم الدورة</th>
                    <th>عدد المشاركين</th>
                    <th>عرض الطلبة</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var course in Model)
                {
                    <tr>
                        <td>@course.CourseName</td>
                        <td>@course.ParticipantCount</td>
                        <td>
                            <a asp-action="Participants" asp-route-id="@course.CourseId" class="btn btn-outline-primary btn-sm">👥 عرض الطلبة</a>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    }
    else
    {
        <div class="alert alert-info text-center">
            لا توجد دورات تحتوي على مشاركين حالياً.
        </div>
    }
</div>

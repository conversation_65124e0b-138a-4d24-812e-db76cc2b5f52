/* _content/Roya/Areas/Admin/Views/Shared/_AdminLayout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-jsq5kikmg7] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-jsq5kikmg7] {
  color: #0077cc;
}

.btn-primary[b-jsq5kikmg7] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-jsq5kikmg7], .nav-pills .show > .nav-link[b-jsq5kikmg7] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-jsq5kikmg7] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-jsq5kikmg7] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-jsq5kikmg7] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-jsq5kikmg7] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-jsq5kikmg7] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
/* _content/Roya/Areas/TrainerA/Views/Shared/_TrainerLayout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-932jh59sv7] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-932jh59sv7] {
  color: #0077cc;
}

.btn-primary[b-932jh59sv7] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-932jh59sv7], .nav-pills .show > .nav-link[b-932jh59sv7] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-932jh59sv7] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-932jh59sv7] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-932jh59sv7] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-932jh59sv7] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-932jh59sv7] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
/* _content/Roya/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-6x2crewwav] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-6x2crewwav] {
  color: #0077cc;
}

.btn-primary[b-6x2crewwav] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-6x2crewwav], .nav-pills .show > .nav-link[b-6x2crewwav] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-6x2crewwav] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-6x2crewwav] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-6x2crewwav] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-6x2crewwav] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-6x2crewwav] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جميع الدورات - رؤية 2030</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="admin-dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- الشريط الجانبي -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="logo.png" alt="رؤية 2030">
                    <h3>لوحة التحكم</h3>
                </div>
                <button class="sidebar-toggle-btn" id="sidebarToggle" title="تصغير/توسيع القائمة">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item">
                        <a href="admin-dashboard.html" class="nav-link">
                            <i class="fas fa-home"></i>
                            <span>الصفحة الرئيسية</span>
                        </a>
                    </li>
                    
                    <li class="nav-item has-submenu">
                        <a href="#" class="nav-link">
                            <i class="fas fa-chalkboard-teacher"></i>
                            <span>إدارة المدربين</span>
                            <i class="fas fa-chevron-down submenu-arrow"></i>
                        </a>
                        <ul class="submenu">
                            <li><a href="trainer-requests.html">طلبات المدربين</a></li>
                            <li><a href="all-trainers.html">جميع المدربين</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item has-submenu open">
                        <a href="#" class="nav-link">
                            <i class="fas fa-book"></i>
                            <span>إدارة الدورات</span>
                            <i class="fas fa-chevron-down submenu-arrow"></i>
                        </a>
                        <ul class="submenu">
                            <li><a href="course-requests.html">طلبات الدورات</a></li>
                            <li><a href="all-courses.html" class="active">جميع الدورات</a></li>
                            <li><a href="approved-courses.html">الدورات المقبولة</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item">
                        <a href="settings.html" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>تسجيل الخروج</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- الهيدر العلوي -->
            <header class="main-header">
                <div class="header-content">
                    <h1>جميع الدورات</h1>
                    <div class="user-info">
                        <div class="user-avatar">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="المدير">
                        </div>
                        <div class="user-details">
                            <span class="user-name">أحمد محمد</span>
                            <span class="user-role">مدير النظام</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- المحتوى -->
            <div class="dashboard-content">
                <!-- إحصائيات سريعة -->
                <div class="quick-stats">
                    <div class="stat-item">
                        <span class="stat-number">97</span>
                        <span class="stat-label">إجمالي الدورات</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">85</span>
                        <span class="stat-label">دورات مقبولة</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">8</span>
                        <span class="stat-label">قيد المراجعة</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">4</span>
                        <span class="stat-label">دورات مرفوضة</span>
                    </div>
                </div>

                <!-- جدول الدورات -->
                <div class="table-section">
                    <div class="section-header">
                        <h2>قائمة الدورات</h2>
                        <div class="header-actions">
                            <button class="btn btn-primary" onclick="exportCoursesData()">
                                <i class="fas fa-download"></i>
                                تصدير البيانات
                            </button>
                            <button class="btn btn-success" onclick="addNewCourse()">
                                <i class="fas fa-plus"></i>
                                إضافة دورة جديدة
                            </button>
                        </div>
                    </div>

                    <!-- أدوات البحث والفلترة -->
                    <div class="search-filters">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="search-input" placeholder="البحث باسم الدورة أو المدرب...">
                        </div>
                        
                        <div class="filter-group">
                            <select id="status-filter">
                                <option value="">جميع الحالات</option>
                                <option value="approved">مقبولة</option>
                                <option value="pending">قيد المراجعة</option>
                                <option value="rejected">مرفوضة</option>
                            </select>
                            
                            <select id="category-filter">
                                <option value="">جميع التصنيفات</option>
                                <option value="tech">تقنية المعلومات</option>
                                <option value="marketing">التسويق</option>
                                <option value="design">التصميم</option>
                                <option value="business">إدارة الأعمال</option>
                            </select>
                            
                            <input type="date" id="date-filter" placeholder="تاريخ الإضافة">
                        </div>
                    </div>

                    <!-- الجدول -->
                    <div class="table-container">
                        <table class="data-table">
                            <colgroup>
                                <col style="width: 250px;">
                                <col style="width: 180px;">
                                <col style="width: 150px;">
                                <col style="width: 130px;">
                                <col style="width: 140px;">
                                <col style="width: 100px;">
                                <col style="width: 170px;">
                            </colgroup>
                            <thead>
                                <tr>
                                    <th>اسم الدورة</th>
                                    <th>اسم المدرب</th>
                                    <th>التصنيف</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>المدة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="table-row" data-status="approved" data-category="tech">
                                    <td><strong>تطوير مواقع الويب الشاملة</strong></td>
                                    <td>أحمد علي محمد</td>
                                    <td><span class="category-badge tech">تقنية المعلومات</span></td>
                                    <td><span class="status-badge approved">مقبولة</span></td>
                                    <td>2024-01-15</td>
                                    <td>8 أسابيع</td>
                                    <td class="actions">
                                        <button class="btn-icon btn-info" onclick="viewCourseDetails(1)" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon btn-warning" onclick="editCourse(1)" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon btn-success" onclick="approveCourse(1)" title="قبول">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn-icon btn-danger" onclick="rejectCourse(1)" title="رفض">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </td>
                                </tr>
                                
                                <tr class="table-row" data-status="pending" data-category="marketing">
                                    <td><strong>التسويق الرقمي المتقدم</strong></td>
                                    <td>فاطمة أحمد سالم</td>
                                    <td><span class="category-badge marketing">التسويق</span></td>
                                    <td><span class="status-badge pending">قيد المراجعة</span></td>
                                    <td>2024-01-12</td>
                                    <td>6 أسابيع</td>
                                    <td class="actions">
                                        <button class="btn-icon btn-info" onclick="viewCourseDetails(2)" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon btn-warning" onclick="editCourse(2)" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon btn-success" onclick="approveCourse(2)" title="قبول">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn-icon btn-danger" onclick="rejectCourse(2)" title="رفض">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </td>
                                </tr>
                                
                                <tr class="table-row" data-status="approved" data-category="design">
                                    <td><strong>التصميم الجرافيكي الاحترافي</strong></td>
                                    <td>سارة أحمد علي</td>
                                    <td><span class="category-badge design">التصميم</span></td>
                                    <td><span class="status-badge approved">مقبولة</span></td>
                                    <td>2024-01-08</td>
                                    <td>7 أسابيع</td>
                                    <td class="actions">
                                        <button class="btn-icon btn-info" onclick="viewCourseDetails(3)" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon btn-warning" onclick="editCourse(3)" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon btn-success" onclick="approveCourse(3)" title="قبول">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn-icon btn-danger" onclick="rejectCourse(3)" title="رفض">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </td>
                                </tr>
                                
                                <tr class="table-row" data-status="rejected" data-category="business">
                                    <td><strong>إدارة المشاريع الأساسية</strong></td>
                                    <td>محمد سالم عبدالله</td>
                                    <td><span class="category-badge business">إدارة الأعمال</span></td>
                                    <td><span class="status-badge rejected">مرفوضة</span></td>
                                    <td>2024-01-05</td>
                                    <td>5 أسابيع</td>
                                    <td class="actions">
                                        <button class="btn-icon btn-info" onclick="viewCourseDetails(4)" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon btn-warning" onclick="editCourse(4)" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon btn-success" onclick="approveCourse(4)" title="قبول">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn-icon btn-danger" onclick="rejectCourse(4)" title="رفض">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </td>
                                </tr>
                                
                                <tr class="table-row" data-status="approved" data-category="tech">
                                    <td><strong>البرمجة بـ Python المتقدمة</strong></td>
                                    <td>خالد محمود أحمد</td>
                                    <td><span class="category-badge tech">تقنية المعلومات</span></td>
                                    <td><span class="status-badge approved">مقبولة</span></td>
                                    <td>2024-01-02</td>
                                    <td>12 أسبوع</td>
                                    <td class="actions">
                                        <button class="btn-icon btn-info" onclick="viewCourseDetails(5)" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon btn-warning" onclick="editCourse(5)" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon btn-success" onclick="approveCourse(5)" title="قبول">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn-icon btn-danger" onclick="rejectCourse(5)" title="رفض">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- الترقيم -->
                    <div class="pagination">
                        <button class="btn btn-secondary" disabled>السابق</button>
                        <span class="page-info">صفحة 1 من 8</span>
                        <button class="btn btn-secondary">التالي</button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="admin-dashboard.js"></script>
    <script>
        function exportCoursesData() {
            alert('سيتم تصدير بيانات الدورات قريباً');
        }
        
        function addNewCourse() {
            window.location.href = 'add-course.html';
        }
    </script>
</body>
</html>

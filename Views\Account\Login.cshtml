﻿@model LoginViewModel
@{
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>Login</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />

</head>
<body style="background-color:#f5f5f5;">

    <div style="display:flex;justify-content:center;align-items:center;min-height:100vh;">
        <div style="background-color:#fdf9f9;border-radius:20px;padding:40px 30px;width:350px;box-shadow:0 0 10px rgba(0,0,0,0.1);text-align:center;">

            <!-- شعار -->
            <img src="~/Pictures/Roya 2030 Logo.png" alt="Logo" style="width:80px;height:auto;margin-bottom:20px;" />

            <!-- عنوان -->
            <h2 style="margin-bottom:20px;font-weight:bold;color:#333;">Login</h2>

            <!-- فورم تسجيل الدخول -->
            <form asp-action="Login" method="post">
                <!-- 🔴 هنا يتم عرض الأخطاء -->
                @if (!ViewData.ModelState.IsValid)
                {
                    <div class="alert alert-danger">
                        @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                        {
                            <div>@error.ErrorMessage</div>
                        }
                    </div>
                }
                <div class="form-group mb-3 text-start">
                    <label>Email</label>
                    <input asp-for="Email" class="form-control" placeholder="<EMAIL>" />
                    <span asp-validation-for="Email" class="text-danger"></span>
                </div>

                <div class="form-group mb-3 text-start">
                    <label>Password</label>
                    <input asp-for="Password" class="form-control" type="password" placeholder="••••••••" />
                    <span asp-validation-for="Password" class="text-danger"></span>
                </div>

                <div class="form-group form-check text-start mb-3">
                    <input asp-for="RememberMe" class="form-check-input" />
                    <label asp-for="RememberMe" class="form-check-label">Remember me</label>
                </div>

                <button type="submit" class="btn w-100 text-white" style=" background: linear-gradient(135deg, #fca311, #fb8500);">Log In</button>

                <div class="mt-3">
                    <a asp-action="ForgotPassword" class="text-muted">Forgot Password?</a>
                </div>
            </form>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation-unobtrusive@3.2.12/jquery.validate.unobtrusive.min.js"></script>

</body>
</html>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}

@{
    ViewData["Title"] = "رؤية 2030 - الصفحة الرئيسية";
    Layout = null; // استخدام layout مخصص
}

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>رؤية 2030 - الصفحة الرئيسية</title>
    <link rel="stylesheet" href="~/Front-End/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- شاشة التحميل -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">جاري التحميل...</div>
            <div class="loading-progress">
                <div class="loading-bar"></div>
            </div>
        </div>
    </div>

    <!-- حاوية الإشعارات -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- ناف بار -->
    <nav class="navbar">
        <div class="logo">
            <img src="~/Front-End/logo.svg" alt="رؤية 2030">
        </div>

        <!-- زر المنيو للشاشات الصغيرة -->
        <button class="mobile-menu-btn" id="mobileMenuBtn">
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
        </button>

        <div class="nav-links" id="navLinks">
            <a href="@Url.Action("Index", "Home")" class="active">الصفحة الرئيسية</a>
            <a href="@Url.Action("Index", "BrowseCourses")">الدورات</a>
            @if (User.Identity.IsAuthenticated && User.IsInRole("Trainee"))
            {
                <a href="@Url.Action("MyCourses", "BrowseCourses")">دوراتي</a>
            }
            <a href="#about">نبذة عنا</a>
        </div>
        <div class="auth-buttons" id="authButtons">
            @if (User.Identity.IsAuthenticated)
            {
                <span class="welcome-text">مرحباً، @User.Identity.Name</span>
                <a href="@Url.Action("Logout", "Account")" class="login-btn">تسجيل خروج</a>
            }
            else
            {
                <a href="@Url.Action("Login", "Account")" class="login-btn">تسجيل دخول</a>
            }
        </div>
    </nav>

    <!-- قسم الهيرو المحسن -->
    <section class="hero">
        <div class="hero-background">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
                <div class="shape shape-5"></div>
            </div>
        </div>
        <div class="hero-overlay"></div>
        <div class="container">
            <div class="hero-content">
                <div class="hero-logo-section">
                    <div class="animated-logo">
                        <div class="logo-square">
                            <div class="logo-circle"></div>
                        </div>
                    </div>
                </div>
                <div class="hero-text">
                    <h1 class="hero-title">
                        <span class="title-main">نُلهم الشباب</span>
                        <span class="title-sub">لتشكيل الغد</span>
                    </h1>
                    <p class="hero-description">
                        منصة رؤية 2030 التعليمية - رحلتك نحو التميز والإبداع تبدأ هنا
                    </p>
                    <p class="hero-quote">
                        <i class="fas fa-quote-right"></i>
                        "التعليم هو أقوى سلاح يمكن استخدامه لتغيير العالم"
                        <i class="fas fa-quote-left"></i>
                    </p>
                    <div class="hero-buttons">
                        <a href="@Url.Action("Index", "BrowseCourses")" class="cta-button primary ripple btn-glow">
                            <span>ابدأ رحلتك التعليمية</span>
                            <i class="fas fa-arrow-left"></i>
                        </a>
                        <a href="#about" class="cta-button secondary ripple">
                            <span>اكتشف رؤيتنا</span>
                            <i class="fas fa-info-circle"></i>
                        </a>
                    </div>

                </div>
            </div>
        </div>
    </section>

    <!-- قسم نبذة عنا -->
    <section id="about" class="about-section">
        <div class="container">
            <div class="about-content">
                <div class="about-text">
                    <h2>نبذة عن رؤية 2030</h2>
                    <p>منصة رؤية 2030 هي مبادرة تعليمية رائدة تهدف إلى تمكين الشباب الليبي وتطوير قدراتهم في مختلف المجالات التقنية والمهنية. نحن نؤمن بأن التعليم الجودة والتدريب المتخصص هما أساس بناء مستقبل مشرق ومستدام.</p>
                    <p>من خلال دوراتنا المتنوعة والمصممة بعناية، نسعى لسد الفجوة بين التعليم الأكاديمي ومتطلبات سوق العمل، مما يساهم في إعداد جيل قادر على قيادة التحول الرقمي والتنمية المستدامة في ليبيا.</p>
                </div>
                <div class="about-images">
                    <div class="image-grid">
                        <div class="image-item">
                            <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=300&h=200&fit=crop" alt="صورة تدريب">
                        </div>
                        <div class="image-item">
                            <img src="https://images.unsplash.com/photo-1531482615713-2afd69097998?w=300&h=200&fit=crop" alt="صورة ورشة عمل">
                        </div>
                        <div class="image-item">
                            <img src="https://images.unsplash.com/photo-1515187029135-18ee286d815b?w=300&h=200&fit=crop" alt="صورة مؤتمر">
                        </div>
                        <div class="image-item">
                            <img src="https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=300&h=200&fit=crop" alt="صورة تخرج">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم المستهدفون -->
    <section class="target-section">
        <div class="container">
            <h2>المستهدفون في رؤية 2030</h2>
            <div class="target-cards">
                <div class="target-card">
                    <div class="icon">
                        <i class="fas fa-hand-holding-usd"></i>
                    </div>
                    <h3>ذوي الدخل المحدود من الشباب</h3>
                </div>
                <div class="target-card">
                    <div class="icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <h3>أرباب الأسر</h3>
                </div>
                <div class="target-card">
                    <div class="icon">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <h3>أفراد دور الرعاية<br><span class="subtitle">(الشباب المستقلين فوق 18 عام)</span></h3>
                </div>
                <div class="target-card">
                    <div class="icon">
                        <i class="fas fa-wheelchair"></i>
                    </div>
                    <h3>أصحاب الهمم</h3>
                </div>
            </div>
            <div class="target-quote">
                <p>نحن في رؤية 2030 نؤمن بأن كل فرد يستحق الفرصة للتعلم والنمو. لذلك نقدم برامج تدريبية شاملة ومتنوعة تلبي احتياجات جميع فئات المجتمع، من الطلاب والخريجين إلى المهنيين وأصحاب الهمم، لنبني معاً مستقبلاً أفضل للجميع.</p>
            </div>
        </div>
    </section>

    <!-- قسم أحدث الدورات -->
    <section class="latest-courses">
        <div class="container">
            <h2>أحدث الدورات</h2>
            <div class="courses-container">
                <!-- الدورة الأولى -->
                <div class="course-card">
                    <div class="course-image">
                        <img src="https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=250&fit=crop" alt="دورة تطوير الويب">
                        <div class="course-category">تطوير الويب</div>
                        <div class="course-date">15 يناير 2024</div>
                    </div>
                    <div class="course-details">
                        <h3>تطوير مواقع الويب الشاملة</h3>
                        <div class="course-meta-info">
                            <div class="course-instructor">
                                <i class="fas fa-user"></i>
                                أحمد محمد
                            </div>
                            <div class="course-duration">
                                <i class="fas fa-clock"></i>
                                8 أسابيع
                            </div>
                        </div>
                        <p class="description">دورة شاملة لتعلم تطوير مواقع الويب من الصفر باستخدام HTML, CSS, و JavaScript</p>
                        <div class="course-start-date">
                            <i class="fas fa-calendar-alt"></i>
                            <span>تاريخ البداية: 15 يناير 2024</span>
                        </div>
                        <div class="course-footer">
                            <a href="@Url.Action("Index", "BrowseCourses")" class="enroll-btn ripple btn-glow">عرض التفاصيل</a>
                        </div>
                    </div>
                </div>

                <!-- الدورة الثانية -->
                <div class="course-card">
                    <div class="course-image">
                        <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=250&fit=crop" alt="دورة التسويق الرقمي">
                        <div class="course-category">تسويق رقمي</div>
                        <div class="course-date">27 يناير 2024</div>
                    </div>
                    <div class="course-details">
                        <h3>التسويق الرقمي المتقدم</h3>
                        <div class="course-meta-info">
                            <div class="course-instructor">
                                <i class="fas fa-user"></i>
                                فاطمة أحمد
                            </div>
                            <div class="course-duration">
                                <i class="fas fa-clock"></i>
                                6 أسابيع
                            </div>
                        </div>
                        <p class="description">إتقان استراتيجيات التسويق الرقمي ووسائل التواصل الاجتماعي لتنمية الأعمال</p>
                        <div class="course-start-date">
                            <i class="fas fa-calendar-alt"></i>
                            <span>تاريخ البداية: 27 يناير 2024</span>
                        </div>
                        <div class="course-footer">
                            <a href="@Url.Action("Index", "BrowseCourses")" class="enroll-btn ripple btn-glow">عرض التفاصيل</a>
                        </div>
                    </div>
                </div>

                <!-- الدورة الثالثة -->
                <div class="course-card">
                    <div class="course-image">
                        <img src="https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=250&fit=crop" alt="دورة إدارة الأعمال">
                        <div class="course-category">إدارة</div>
                        <div class="course-date">1 مارس 2024</div>
                    </div>
                    <div class="course-details">
                        <h3>إدارة المشاريع الاحترافية</h3>
                        <div class="course-meta-info">
                            <div class="course-instructor">
                                <i class="fas fa-user"></i>
                                محمد علي
                            </div>
                            <div class="course-duration">
                                <i class="fas fa-clock"></i>
                                10 أسابيع
                            </div>
                        </div>
                        <p class="description">دورة شاملة في إدارة المشاريع وفق معايير PMI وأفضل الممارسات العالمية</p>
                        <div class="course-start-date">
                            <i class="fas fa-calendar-alt"></i>
                            <span>تاريخ البداية: 1 مارس 2024</span>
                        </div>
                        <div class="course-footer">
                            <a href="@Url.Action("Index", "BrowseCourses")" class="enroll-btn ripple btn-glow">عرض التفاصيل</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="view-all">
                <a href="@Url.Action("Index", "BrowseCourses")" class="view-all-btn">عرض جميع الدورات</a>
            </div>
        </div>
    </section>

    <!-- قسم الإحصائيات المحسن -->
    <section class="enhanced-stats-section">
        <div class="container">
            <div class="stats-header">
                <h2>إنجازاتنا بالأرقام</h2>
                <p>نفخر بما حققناه من نجاحات في رحلتنا التعليمية</p>
            </div>

            <div class="enhanced-stats-grid">
                <div class="enhanced-stat-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="stat-content">
                        <h3 class="animated-number" data-target="2500">0</h3>
                        <p class="stat-label">متدرب مسجل</p>
                        <span class="stat-description">من جميع أنحاء ليبيا</span>
                    </div>
                </div>

                <div class="enhanced-stat-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon">
                            <i class="fas fa-book-open"></i>
                        </div>
                    </div>
                    <div class="stat-content">
                        <h3 class="animated-number" data-target="85">0</h3>
                        <p class="stat-label">دورة تدريبية</p>
                        <span class="stat-description">في مختلف المجالات</span>
                    </div>
                </div>

                <div class="enhanced-stat-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                    </div>
                    <div class="stat-content">
                        <h3 class="animated-number" data-target="45">0</h3>
                        <p class="stat-label">مدرب خبير</p>
                        <span class="stat-description">معتمدين ومتخصصين</span>
                    </div>
                </div>

                <div class="enhanced-stat-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon">
                            <i class="fas fa-award"></i>
                        </div>
                    </div>
                    <div class="stat-content">
                        <h3 class="animated-number" data-target="1950">0</h3>
                        <p class="stat-label">شهادة معتمدة</p>
                        <span class="stat-description">تم إصدارها بنجاح</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- فاصل تصميمي -->
    <div class="section-divider">
        <div class="divider-content">
            <div class="divider-line"></div>
            <div class="divider-icon">
                <i class="fas fa-star"></i>
            </div>
            <div class="divider-line"></div>
        </div>
    </div>

    <!-- الفوتر -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="~/Front-End/logo.svg" alt="رؤية 2030">
                </div>
                <div class="footer-links">
                    <h3>روابط سريعة</h3>
                    <ul>
                        <li><a href="@Url.Action("Index", "Home")">الرئيسية</a></li>
                        <li><a href="@Url.Action("Index", "BrowseCourses")">الدورات</a></li>
                        @if (User.Identity.IsAuthenticated && User.IsInRole("Trainee"))
                        {
                            <li><a href="@Url.Action("MyCourses", "BrowseCourses")">دوراتي</a></li>
                        }
                        <li><a href="#about">نبذة عنا</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>تواصل معنا</h3>
                    <p><i class="fas fa-map-marker-alt"></i> بنغازي، ليبيا</p>
                    <p><i class="fas fa-phone"></i> +218 91-234-5678</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
                <div class="footer-social">
                    <h3>تابعنا</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 رؤية بنغازي 2030. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script>
        // تحريك الأرقام في قسم الإحصائيات
        function animateNumbers() {
            const numbers = document.querySelectorAll('.stat-number');

            numbers.forEach(number => {
                const target = parseInt(number.getAttribute('data-target'));
                const increment = target / 100;
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    number.textContent = Math.floor(current);
                }, 20);
            });
        }

        // تشغيل الأنيميشن عند ظهور القسم
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateNumbers();
                    observer.unobserve(entry.target);
                }
            });
        });

        document.addEventListener('DOMContentLoaded', () => {
            const statsSection = document.querySelector('.stats-section');
            if (statsSection) {
                observer.observe(statsSection);
            }

            // إخفاء شاشة التحميل
            setTimeout(() => {
                const loadingScreen = document.getElementById('loadingScreen');
                if (loadingScreen) {
                    loadingScreen.style.opacity = '0';
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';

                        // إضافة تأثيرات الظهور للعناصر
                        const elements = document.querySelectorAll('.hero-content, .course-card, .target-card');
                        elements.forEach((element, index) => {
                            setTimeout(() => {
                                element.classList.add('fade-in');
                            }, index * 100);
                        });
                    }, 500);
                }
            }, 2000);

            // عرض إشعار ترحيبي
            setTimeout(() => {
                showToast('مرحباً بك!', 'أهلاً بك في منصة رؤية 2030 للتدريب', 'success');
            }, 3000);
        });

        // نظام الإشعارات
        function showToast(title, message, type = 'info') {
            const container = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;

            const icons = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };

            toast.innerHTML = `
                <div class="toast-icon">
                    <i class="${icons[type]}"></i>
                </div>
                <div class="toast-content">
                    <div class="toast-title">${title}</div>
                    <div class="toast-message">${message}</div>
                </div>
                <button class="toast-close" onclick="closeToast(this)">
                    <i class="fas fa-times"></i>
                </button>
            `;

            container.appendChild(toast);

            // إظهار الإشعار
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            // إخفاء الإشعار تلقائياً
            setTimeout(() => {
                closeToast(toast.querySelector('.toast-close'));
            }, 5000);
        }

        function closeToast(button) {
            const toast = button.closest('.toast');
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }

        // إضافة تأثيرات للأزرار
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('enroll-btn')) {
                e.preventDefault();
                showToast('تم بنجاح!', 'تم توجيهك لصفحة التفاصيل', 'success');
                setTimeout(() => {
                    window.location.href = e.target.href;
                }, 1000);
            }
        });

        // العد المتحرك للإحصائيات
        function animateNumbers() {
            const numbers = document.querySelectorAll('.animated-number');

            numbers.forEach(number => {
                // التحقق من أن العدد لم يتم تحريكه من قبل
                if (number.dataset.animated === 'true') return;

                const target = parseInt(number.getAttribute('data-target'));
                const increment = target / 100; // سرعة العد
                let current = 0;

                // وضع علامة أن العدد بدأ التحريك
                number.dataset.animated = 'true';

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }

                    // تنسيق الرقم مع الفواصل
                    number.textContent = Math.floor(current).toLocaleString('ar-EG');
                }, 20);
            });
        }

        // تشغيل العد المتحرك عند ظهور القسم
        function observeStatsSection() {
            const statsSection = document.querySelector('.enhanced-stats-section');
            if (!statsSection) return;

            // تشغيل فوري إذا كان القسم مرئي بالفعل
            const rect = statsSection.getBoundingClientRect();
            const isVisible = rect.top < window.innerHeight && rect.bottom > 0;

            if (isVisible) {
                setTimeout(animateNumbers, 500); // تأخير قصير للتأكد من التحميل
            }

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        setTimeout(animateNumbers, 200); // تأخير قصير
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1, // تقليل العتبة للشاشات الصغيرة
                rootMargin: '50px' // إضافة هامش للتفعيل المبكر
            });

            observer.observe(statsSection);
        }

        // تشغيل المراقب عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            observeStatsSection();
            initMobileMenu();
            setupViewportHeight();

            // إضافة تأثيرات AOS للكروت
            const cards = document.querySelectorAll('.enhanced-stat-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(50px)';
                    card.style.transition = 'all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275)';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });

            // تحقق إضافي بعد تحميل الصفحة
            setTimeout(() => {
                checkAndAnimateStats();
            }, 1000);
        });

        // دالة للتحقق وتشغيل العد إذا لم يعمل
        function checkAndAnimateStats() {
            const numbers = document.querySelectorAll('.animated-number');
            let needsAnimation = false;

            numbers.forEach(number => {
                if (number.textContent === '0' && number.dataset.animated !== 'true') {
                    needsAnimation = true;
                }
            });

            if (needsAnimation) {
                const statsSection = document.querySelector('.enhanced-stats-section');
                if (statsSection) {
                    const rect = statsSection.getBoundingClientRect();
                    // إذا كان القسم مرئي ولم يتم تحريك الأرقام
                    if (rect.top < window.innerHeight && rect.bottom > 0) {
                        animateNumbers();
                    }
                }
            }
        }

        // مراقبة تغيير حجم الشاشة
        window.addEventListener('resize', function() {
            setTimeout(checkAndAnimateStats, 500);
        });

        // مراقبة التمرير للتأكد من تشغيل العد
        window.addEventListener('scroll', function() {
            checkAndAnimateStats();
        });

        // إعداد ارتفاع الشاشة الديناميكي للهواتف
        function setupViewportHeight() {
            function setViewportHeight() {
                const vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', `${vh}px`);

                // تحديث ارتفاع الهيرو
                const hero = document.querySelector('.hero');
                if (hero && window.innerWidth <= 768) {
                    hero.style.minHeight = `calc(${window.innerHeight}px)`;
                }
            }

            // تشغيل عند التحميل
            setViewportHeight();

            // تشغيل عند تغيير حجم الشاشة
            window.addEventListener('resize', setViewportHeight);

            // تشغيل عند تغيير اتجاه الشاشة
            window.addEventListener('orientationchange', function() {
                setTimeout(setViewportHeight, 100);
            });
        }

        // تأثير الشعاع المتحرك للكروت
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.enhanced-stat-card');

            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-15px) scale(1.05)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });

        // وظائف المنيو المتجاوب
        function initMobileMenu() {
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const navLinks = document.getElementById('navLinks');

            if (mobileMenuBtn && navLinks) {
                mobileMenuBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // تبديل حالة الزر
                    this.classList.toggle('active');

                    // تبديل حالة القائمة
                    navLinks.classList.toggle('active');

                    // منع التمرير عند فتح المنيو
                    if (this.classList.contains('active')) {
                        document.body.style.overflow = 'hidden';
                    } else {
                        document.body.style.overflow = 'auto';
                    }
                });

                // إغلاق المنيو عند النقر على رابط
                const navLinksItems = navLinks.querySelectorAll('a');
                navLinksItems.forEach(link => {
                    link.addEventListener('click', function() {
                        mobileMenuBtn.classList.remove('active');
                        navLinks.classList.remove('active');
                        document.body.style.overflow = 'auto';
                    });
                });

                // إغلاق المنيو عند تغيير حجم الشاشة
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 768) {
                        mobileMenuBtn.classList.remove('active');
                        navLinks.classList.remove('active');
                        document.body.style.overflow = 'auto';
                    }
                });
            }
        }
    </script>
</body>
</html>

﻿@model List<Roya.ViewModels.AdminCourseVM>
@using Roya.Models
@{
    ViewData["Title"] = "الدورات التي تحتوي على طلبة";
}

<div class="container mt-5" dir="rtl">
    <h2 class="text-center text-primary mb-4">📚 الدورات</h2>

    <!-- 🔍 نموذج البحث والفلترة -->
    <div class="row justify-content-center mb-4">
        <div class="col-md-10">
            <form asp-action="CoursesWithParticipants" method="get" class="card p-3 shadow-sm bg-light">
                <div class="row g-2 align-items-center">
                    <div class="col-md-4">
                        <select name="filterBy" class="form-select">
                            <option value="">اختر نوع الفلترة</option>
                            <option value="course">🔍 باسم الدورة</option>
                            <option value="trainer">👨‍🏫 باسم المدرب</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <input type="text" name="search" class="form-control" placeholder="اكتب كلمة البحث..." />
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-dark w-100">تصفية</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- ✅ جدول عرض الدورات -->
    @if (Model.Any())
    {
        <table class="table table-bordered text-center shadow-sm">
            <thead class="table-light">
                <tr>
                    <th>اسم الدورة</th>
                    <th>اسم المدرب</th>
                    <th>الحالة</th>
                    <th>عدد الطلبة</th>
                    <th>إجراءات</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var course in Model)
                {
                    <tr>
                        <td>@course.CourseName</td>
                        <td>@course.TrainerName</td>
                        <td>
                            @switch (course.CourseStatus)
                            {
                                case CourseStatus.Approved:
                                    <span class="badge bg-success">مقبولة</span>
                                    break;
                                case CourseStatus.Rejected:
                                    <span class="badge bg-danger">مرفوضة</span>
                                    break;
                                case CourseStatus.Pending:
                                    <span class="badge bg-warning text-dark">قيد المراجعة</span>
                                    break;
                            }
                        </td>
                        <td><span class="badge bg-dark">@course.ParticipantCount</span></td>
                        <td>
                            <a asp-area="Admin" asp-controller="AdminCourses" asp-action="Participants" asp-route-id="@course.CourseId" class="btn btn-outline-primary btn-sm mb-1">
                                👥 عرض الطلبة
                            </a>

                            <!-- ✅ أزرار SweetAlert -->
                            <button class="btn btn-success btn-sm mb-1" onclick="confirmAction('approve', @course.CourseId)">✔️ قبول</button>
                            <button class="btn btn-danger btn-sm mb-1" onclick="confirmAction('reject', @course.CourseId)">✖️ رفض</button>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    }
    else
    {
        <div class="alert alert-info text-center">
            لا توجد دورات مطابقة للبحث.
        </div>
    }
</div>

<!-- ✅ SweetAlert2 -->
<link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

@section Scripts {
    <script>
        function confirmAction(action, courseId) {
            Swal.fire({
                title: action === 'approve' ? 'تأكيد القبول' : 'تأكيد الرفض',
                text: action === 'approve' ? 'هل أنت متأكد من قبول هذه الدورة؟' : 'هل أنت متأكد من رفض هذه الدورة؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: action === 'approve' ? '#28a745' : '#d33'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = action === 'approve' ? '/Admin/AdminCourses/Approve' : '/Admin/AdminCourses/Reject';

                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'id';
                    input.value = courseId;
                    form.appendChild(input);

                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }

        // ✅ عرض SweetAlert لنتائج العمليات (باستخدام Html.Raw لمنع الرموز الغريبة)
        document.addEventListener("DOMContentLoaded", function () {
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
                        Swal.fire({
                            icon: 'success',
                            title: 'نجاح',
                            html: '@Html.Raw(TempData["SuccessMessage"])'
                        });
            </text>
        }

        @if (TempData["WarningMessage"] != null)
        {
            <text>
                        Swal.fire({
                            icon: 'warning',
                            title: 'تنبيه',
                            html: '@Html.Raw(TempData["WarningMessage"])'
                        });
            </text>
        }
        });
    </script>
}

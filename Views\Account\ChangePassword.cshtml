﻿@model ChangePasswordViewModel

@{
    ViewData["Title"] = "Change Password";
}

<h2>Change Password</h2>

<form asp-action="ChangePassword" method="post">
    <div>
        <label asp-for="CurrentPassword"></label>
        <input asp-for="CurrentPassword" type="password" />
        <span asp-validation-for="CurrentPassword"></span>
    </div>

    <div>
        <label asp-for="NewPassword"></label>
        <input asp-for="NewPassword" type="password" />
        <span asp-validation-for="NewPassword"></span>
    </div>

    <div>
        <label asp-for="ConfirmPassword"></label>
        <input asp-for="ConfirmPassword" type="password" />
        <span asp-validation-for="ConfirmPassword"></span>
    </div>

    <button type="submit">Change Password</button>
</form>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}

﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Roya.Data;
using Roya.Models;

namespace Roya.Areas.TrainerA.Controllers
{
    [Authorize(Roles = "Trainer")]
    [Area("TrainerA")]
    public class ProfileController : Controller
    {
        private readonly UserManager<IdentityUser> _userManager;
        private readonly AppDbContext _context;

        public ProfileController(UserManager<IdentityUser> userManager, AppDbContext context)
        {
            _userManager = userManager;
            _context = context;
        }

        [HttpGet]
        public async Task<IActionResult> Edit()
        {
            var user = await _userManager.GetUserAsync(User);
            var trainer = await _context.Trainers.FindAsync(user.Id);
            return View(trainer);
        }

        [HttpPost]
        public async Task<IActionResult> Edit(Trainer model, IFormFile? ImageFile, IFormFile? CVFile)
        {
            var user = await _userManager.GetUserAsync(User);
            var trainer = await _context.Trainers.FindAsync(user.Id);
            if (trainer == null) return NotFound();

            var oldStatus = trainer.Status;

            trainer.FullName = model.FullName;
            trainer.PhoneNum = model.PhoneNum;
            trainer.City = model.City;
            trainer.Age = model.Age;
            trainer.ExperienceYears = model.ExperienceYears;

            if (ImageFile != null)
            {
                string imgName = Guid.NewGuid() + Path.GetExtension(ImageFile.FileName);
                string imgPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot/uploads/TrainerImages", imgName);
                using var stream = new FileStream(imgPath, FileMode.Create);
                await ImageFile.CopyToAsync(stream);
                trainer.Image = $"/uploads/TrainerImages/{imgName}";
            }

            if (CVFile != null)
            {
                string cvName = Guid.NewGuid() + Path.GetExtension(CVFile.FileName);
                string cvPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot/uploads/CVs", cvName);
                using var stream = new FileStream(cvPath, FileMode.Create);
                await CVFile.CopyToAsync(stream);
                trainer.CV = $"/uploads/CVs/{cvName}";
            }

            trainer.Status = TrainerStatus.Pending;

            _context.TrainerStatusLogs.Add(new TrainerStatusLog
            {
                TrainerId = trainer.Id,
                OldStatus = oldStatus,
                NewStatus = TrainerStatus.Pending,
                ChangedBy = trainer.UserName ?? "Trainer",
                ChangeTime = DateTime.Now
            });

            _context.Update(trainer);
            await _context.SaveChangesAsync();

            // ✅ تسجيل خروج
            await HttpContext.SignOutAsync();

            TempData["Message"] = "✅ تم تعديل البيانات بنجاح. سيتم مراجعتها من قبل الإدارة، ولا يمكنك الدخول حتى يتم اعتماد الحساب من جديد.";
            return RedirectToAction("Login", "Account", new { area = "" }); // أو غيرها حسب اسم الكنترولر عندك
        }



    }
}

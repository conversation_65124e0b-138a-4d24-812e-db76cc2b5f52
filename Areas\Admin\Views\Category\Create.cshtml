﻿@model Category

@{
    ViewData["Title"] = "➕ إضافة تصنيف";
}

<div class="container mt-5" style="max-width: 600px;">
    <h2 class="text-center mb-4" style="color: #333; font-weight: bold;">
        ➕ إضافة تصنيف جديد
    </h2>

    <div class="shadow rounded p-4" style="background-color: #fff; border: 1px solid #eee;">
        <form asp-action="Create" enctype="multipart/form-data" method="post">
            <div class="mb-3">
                <label asp-for="CategoryName" class="form-label fw-bold text-muted">اسم التصنيف</label>
                <input asp-for="CategoryName" class="form-control rounded-pill shadow-sm" placeholder="أدخل اسم التصنيف" />
                <span asp-validation-for="CategoryName" class="text-danger small"></span>
            </div>

            <div class="d-flex justify-content-between mt-4">
                <button type="submit" class="btn text-white fw-bold px-4 rounded-pill shadow-sm"
                        style="background: linear-gradient(135deg, #ffa94d, #ff6b00); border: none;">
                    ➕ إضافة
                </button>

                <a asp-action="Index" class="btn btn-outline-secondary rounded-pill px-4 shadow-sm">
                    🔙 
                </a>
            </div>
        </form>
    </div>
</div>

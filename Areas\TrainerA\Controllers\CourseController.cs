﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Roya.Data;
using Roya.Models;
using Roya.ViewModels;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Hosting;

namespace Roya.Areas.TrainerA.Controllers
{
    [Area("TrainerA")]
    [Authorize(Roles = "Trainer")]
    public class CourseController : Controller
    {
        private readonly AppDbContext _context;
        private readonly UserManager<IdentityUser> _userManager;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public CourseController(AppDbContext context, UserManager<IdentityUser> userManager, IWebHostEnvironment webHostEnvironment)
        {
            _context = context;
            _userManager = userManager;
            _webHostEnvironment = webHostEnvironment;
        }

        [HttpGet]
        public IActionResult Create()
        {
            var vm = new CourseWithQuestionsVM
            {
                Questions = new List<QuestionVM> { new QuestionVM() }
            };
            ViewBag.Categories = _context.Categories.ToList();
            return View(vm);
        }
        private async Task<string> SaveFile(IFormFile file, string folder)
        {
            string uploads = Path.Combine(_webHostEnvironment.WebRootPath, "uploads", folder);
            Directory.CreateDirectory(uploads);
            string fileName = Guid.NewGuid().ToString() + Path.GetExtension(file.FileName);
            string filePath = Path.Combine(uploads, fileName);
            using (var fs = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(fs);
            }
            return $"/uploads/{folder}/{fileName}";
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CourseWithQuestionsVM vm)
        {
            if (!ModelState.IsValid || vm.Questions == null || vm.Questions.Count == 0)
            {
                if (vm.Questions == null || vm.Questions.Count == 0)
                {
                    ModelState.AddModelError(string.Empty, "يجب إضافة سؤال واحد على الأقل.");
                }

                ViewBag.Categories = _context.Categories.ToList();
                return View(vm);
            }

            var trainer = await _userManager.GetUserAsync(User);

            // حفظ الكورس
            var course = vm.Course;
        

            course.UserId = trainer.Id;
            course.Status = CourseStatus.Pending;

            // حفظ الصورة لو فيه
            if (course.PictureFile != null)
            {
                string coursepicPath = await SaveFile(vm.Course.PictureFile, "CourseImeges");
             course.Image = coursepicPath; 
            }
          
            _context.Courses.Add(course);
            await _context.SaveChangesAsync();

            // حفظ الأسئلة
            foreach (var q in vm.Questions)
            {
                var question = new Question
                {
                    CourseId = course.CourseId,
                    QuestionText = q.QuestionText,
                    Type = q.QuestionType,
                };

                // لو نوع السؤال اختياري: نحفظ الخيارات
                if (q.QuestionType == QuestionType.MultipleChoice && !string.IsNullOrEmpty(q.OptionsText))
                {
                    var options = q.OptionsText
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(opt => new ChoiceOption
                        {
                            OptionText = opt.Trim()
                        }).ToList();

                    question.Options = options;
                }

                _context.Questions.Add(question);
            }

            await _context.SaveChangesAsync();
            TempData["Success"] = "تم إرسال الكورس للمراجعة.";

            return RedirectToAction("MyCourses");
        }
        public async Task<IActionResult> MyCourses(string? search)
        {
            var trainer = await _userManager.GetUserAsync(User);

            var query = _context.Courses
                .Include(c => c.Category)
                .Where(c => c.UserId == trainer.Id);

            if (!string.IsNullOrWhiteSpace(search))
            {
                query = query.Where(c => c.CourseName.Contains(search));
            }

            var courses = await query
                .OrderByDescending(c => c.CreatedDate) // ✅ الأحدث أولاً
                .ToListAsync();

            return View(courses);
        }


        [HttpGet]
        public async Task<IActionResult> Details(int id)
        {
            var trainer = await _userManager.GetUserAsync(User);

            var course = await _context.Courses
                .Include(c => c.Category)
                .Include(c => c.Questions)
                    .ThenInclude(q => q.Options)
                .FirstOrDefaultAsync(c => c.CourseId == id && c.UserId == trainer.Id);

            if (course == null)
            {
                return NotFound();
            }

            return View(course);
        }

    }
}

﻿@model Roya.ViewModels.TraineeAnswersVM

@{
    ViewData["Title"] = "إجابات المتدرب";
}

<div class="container mt-5" dir="rtl">
    <h3 class="text-center text-primary mb-4">📋 إجابات المتدرب في الدورة</h3>

    <!-- 🧍‍♂️ بيانات المتدرب -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-info text-white fw-bold">
            🧍‍♂️ بيانات المتدرب
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-6">
                    <p><strong>📛 الاسم الكامل:</strong> @Model.FullName</p>
                    <p><strong>📍 المدينة:</strong> @Model.City</p>
                    <p><strong>📘 التخصص / المهنة:</strong> @Model.MajorOrProfession</p>
                </div>
                <div class="col-md-6">
                    <p><strong>📞 رقم الهاتف:</strong> @Model.PhoneNum</p>
                    <p><strong>🎂 العمر:</strong> @Model.Age</p>
                </div>
            </div>
        </div>
    </div>

    <!-- ✍️ إجابات المتدرب -->
    <div class="card shadow-sm mb-5">
        <div class="card-header bg-secondary text-white fw-bold">
            ✍️ إجابات المتدرب على أسئلة الدورة
        </div>
        <div class="card-body">
            @if (Model.Answers != null && Model.Answers.Any())
            {
                <ul class="list-group list-group-flush">
                    @foreach (var answer in Model.Answers)
                    {
                        <li class="list-group-item">
                            <p><strong>❓ السؤال:</strong> @answer.QuestionText</p>
                            <p><strong>✅ الإجابة:</strong> @answer.AnswerText</p>
                        </li>
                    }
                </ul>
            }
            else
            {
                <div class="alert alert-warning text-center mt-3">
                    ⚠️ لا توجد إجابات لهذا المتدرب.
                </div>
            }
        </div>
    </div>
</div>

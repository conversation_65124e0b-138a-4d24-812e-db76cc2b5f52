﻿@model List<Trainer>
<h2>جميع طلبات المدربين</h2>

@if (TempData["Message"] != null)
{
    <div class="alert alert-info">@TempData["Message"]</div>
}

<table class="table">
    <thead>
        <tr>
            <th>الاسم</th>
            <th>البريد</th>
            <th>الهاتف</th>
            <th>المدينة</th>
            <th>الحالة</th>
            <th>إجراءات</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var t in Model)
        {
            <tr>
                <td>@t.FullName</td>                    
                <td>@t.Email</td>
                <td>@t.PhoneNum</td>
                <td>@t.City</td>
                <td>@t.Status</td>
                <td>
                    @if (t.Status == TrainerStatus.Pending)
                    {
                        <form asp-action="Approve" method="post" style="display:inline">
                            <input type="hidden" name="id" value="@t.Id" />
                            <button class="btn btn-success btn-sm">قبول</button>
                        </form>
                        <form asp-action="Reject" method="post" style="display:inline">
                            <input type="hidden" name="id" value="@t.Id" />
                            <button class="btn btn-danger btn-sm">رفض</button>
                        </form>
                    }
                </td>
            </tr>
        }
    </tbody>
</table>

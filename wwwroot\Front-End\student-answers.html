<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إجابات الطالب - رؤية 2030</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="admin-dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- الشريط الجانبي -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="logo.png" alt="رؤية 2030">
                    <h3>لوحة التحكم</h3>
                </div>
                <button class="sidebar-toggle-btn" id="sidebarToggle" title="تصغير/توسيع القائمة">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item">
                        <a href="admin-dashboard.html" class="nav-link">
                            <i class="fas fa-home"></i>
                            <span>الصفحة الرئيسية</span>
                        </a>
                    </li>
                    
                    <li class="nav-item has-submenu">
                        <a href="#" class="nav-link">
                            <i class="fas fa-chalkboard-teacher"></i>
                            <span>إدارة المدربين</span>
                            <i class="fas fa-chevron-down submenu-arrow"></i>
                        </a>
                        <ul class="submenu">
                            <li><a href="trainer-requests.html">طلبات المدربين</a></li>
                            <li><a href="all-trainers.html">جميع المدربين</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item has-submenu open">
                        <a href="#" class="nav-link">
                            <i class="fas fa-book"></i>
                            <span>إدارة الدورات</span>
                            <i class="fas fa-chevron-down submenu-arrow"></i>
                        </a>
                        <ul class="submenu">
                            <li><a href="course-requests.html">طلبات الدورات</a></li>
                            <li><a href="all-courses.html">جميع الدورات</a></li>
                            <li><a href="approved-courses.html">الدورات المقبولة</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item">
                        <a href="settings.html" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>تسجيل الخروج</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- الهيدر العلوي -->
            <header class="main-header">
                <div class="header-content">
                    <div class="header-left">
                        <button class="btn btn-secondary" onclick="goBack()">
                            <i class="fas fa-arrow-right"></i>
                            رجوع
                        </button>
                        <h1>إجابات الطالب</h1>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="المدير">
                        </div>
                        <div class="user-details">
                            <span class="user-name">أحمد محمد</span>
                            <span class="user-role">مدير النظام</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- المحتوى -->
            <div class="dashboard-content">
                <!-- بيانات الطالب -->
                <div class="student-profile-card">
                    <div class="student-profile-header">
                        <div class="profile-image">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=120&h=120&fit=crop&crop=face" alt="علي أحمد محمد">
                            <div class="status-badge pending">
                                <i class="fas fa-clock"></i>
                                قيد المراجعة
                            </div>
                        </div>
                        
                        <div class="profile-info">
                            <h2>علي أحمد محمد</h2>
                            <p class="student-id">رقم الطالب: ST001</p>
                            
                            <div class="contact-info">
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <span><EMAIL></span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <span>+218 91 234 5678</span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>بنغازي، ليبيا</span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-birthday-cake"></i>
                                    <span>25 سنة</span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span>بكالوريوس هندسة حاسوب</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إجابات الطالب -->
                <div class="answers-section">
                    <div class="section-header">
                        <h2>إجابات استمارة التقديم</h2>
                        <div class="submission-date">
                            <i class="fas fa-calendar"></i>
                            تم التقديم في: 2024-01-20
                        </div>
                    </div>

                    <div class="answers-container">
                        <!-- السؤال الأول -->
                        <div class="answer-item">
                            <div class="question">
                                <h3>
                                    <i class="fas fa-question-circle"></i>
                                    لماذا تريد الالتحاق بهذه الدورة؟
                                </h3>
                            </div>
                            <div class="answer">
                                <p>أريد الالتحاق بدورة تطوير مواقع الويب لأنني أسعى لتطوير مهاراتي في مجال البرمجة والتقنية. أعتقد أن هذه الدورة ستساعدني في بناء أساس قوي في تطوير المواقع الإلكترونية وستفتح لي آفاقاً جديدة في سوق العمل. كما أنني مهتم بتعلم أحدث التقنيات والأدوات المستخدمة في هذا المجال.</p>
                            </div>
                        </div>

                        <!-- السؤال الثاني -->
                        <div class="answer-item">
                            <div class="question">
                                <h3>
                                    <i class="fas fa-question-circle"></i>
                                    ما هي خبرتك السابقة في مجال البرمجة؟
                                </h3>
                            </div>
                            <div class="answer">
                                <p>لدي خبرة أساسية في البرمجة من خلال دراستي الجامعية في هندسة الحاسوب. تعلمت لغات البرمجة مثل C++ و Java، وقمت بتطوير بعض المشاريع البسيطة كجزء من المنهج الدراسي. كما أنني أتابع دورات أونلاين في مجال تطوير الويب وأحاول تطبيق ما أتعلمه في مشاريع شخصية صغيرة.</p>
                            </div>
                        </div>

                        <!-- السؤال الثالث -->
                        <div class="answer-item">
                            <div class="question">
                                <h3>
                                    <i class="fas fa-question-circle"></i>
                                    ما هي أهدافك المهنية بعد إتمام الدورة؟
                                </h3>
                            </div>
                            <div class="answer">
                                <p>هدفي الأساسي هو العمل كمطور ويب في إحدى الشركات التقنية أو بدء مشروعي الخاص في مجال تطوير المواقع الإلكترونية. أريد أن أصبح خبيراً في تقنيات الويب الحديثة وأن أساهم في تطوير حلول تقنية مبتكرة. كما أطمح لمساعدة الشركات المحلية في تطوير حضورها الرقمي من خلال مواقع ويب احترافية.</p>
                            </div>
                        </div>

                        <!-- السؤال الرابع -->
                        <div class="answer-item">
                            <div class="question">
                                <h3>
                                    <i class="fas fa-question-circle"></i>
                                    كم من الوقت يمكنك تخصيصه أسبوعياً للدورة؟
                                </h3>
                            </div>
                            <div class="answer">
                                <p>يمكنني تخصيص 15-20 ساعة أسبوعياً للدورة. لدي مرونة في الوقت حيث أنني أعمل بدوام جزئي، مما يتيح لي التركيز على الدراسة والتطبيق العملي. أخطط لتخصيص 3-4 ساعات يومياً للدورة، بما في ذلك حضور المحاضرات وإنجاز المشاريع والواجبات.</p>
                            </div>
                        </div>

                        <!-- السؤال الخامس -->
                        <div class="answer-item">
                            <div class="question">
                                <h3>
                                    <i class="fas fa-question-circle"></i>
                                    هل لديك أي مشاريع سابقة يمكنك مشاركتها؟
                                </h3>
                            </div>
                            <div class="answer">
                                <p>نعم، قمت بتطوير موقع ويب بسيط لمحل تجاري محلي باستخدام HTML و CSS و JavaScript الأساسي. كما طورت تطبيق إدارة مكتبة بسيط باستخدام Java كمشروع تخرج. هذه المشاريع متاحة على GitHub الخاص بي، ويمكنني مشاركة الروابط عند الحاجة. أعتبر هذه المشاريع نقطة انطلاق وأتطلع لتطوير مشاريع أكثر تعقيداً واحترافية.</p>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="final-actions">
                        <button class="btn btn-success btn-large" onclick="approveStudent()">
                            <i class="fas fa-check"></i>
                            قبول الطالب
                        </button>
                        <button class="btn btn-danger btn-large" onclick="rejectStudent()">
                            <i class="fas fa-times"></i>
                            رفض الطالب
                        </button>
                        <button class="btn btn-info btn-large" onclick="requestMoreInfo()">
                            <i class="fas fa-question-circle"></i>
                            طلب معلومات إضافية
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="admin-dashboard.js"></script>
    <script>
        function goBack() {
            window.history.back();
        }
        
        function approveStudent() {
            if(confirm('هل أنت متأكد من قبول هذا الطالب؟')) {
                alert('تم قبول الطالب بنجاح!');
                // هنا يمكن إضافة كود لإرسال الطلب للخادم
            }
        }
        
        function rejectStudent() {
            if(confirm('هل أنت متأكد من رفض هذا الطالب؟')) {
                alert('تم رفض الطالب!');
                // هنا يمكن إضافة كود لإرسال الطلب للخادم
            }
        }
        
        function requestMoreInfo() {
            alert('تم إرسال طلب للحصول على معلومات إضافية');
            // هنا يمكن إضافة كود لإرسال الطلب للخادم
        }
    </script>
</body>
</html>

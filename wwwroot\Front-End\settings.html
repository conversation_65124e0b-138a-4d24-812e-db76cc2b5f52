<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - رؤية 2030</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .settings-container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .settings-header {
            background: linear-gradient(135deg, var(--primary-color), #ff6b35);
            color: white;
            padding: 3rem 2rem;
            border-radius: 20px;
            text-align: center;
            margin-bottom: 3rem;
            position: relative;
            overflow: hidden;
        }
        
        .settings-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="0.5" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
        }
        
        .settings-header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }
        
        .settings-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .settings-tabs {
            display: flex;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .tab-button {
            flex: 1;
            padding: 1.5rem 2rem;
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--secondary-color);
            transition: all 0.3s ease;
            position: relative;
        }
        
        .tab-button.active {
            background: linear-gradient(135deg, var(--primary-color), #ff6b35);
            color: white;
        }
        
        .tab-button:hover:not(.active) {
            background: rgba(255, 87, 34, 0.1);
        }
        
        .tab-content {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 2.5rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-section {
            margin-bottom: 2.5rem;
        }
        
        .form-section h3 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--secondary-color);
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-family: 'Tajawal', sans-serif;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
        }
        
        .save-btn {
            background: linear-gradient(135deg, var(--primary-color), #ff6b35);
            color: white;
            padding: 1rem 2.5rem;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 87, 34, 0.3);
        }
        
        .danger-zone {
            background: linear-gradient(135deg, #ffebee, #fce4ec);
            border: 1px solid #f8bbd9;
            border-radius: 15px;
            padding: 2rem;
            margin-top: 3rem;
        }
        
        .danger-zone h3 {
            color: #d32f2f;
            margin-bottom: 1rem;
        }
        
        .delete-btn {
            background: linear-gradient(135deg, #d32f2f, #c62828);
            color: white;
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .delete-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(211, 47, 47, 0.3);
        }
        
        .profile-picture {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .profile-img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid var(--primary-color);
            margin-bottom: 1rem;
            object-fit: cover;
        }
        
        .upload-btn {
            background: rgba(255, 87, 34, 0.1);
            color: var(--primary-color);
            padding: 0.5rem 1.5rem;
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .upload-btn:hover {
            background: var(--primary-color);
            color: white;
        }
        
        .notification-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 1rem;
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: var(--primary-color);
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <!-- ناف بار -->
    <nav class="navbar">
        <div class="logo">
            <img src="logo.svg" alt="رؤية 2030">
        </div>
        <div class="nav-links">
            <a href="index.html">الصفحة الرئيسية</a>
            <a href="courses.html">الدورات</a>
            <a href="my-courses.html">دوراتي</a>
            <a href="settings.html" class="active">الإعدادات</a>
            <a href="about.html">نبذة عنا</a>
        </div>
        <div class="auth-buttons">
            <a href="login.html" class="login-btn">تسجيل دخول</a>
        </div>
    </nav>

    <div class="settings-container">
        <!-- رأس الصفحة -->
        <div class="settings-header">
            <h1><i class="fas fa-cog"></i> إعدادات الحساب</h1>
            <p>إدارة معلوماتك الشخصية وإعدادات الحساب</p>
        </div>

        <!-- قائمة التبويبات -->
        <div class="tabs">
            <button class="tab-btn active" onclick="openTab(event, 'profile')">
                <i class="fas fa-user"></i> الملف الشخصي
            </button>
            <button class="tab-btn" onclick="openTab(event, 'security')">
                <i class="fas fa-shield-alt"></i> الأمان
            </button>
            <button class="tab-btn" onclick="openTab(event, 'notifications')">
                <i class="fas fa-bell"></i> الإشعارات
            </button>
            <button class="tab-btn" onclick="openTab(event, 'preferences')">
                <i class="fas fa-cog"></i> التفضيلات
            </button>
        </div>

        <!-- محتوى التبويبات -->
        <!-- تبويب الملف الشخصي -->
        <div id="profile" class="tab-content active">
            <div class="profile-picture">
                <img src="https://via.placeholder.com/120" alt="صورة الملف الشخصي" class="profile-img">
                <br>
                <button class="upload-btn">
                    <i class="fas fa-camera"></i> تغيير الصورة
                </button>
            </div>

            <form>
                <div class="form-section">
                    <h3><i class="fas fa-user"></i> المعلومات الأساسية</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">الاسم الأول</label>
                            <input type="text" id="firstName" value="أحمد">
                        </div>
                        <div class="form-group">
                            <label for="lastName">اسم العائلة</label>
                            <input type="text" id="lastName" value="محمد">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">البريد الإلكتروني</label>
                            <input type="email" id="email" value="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label for="phone">رقم الهاتف</label>
                            <input type="tel" id="phone" value="+218912345678">
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3><i class="fas fa-map-marker-alt"></i> معلومات الموقع</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="city">المدينة</label>
                            <select id="city">
                                <option value="benghazi" selected>بنغازي</option>
                                <option value="tripoli">طرابلس</option>
                                <option value="misrata">مصراتة</option>
                                <option value="zawiya">الزاوية</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="age">العمر</label>
                            <input type="number" id="age" value="25" min="16" max="65">
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3><i class="fas fa-graduation-cap"></i> المعلومات التعليمية والمهنية</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="education">المستوى التعليمي</label>
                            <select id="education">
                                <option value="high_school">ثانوية عامة</option>
                                <option value="diploma">دبلوم</option>
                                <option value="bachelor" selected>بكالوريوس</option>
                                <option value="master">ماجستير</option>
                                <option value="phd">دكتوراه</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="profession">المهنة/التخصص</label>
                            <input type="text" id="profession" value="مطور ويب">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="bio">نبذة عنك</label>
                        <textarea id="bio" rows="4" placeholder="اكتب نبذة مختصرة عنك...">مطور ويب متخصص في تقنيات الواجهة الأمامية</textarea>
                    </div>
                </div>

                <button type="submit" class="save-btn">
                    <i class="fas fa-save"></i> حفظ التغييرات
                </button>
            </form>
        </div>

        <!-- تبويب الحساب والأمان -->
        <div id="account" class="tab-content">
            <form>
                <div class="form-section">
                    <h3><i class="fas fa-lock"></i> تغيير كلمة المرور</h3>
                    <div class="form-group">
                        <label for="currentPassword">كلمة المرور الحالية</label>
                        <input type="password" id="currentPassword">
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="newPassword">كلمة المرور الجديدة</label>
                            <input type="password" id="newPassword">
                        </div>
                        <div class="form-group">
                            <label for="confirmPassword">تأكيد كلمة المرور</label>
                            <input type="password" id="confirmPassword">
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3><i class="fas fa-shield-alt"></i> الأمان</h3>
                    <div class="notification-item">
                        <div>
                            <strong>المصادقة الثنائية</strong>
                            <p>إضافة طبقة حماية إضافية لحسابك</p>
                        </div>
                        <label class="switch">
                            <input type="checkbox">
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div class="notification-item">
                        <div>
                            <strong>تسجيل الدخول من أجهزة جديدة</strong>
                            <p>إرسال إشعار عند تسجيل الدخول من جهاز جديد</p>
                        </div>
                        <label class="switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <button type="submit" class="save-btn">
                    <i class="fas fa-save"></i> حفظ التغييرات
                </button>
            </form>
        </div>

        <!-- تبويب الإشعارات -->
        <div id="notifications" class="tab-content">
            <div class="form-section">
                <h3><i class="fas fa-bell"></i> إعدادات الإشعارات</h3>

                <div class="notification-item">
                    <div>
                        <strong>إشعارات الدورات الجديدة</strong>
                        <p>تلقي إشعارات عند إضافة دورات جديدة</p>
                    </div>
                    <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                </div>

                <div class="notification-item">
                    <div>
                        <strong>تذكيرات المواعيد</strong>
                        <p>تذكيرات بمواعيد الدورات والمحاضرات</p>
                    </div>
                    <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                </div>

                <div class="notification-item">
                    <div>
                        <strong>إشعارات التقييم</strong>
                        <p>إشعارات عند تقييم أدائك في الدورات</p>
                    </div>
                    <label class="switch">
                        <input type="checkbox">
                        <span class="slider"></span>
                    </label>
                </div>

                <div class="notification-item">
                    <div>
                        <strong>النشرة الإخبارية</strong>
                        <p>تلقي النشرة الإخبارية الأسبوعية</p>
                    </div>
                    <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>

            <button type="submit" class="save-btn">
                <i class="fas fa-save"></i> حفظ الإعدادات
            </button>
        </div>

        <!-- تبويب التفضيلات -->
        <div id="preferences" class="tab-content">
            <div class="form-section">
                <h3><i class="fas fa-palette"></i> تفضيلات العرض</h3>

                <div class="form-group">
                    <label for="language">اللغة</label>
                    <select id="language">
                        <option value="ar" selected>العربية</option>
                        <option value="en">English</option>
                    </select>
                </div>

                <div class="notification-item">
                    <div>
                        <strong>الوضع الليلي</strong>
                        <p>تفعيل الوضع الليلي للموقع</p>
                    </div>
                    <label class="switch">
                        <input type="checkbox">
                        <span class="slider"></span>
                    </label>
                </div>

                <div class="form-group">
                    <label for="timezone">المنطقة الزمنية</label>
                    <select id="timezone">
                        <option value="africa/tripoli" selected>ليبيا (GMT+2)</option>
                        <option value="africa/cairo">مصر (GMT+2)</option>
                        <option value="asia/riyadh">السعودية (GMT+3)</option>
                    </select>
                </div>
            </div>

            <div class="form-section">
                <h3><i class="fas fa-graduation-cap"></i> تفضيلات التعلم</h3>

                <div class="form-group">
                    <label for="learningStyle">أسلوب التعلم المفضل</label>
                    <select id="learningStyle">
                        <option value="visual">بصري</option>
                        <option value="auditory">سمعي</option>
                        <option value="kinesthetic" selected>حركي</option>
                        <option value="mixed">مختلط</option>
                    </select>
                </div>

                <div class="notification-item">
                    <div>
                        <strong>التشغيل التلقائي للفيديوهات</strong>
                        <p>تشغيل الفيديوهات تلقائياً عند فتحها</p>
                    </div>
                    <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>

            <button type="submit" class="save-btn">
                <i class="fas fa-save"></i> حفظ التفضيلات
            </button>

            <!-- منطقة الخطر -->
            <div class="danger-zone">
                <h3><i class="fas fa-exclamation-triangle"></i> منطقة الخطر</h3>
                <p>هذه الإجراءات لا يمكن التراجع عنها. يرجى التأكد قبل المتابعة.</p>
                <button class="delete-btn" onclick="confirmDelete()">
                    <i class="fas fa-trash"></i> حذف الحساب نهائياً
                </button>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));

            // إزالة الفئة النشطة من جميع الأزرار
            const buttons = document.querySelectorAll('.tab-button');
            buttons.forEach(button => button.classList.remove('active'));

            // إظهار التبويب المحدد
            document.getElementById(tabName).classList.add('active');

            // إضافة الفئة النشطة للزر المحدد
            event.target.classList.add('active');
        }

        function confirmDelete() {
            if (confirm('هل أنت متأكد من رغبتك في حذف حسابك نهائياً؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                alert('تم إرسال طلب حذف الحساب. سيتم التواصل معك خلال 24 ساعة.');
            }
        }

        // تأثيرات إضافية
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثير للأزرار
            const buttons = document.querySelectorAll('.save-btn, .delete-btn, .upload-btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
            </div>
            <a href="login.html" class="login-btn">تسجيل دخول</a>
        </div>
    </nav>

    <div class="settings-container">
        <!-- رأس الصفحة -->
        <div class="settings-header">
            <h1><i class="fas fa-cog"></i> إعدادات الحساب</h1>
            <p>قم بتحديث معلوماتك الشخصية وإعدادات حسابك</p>
        </div>

        <!-- تبويبات الإعدادات -->
        <div class="settings-tabs">
            <button class="tab-button active" onclick="showTab('profile')">
                <i class="fas fa-user"></i> الملف الشخصي
            </button>
            <button class="tab-button" onclick="showTab('account')">
                <i class="fas fa-shield-alt"></i> الحساب والأمان
            </button>
            <button class="tab-button" onclick="showTab('notifications')">
                <i class="fas fa-bell"></i> الإشعارات
            </button>
            <button class="tab-button" onclick="showTab('preferences')">
                <i class="fas fa-palette"></i> التفضيلات
            </button>
        </div>

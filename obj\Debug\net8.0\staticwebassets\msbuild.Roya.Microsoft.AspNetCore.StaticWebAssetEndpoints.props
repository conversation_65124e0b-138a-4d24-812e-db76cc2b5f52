﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/Roya/assets/demo/chart-area-demo.8flq4k35dk.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\demo\chart-area-demo.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"8flq4k35dk"},{"Name":"integrity","Value":"sha256-9LLP7XKqyptnrNbmQqIjm5rIDw5zwE9KWE4hVhK5Jvk="},{"Name":"label","Value":"_content/Roya/assets/demo/chart-area-demo.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1530"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00229LLP7XKqyptnrNbmQqIjm5rIDw5zwE9KWE4hVhK5Jvk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 21 May 2025 22:55:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/assets/demo/chart-area-demo.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\demo\chart-area-demo.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9LLP7XKqyptnrNbmQqIjm5rIDw5zwE9KWE4hVhK5Jvk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1530"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00229LLP7XKqyptnrNbmQqIjm5rIDw5zwE9KWE4hVhK5Jvk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 21 May 2025 22:55:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/assets/demo/chart-bar-demo.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\demo\chart-bar-demo.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9tpvm3R5RdFWxUDIA09CWwnWxpLxiJYWKom8gdLwl3I="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1112"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00229tpvm3R5RdFWxUDIA09CWwnWxpLxiJYWKom8gdLwl3I=\u0022"},{"Name":"Last-Modified","Value":"Wed, 21 May 2025 22:55:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/assets/demo/chart-bar-demo.qbt6h4ox5y.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\demo\chart-bar-demo.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qbt6h4ox5y"},{"Name":"integrity","Value":"sha256-9tpvm3R5RdFWxUDIA09CWwnWxpLxiJYWKom8gdLwl3I="},{"Name":"label","Value":"_content/Roya/assets/demo/chart-bar-demo.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1112"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00229tpvm3R5RdFWxUDIA09CWwnWxpLxiJYWKom8gdLwl3I=\u0022"},{"Name":"Last-Modified","Value":"Wed, 21 May 2025 22:55:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/assets/demo/chart-pie-demo.hw7eaup9v4.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\demo\chart-pie-demo.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hw7eaup9v4"},{"Name":"integrity","Value":"sha256-gXHh8OZnGFZ8Pb\u002BCoSlwJ7lx7tZD1amOLrSASwI8Neo="},{"Name":"label","Value":"_content/Roya/assets/demo/chart-pie-demo.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"597"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022gXHh8OZnGFZ8Pb\u002BCoSlwJ7lx7tZD1amOLrSASwI8Neo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 21 May 2025 22:55:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/assets/demo/chart-pie-demo.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\demo\chart-pie-demo.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gXHh8OZnGFZ8Pb\u002BCoSlwJ7lx7tZD1amOLrSASwI8Neo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"597"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022gXHh8OZnGFZ8Pb\u002BCoSlwJ7lx7tZD1amOLrSASwI8Neo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 21 May 2025 22:55:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/assets/demo/datatables-demo.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\demo\datatables-demo.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-3BYZ/ie9x2nIWSAO1N9M0VISPR5FfP5Wg0EKHZzNOoo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"103"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00223BYZ/ie9x2nIWSAO1N9M0VISPR5FfP5Wg0EKHZzNOoo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 21 May 2025 22:55:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/assets/demo/datatables-demo.w9gruc5uo1.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\demo\datatables-demo.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"w9gruc5uo1"},{"Name":"integrity","Value":"sha256-3BYZ/ie9x2nIWSAO1N9M0VISPR5FfP5Wg0EKHZzNOoo="},{"Name":"label","Value":"_content/Roya/assets/demo/datatables-demo.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"103"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00223BYZ/ie9x2nIWSAO1N9M0VISPR5FfP5Wg0EKHZzNOoo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 21 May 2025 22:55:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/assets/img/error-404-monochrome.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\img\error-404-monochrome.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GuryUo0\u002BxyzRuEVttjuLmS8hqxoE9FucfaX/mboMWj8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"6119"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022GuryUo0\u002BxyzRuEVttjuLmS8hqxoE9FucfaX/mboMWj8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 21 May 2025 22:55:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/assets/img/error-404-monochrome.yp87i3fbss.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\assets\img\error-404-monochrome.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"yp87i3fbss"},{"Name":"integrity","Value":"sha256-GuryUo0\u002BxyzRuEVttjuLmS8hqxoE9FucfaX/mboMWj8="},{"Name":"label","Value":"_content/Roya/assets/img/error-404-monochrome.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6119"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022GuryUo0\u002BxyzRuEVttjuLmS8hqxoE9FucfaX/mboMWj8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 21 May 2025 22:55:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/css/styles.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\styles.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Tl/JSPdgPPXWVWSUewZbpR6kZW1nVf8f6PA1SpwxpFo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"250226"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Tl/JSPdgPPXWVWSUewZbpR6kZW1nVf8f6PA1SpwxpFo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 21 May 2025 22:55:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/css/styles.mf45fbw2f4.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\styles.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mf45fbw2f4"},{"Name":"integrity","Value":"sha256-Tl/JSPdgPPXWVWSUewZbpR6kZW1nVf8f6PA1SpwxpFo="},{"Name":"label","Value":"_content/Roya/css/styles.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"250226"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Tl/JSPdgPPXWVWSUewZbpR6kZW1nVf8f6PA1SpwxpFo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 21 May 2025 22:55:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/animate.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\animate.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-pr1RD8sKPn4nSCTIJyIjotnWZM5mNFWfGCAPn8C7Q3E="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"77758"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022pr1RD8sKPn4nSCTIJyIjotnWZM5mNFWfGCAPn8C7Q3E=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/animate.uyw73ukb29.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\animate.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uyw73ukb29"},{"Name":"integrity","Value":"sha256-pr1RD8sKPn4nSCTIJyIjotnWZM5mNFWfGCAPn8C7Q3E="},{"Name":"label","Value":"_content/Roya/cssvist/animate.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"77758"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022pr1RD8sKPn4nSCTIJyIjotnWZM5mNFWfGCAPn8C7Q3E=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/aos.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\aos.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GqiEX9BuR1rv5zPU5Vs2qS/NSHl1BJyBcjQYJ6ycwD4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"26053"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022GqiEX9BuR1rv5zPU5Vs2qS/NSHl1BJyBcjQYJ6ycwD4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/aos.euiikl6nsc.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\aos.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"euiikl6nsc"},{"Name":"integrity","Value":"sha256-GqiEX9BuR1rv5zPU5Vs2qS/NSHl1BJyBcjQYJ6ycwD4="},{"Name":"label","Value":"_content/Roya/cssvist/aos.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"26053"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022GqiEX9BuR1rv5zPU5Vs2qS/NSHl1BJyBcjQYJ6ycwD4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/bootstrap.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\bootstrap.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YLGeXaapI0/5IgZopewRJcFXomhRMlYYjugPLSyNjTY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"155758"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022YLGeXaapI0/5IgZopewRJcFXomhRMlYYjugPLSyNjTY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/bootstrap.min.o2wlpouf80.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\bootstrap.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"o2wlpouf80"},{"Name":"integrity","Value":"sha256-YLGeXaapI0/5IgZopewRJcFXomhRMlYYjugPLSyNjTY="},{"Name":"label","Value":"_content/Roya/cssvist/bootstrap.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"155758"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022YLGeXaapI0/5IgZopewRJcFXomhRMlYYjugPLSyNjTY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/flaticon.0r9ch1ruzz.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\flaticon.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0r9ch1ruzz"},{"Name":"integrity","Value":"sha256-XEE4DjfBrGdCnwfqXLIJxbV7Ky/ZCSX5jRSB6mXD4IU="},{"Name":"label","Value":"_content/Roya/cssvist/flaticon.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"971"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022XEE4DjfBrGdCnwfqXLIJxbV7Ky/ZCSX5jRSB6mXD4IU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/flaticon.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\flaticon.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-XEE4DjfBrGdCnwfqXLIJxbV7Ky/ZCSX5jRSB6mXD4IU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"971"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022XEE4DjfBrGdCnwfqXLIJxbV7Ky/ZCSX5jRSB6mXD4IU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/font-awesome.min.5y92vvq571.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\font-awesome.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5y92vvq571"},{"Name":"integrity","Value":"sha256-3dkvEK0WLHRJ7/Csr0BZjAWxERc5WH7bdeUya2aXxdU="},{"Name":"label","Value":"_content/Roya/cssvist/font-awesome.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"27466"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00223dkvEK0WLHRJ7/Csr0BZjAWxERc5WH7bdeUya2aXxdU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/font-awesome.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\font-awesome.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-3dkvEK0WLHRJ7/Csr0BZjAWxERc5WH7bdeUya2aXxdU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"27466"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00223dkvEK0WLHRJ7/Csr0BZjAWxERc5WH7bdeUya2aXxdU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/magnific-popup.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\magnific-popup.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PZLhE6wwMbg4AB3d35ZdBF9HD/dI/y4RazA3iRDurss="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5259"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022PZLhE6wwMbg4AB3d35ZdBF9HD/dI/y4RazA3iRDurss=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/magnific-popup.lmxkrc82ez.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\magnific-popup.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lmxkrc82ez"},{"Name":"integrity","Value":"sha256-PZLhE6wwMbg4AB3d35ZdBF9HD/dI/y4RazA3iRDurss="},{"Name":"label","Value":"_content/Roya/cssvist/magnific-popup.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5259"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022PZLhE6wwMbg4AB3d35ZdBF9HD/dI/y4RazA3iRDurss=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/nice-select.312ei9lmb1.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\nice-select.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"312ei9lmb1"},{"Name":"integrity","Value":"sha256-ORgUoRSNfCn0XJ/UKMiLq8qhWiPLgLNSfeTY0Vt14Oo="},{"Name":"label","Value":"_content/Roya/cssvist/nice-select.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4009"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022ORgUoRSNfCn0XJ/UKMiLq8qhWiPLgLNSfeTY0Vt14Oo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/nice-select.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\nice-select.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ORgUoRSNfCn0XJ/UKMiLq8qhWiPLgLNSfeTY0Vt14Oo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4009"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022ORgUoRSNfCn0XJ/UKMiLq8qhWiPLgLNSfeTY0Vt14Oo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/owl.carousel.min.a28q16vo32.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\owl.carousel.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"a28q16vo32"},{"Name":"integrity","Value":"sha256-UhQQ4fxEeABh4JrcmAJ1\u002B16id/1dnlOEVCFOxDef9Lw="},{"Name":"label","Value":"_content/Roya/cssvist/owl.carousel.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3351"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022UhQQ4fxEeABh4JrcmAJ1\u002B16id/1dnlOEVCFOxDef9Lw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/owl.carousel.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\owl.carousel.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-UhQQ4fxEeABh4JrcmAJ1\u002B16id/1dnlOEVCFOxDef9Lw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3351"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022UhQQ4fxEeABh4JrcmAJ1\u002B16id/1dnlOEVCFOxDef9Lw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/slick.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\slick.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-bxc/veiXxLXkA8kdmb/G1nHv6nmUUMo7EcDRvM4t38E="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1718"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022bxc/veiXxLXkA8kdmb/G1nHv6nmUUMo7EcDRvM4t38E=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/slick.d5xbcs760j.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\slick.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"d5xbcs760j"},{"Name":"integrity","Value":"sha256-bxc/veiXxLXkA8kdmb/G1nHv6nmUUMo7EcDRvM4t38E="},{"Name":"label","Value":"_content/Roya/cssvist/slick.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1718"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022bxc/veiXxLXkA8kdmb/G1nHv6nmUUMo7EcDRvM4t38E=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/style.4tyddat3gg.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\style.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4tyddat3gg"},{"Name":"integrity","Value":"sha256-eBaxanpQHgBV2AWXfX4f4gvUp99wOD/rQX6e1ACGAw0="},{"Name":"label","Value":"_content/Roya/cssvist/style.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"260270"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022eBaxanpQHgBV2AWXfX4f4gvUp99wOD/rQX6e1ACGAw0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/style.7hqsrj2vl4.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\style.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7hqsrj2vl4"},{"Name":"integrity","Value":"sha256-9YMgjgWjHc/h4czS2twksEPjChfjjxc0KTuNVMC47fA="},{"Name":"label","Value":"_content/Roya/cssvist/style.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"204090"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00229YMgjgWjHc/h4czS2twksEPjChfjjxc0KTuNVMC47fA=\u0022"},{"Name":"Last-Modified","Value":"Fri, 13 Jun 2025 04:28:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/style.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\style.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9YMgjgWjHc/h4czS2twksEPjChfjjxc0KTuNVMC47fA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"204090"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00229YMgjgWjHc/h4czS2twksEPjChfjjxc0KTuNVMC47fA=\u0022"},{"Name":"Last-Modified","Value":"Fri, 13 Jun 2025 04:28:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/style.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\style.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-eBaxanpQHgBV2AWXfX4f4gvUp99wOD/rQX6e1ACGAw0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"260270"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022eBaxanpQHgBV2AWXfX4f4gvUp99wOD/rQX6e1ACGAw0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/swiper.min.0ju6xm52ff.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\swiper.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0ju6xm52ff"},{"Name":"integrity","Value":"sha256-9HZgbIIf0jug/K4YRePkWuOfYECSHeLZZpitfR6SLz4="},{"Name":"label","Value":"_content/Roya/cssvist/swiper.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"19773"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00229HZgbIIf0jug/K4YRePkWuOfYECSHeLZZpitfR6SLz4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/swiper.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\swiper.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9HZgbIIf0jug/K4YRePkWuOfYECSHeLZZpitfR6SLz4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"19773"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00229HZgbIIf0jug/K4YRePkWuOfYECSHeLZZpitfR6SLz4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/themify-icons.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\themify-icons.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CKCvnwNRYXK7PY0x7rtkUQ8ee6hIgcnZn5gJoouUN08="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"16450"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022CKCvnwNRYXK7PY0x7rtkUQ8ee6hIgcnZn5gJoouUN08=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/cssvist/themify-icons.snezv7zscm.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\cssvist\themify-icons.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"snezv7zscm"},{"Name":"integrity","Value":"sha256-CKCvnwNRYXK7PY0x7rtkUQ8ee6hIgcnZn5gJoouUN08="},{"Name":"label","Value":"_content/Roya/cssvist/themify-icons.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16450"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022CKCvnwNRYXK7PY0x7rtkUQ8ee6hIgcnZn5gJoouUN08=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/favicon.61n19gt1b8.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\favicon.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"61n19gt1b8"},{"Name":"integrity","Value":"sha256-Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="},{"Name":"label","Value":"_content/Roya/favicon.ico"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5430"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/favicon.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\favicon.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5430"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/Flaticon.5hl3cx5jxx.woff">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\Flaticon.woff'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5hl3cx5jxx"},{"Name":"integrity","Value":"sha256-eU9EqnvfgpIl4rpzlMoRmfNtk3ZsnY7lr\u002BDwgxfiSYs="},{"Name":"label","Value":"_content/Roya/fonts/Flaticon.woff"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1476"},{"Name":"Content-Type","Value":"application/font-woff"},{"Name":"ETag","Value":"\u0022eU9EqnvfgpIl4rpzlMoRmfNtk3ZsnY7lr\u002BDwgxfiSYs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/Flaticon.e3otbib52z.eot">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\Flaticon.eot'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"e3otbib52z"},{"Name":"integrity","Value":"sha256-OoL51QDNx\u002BYR4iLqCc69pdeiQecCeVth3q9DLVaDuwE="},{"Name":"label","Value":"_content/Roya/fonts/Flaticon.eot"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2226"},{"Name":"Content-Type","Value":"application/vnd.ms-fontobject"},{"Name":"ETag","Value":"\u0022OoL51QDNx\u002BYR4iLqCc69pdeiQecCeVth3q9DLVaDuwE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/Flaticon.eot">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\Flaticon.eot'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-OoL51QDNx\u002BYR4iLqCc69pdeiQecCeVth3q9DLVaDuwE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2226"},{"Name":"Content-Type","Value":"application/vnd.ms-fontobject"},{"Name":"ETag","Value":"\u0022OoL51QDNx\u002BYR4iLqCc69pdeiQecCeVth3q9DLVaDuwE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/Flaticon.jx6wzoddy9.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\Flaticon.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jx6wzoddy9"},{"Name":"integrity","Value":"sha256-6aGlWLMWMpmGwGw3EG10oHr//6sKoYAPUhPziXLujtc="},{"Name":"label","Value":"_content/Roya/fonts/Flaticon.ttf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2048"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u00226aGlWLMWMpmGwGw3EG10oHr//6sKoYAPUhPziXLujtc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/Flaticon.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\Flaticon.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-6aGlWLMWMpmGwGw3EG10oHr//6sKoYAPUhPziXLujtc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2048"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u00226aGlWLMWMpmGwGw3EG10oHr//6sKoYAPUhPziXLujtc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/Flaticon.wlwxyn7ul8.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\Flaticon.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wlwxyn7ul8"},{"Name":"integrity","Value":"sha256-rM6xbBBiKLGILQ8ldme\u002BcmnrYabiM7ZHNRWvdtyirCA="},{"Name":"label","Value":"_content/Roya/fonts/Flaticon.woff2"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1020"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022rM6xbBBiKLGILQ8ldme\u002BcmnrYabiM7ZHNRWvdtyirCA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/Flaticon.woff">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\Flaticon.woff'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-eU9EqnvfgpIl4rpzlMoRmfNtk3ZsnY7lr\u002BDwgxfiSYs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1476"},{"Name":"Content-Type","Value":"application/font-woff"},{"Name":"ETag","Value":"\u0022eU9EqnvfgpIl4rpzlMoRmfNtk3ZsnY7lr\u002BDwgxfiSYs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/Flaticon.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\Flaticon.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-rM6xbBBiKLGILQ8ldme\u002BcmnrYabiM7ZHNRWvdtyirCA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1020"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022rM6xbBBiKLGILQ8ldme\u002BcmnrYabiM7ZHNRWvdtyirCA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/fontawesome-webfont.67h1lttds5.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\fontawesome-webfont.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"67h1lttds5"},{"Name":"integrity","Value":"sha256-rhni5MBPKwS/AwaExMHbj69cj\u002BPuA9HgxAkEZgiziRI="},{"Name":"label","Value":"_content/Roya/fonts/fontawesome-webfont.ttf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"152796"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022rhni5MBPKwS/AwaExMHbj69cj\u002BPuA9HgxAkEZgiziRI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/fontawesome-webfont.8y2jz3soc4.eot">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\fontawesome-webfont.eot'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"8y2jz3soc4"},{"Name":"integrity","Value":"sha256-ULvpGSaX55Hi7k73OReusbA\u002Bcn3/CKH8jXTwDkqoEuE="},{"Name":"label","Value":"_content/Roya/fonts/fontawesome-webfont.eot"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"76518"},{"Name":"Content-Type","Value":"application/vnd.ms-fontobject"},{"Name":"ETag","Value":"\u0022ULvpGSaX55Hi7k73OReusbA\u002Bcn3/CKH8jXTwDkqoEuE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/fontawesome-webfont.91cvyzezk9.woff">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\fontawesome-webfont.woff'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"91cvyzezk9"},{"Name":"integrity","Value":"sha256-rbxPlettfyc4lZzw7Lw3RnL85H6FYFCo6XkfRXYjrCw="},{"Name":"label","Value":"_content/Roya/fonts/fontawesome-webfont.woff"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"90412"},{"Name":"Content-Type","Value":"application/font-woff"},{"Name":"ETag","Value":"\u0022rbxPlettfyc4lZzw7Lw3RnL85H6FYFCo6XkfRXYjrCw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/fontawesome-webfont.b62mvgybyh.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\fontawesome-webfont.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"b62mvgybyh"},{"Name":"integrity","Value":"sha256-faz4P1EXnejXmApRPmerOgjyxicrtZRt\u002BP13wNF2O3M="},{"Name":"label","Value":"_content/Roya/fonts/fontawesome-webfont.woff2"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"71896"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022faz4P1EXnejXmApRPmerOgjyxicrtZRt\u002BP13wNF2O3M=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/fontawesome-webfont.eot">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\fontawesome-webfont.eot'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ULvpGSaX55Hi7k73OReusbA\u002Bcn3/CKH8jXTwDkqoEuE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"76518"},{"Name":"Content-Type","Value":"application/vnd.ms-fontobject"},{"Name":"ETag","Value":"\u0022ULvpGSaX55Hi7k73OReusbA\u002Bcn3/CKH8jXTwDkqoEuE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/fontawesome-webfont.i021en4tkg.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\fontawesome-webfont.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"i021en4tkg"},{"Name":"integrity","Value":"sha256-jjWGOJu0zQGz\u002BFuztiJzm95mJ/KLumOgIMIjypzxua4="},{"Name":"label","Value":"_content/Roya/fonts/fontawesome-webfont.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"391622"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022jjWGOJu0zQGz\u002BFuztiJzm95mJ/KLumOgIMIjypzxua4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/fontawesome-webfont.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\fontawesome-webfont.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-jjWGOJu0zQGz\u002BFuztiJzm95mJ/KLumOgIMIjypzxua4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"391622"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022jjWGOJu0zQGz\u002BFuztiJzm95mJ/KLumOgIMIjypzxua4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/fontawesome-webfont.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\fontawesome-webfont.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-rhni5MBPKwS/AwaExMHbj69cj\u002BPuA9HgxAkEZgiziRI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"152796"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022rhni5MBPKwS/AwaExMHbj69cj\u002BPuA9HgxAkEZgiziRI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/fontawesome-webfont.woff">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\fontawesome-webfont.woff'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-rbxPlettfyc4lZzw7Lw3RnL85H6FYFCo6XkfRXYjrCw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"90412"},{"Name":"Content-Type","Value":"application/font-woff"},{"Name":"ETag","Value":"\u0022rbxPlettfyc4lZzw7Lw3RnL85H6FYFCo6XkfRXYjrCw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/fontawesome-webfont.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\fontawesome-webfont.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-faz4P1EXnejXmApRPmerOgjyxicrtZRt\u002BP13wNF2O3M="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"71896"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u0022faz4P1EXnejXmApRPmerOgjyxicrtZRt\u002BP13wNF2O3M=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/FontAwesome.81c8p4mxph.otf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\FontAwesome.otf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"81c8p4mxph"},{"Name":"integrity","Value":"sha256-7NcvMZEKjuJyb9F71Fm\u002BJvIwd58/PtX2nr\u002BCnksS52g="},{"Name":"label","Value":"_content/Roya/fonts/FontAwesome.otf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"124988"},{"Name":"Content-Type","Value":"font/otf"},{"Name":"ETag","Value":"\u00227NcvMZEKjuJyb9F71Fm\u002BJvIwd58/PtX2nr\u002BCnksS52g=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/FontAwesome.otf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\FontAwesome.otf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-7NcvMZEKjuJyb9F71Fm\u002BJvIwd58/PtX2nr\u002BCnksS52g="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"124988"},{"Name":"Content-Type","Value":"font/otf"},{"Name":"ETag","Value":"\u00227NcvMZEKjuJyb9F71Fm\u002BJvIwd58/PtX2nr\u002BCnksS52g=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/themify.5kueo723u4.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\themify.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5kueo723u4"},{"Name":"integrity","Value":"sha256-NQZjpGZeAAcsaKh60/oL5HuKkUJBJ/Xz4J9mQZcpXwE="},{"Name":"label","Value":"_content/Roya/fonts/themify.ttf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"78584"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022NQZjpGZeAAcsaKh60/oL5HuKkUJBJ/Xz4J9mQZcpXwE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/themify.eot">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\themify.eot'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-3/QV2uyRG2Xcpb4CBxoYJbdVCP8VjeW42Fl2lX25Mcs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"78748"},{"Name":"Content-Type","Value":"application/vnd.ms-fontobject"},{"Name":"ETag","Value":"\u00223/QV2uyRG2Xcpb4CBxoYJbdVCP8VjeW42Fl2lX25Mcs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/themify.fm5yj345o5.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\themify.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fm5yj345o5"},{"Name":"integrity","Value":"sha256-968uCWyFwu1vaLsIb3ksZ/KmBBy7gU/CeWkSsu70/nY="},{"Name":"label","Value":"_content/Roya/fonts/themify.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"234269"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022968uCWyFwu1vaLsIb3ksZ/KmBBy7gU/CeWkSsu70/nY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/themify.nvkla53trw.woff">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\themify.woff'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"nvkla53trw"},{"Name":"integrity","Value":"sha256-DbXFoUdet6PlAomD6h5kLRssAPr/aiUKN1ArDzgypKc="},{"Name":"label","Value":"_content/Roya/fonts/themify.woff"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"56108"},{"Name":"Content-Type","Value":"application/font-woff"},{"Name":"ETag","Value":"\u0022DbXFoUdet6PlAomD6h5kLRssAPr/aiUKN1ArDzgypKc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/themify.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\themify.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-968uCWyFwu1vaLsIb3ksZ/KmBBy7gU/CeWkSsu70/nY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"234269"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022968uCWyFwu1vaLsIb3ksZ/KmBBy7gU/CeWkSsu70/nY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/themify.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\themify.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NQZjpGZeAAcsaKh60/oL5HuKkUJBJ/Xz4J9mQZcpXwE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"78584"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022NQZjpGZeAAcsaKh60/oL5HuKkUJBJ/Xz4J9mQZcpXwE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/themify.woff">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\themify.woff'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-DbXFoUdet6PlAomD6h5kLRssAPr/aiUKN1ArDzgypKc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"56108"},{"Name":"Content-Type","Value":"application/font-woff"},{"Name":"ETag","Value":"\u0022DbXFoUdet6PlAomD6h5kLRssAPr/aiUKN1ArDzgypKc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/fonts/themify.xoyi32mgbc.eot">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\fonts\themify.eot'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"xoyi32mgbc"},{"Name":"integrity","Value":"sha256-3/QV2uyRG2Xcpb4CBxoYJbdVCP8VjeW42Fl2lX25Mcs="},{"Name":"label","Value":"_content/Roya/fonts/themify.eot"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"78748"},{"Name":"Content-Type","Value":"application/vnd.ms-fontobject"},{"Name":"ETag","Value":"\u00223/QV2uyRG2Xcpb4CBxoYJbdVCP8VjeW42Fl2lX25Mcs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/about_overlay.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\about_overlay.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0btWG0vyzr\u002Bb\u002Be5/Mxeq95KnDJ3nucFG1VgwJPYi3Q4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"9235"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00220btWG0vyzr\u002Bb\u002Be5/Mxeq95KnDJ3nucFG1VgwJPYi3Q4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/about_overlay.rtg0r1xhb3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\about_overlay.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rtg0r1xhb3"},{"Name":"integrity","Value":"sha256-0btWG0vyzr\u002Bb\u002Be5/Mxeq95KnDJ3nucFG1VgwJPYi3Q4="},{"Name":"label","Value":"_content/Roya/img/about_overlay.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"9235"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00220btWG0vyzr\u002Bb\u002Be5/Mxeq95KnDJ3nucFG1VgwJPYi3Q4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/advance_feature_bg.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\advance_feature_bg.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-v3/ziDPUuH8usdAzqIydjU6nacAZucCnnvk8bF9WAdU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"11167"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022v3/ziDPUuH8usdAzqIydjU6nacAZucCnnvk8bF9WAdU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/advance_feature_bg.r5jjsn0t25.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\advance_feature_bg.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"r5jjsn0t25"},{"Name":"integrity","Value":"sha256-v3/ziDPUuH8usdAzqIydjU6nacAZucCnnvk8bF9WAdU="},{"Name":"label","Value":"_content/Roya/img/advance_feature_bg.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11167"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022v3/ziDPUuH8usdAzqIydjU6nacAZucCnnvk8bF9WAdU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/advance_feature_img.2t2qmofdyx.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\advance_feature_img.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2t2qmofdyx"},{"Name":"integrity","Value":"sha256-tva2TQLalEJCk2aE9qvp1blhIcE1JKnJF9dE1L7Ph6E="},{"Name":"label","Value":"_content/Roya/img/advance_feature_img.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"63751"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022tva2TQLalEJCk2aE9qvp1blhIcE1JKnJF9dE1L7Ph6E=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/advance_feature_img.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\advance_feature_img.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-tva2TQLalEJCk2aE9qvp1blhIcE1JKnJF9dE1L7Ph6E="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"63751"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022tva2TQLalEJCk2aE9qvp1blhIcE1JKnJF9dE1L7Ph6E=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/animate_icon/icon_1.4kyumy324r.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\animate_icon\icon_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4kyumy324r"},{"Name":"integrity","Value":"sha256-dFjNvXRTqEBJ79oDXmwh4\u002BGlW4ueIl8eBnlHIgK5JTw="},{"Name":"label","Value":"_content/Roya/img/animate_icon/icon_1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2949"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022dFjNvXRTqEBJ79oDXmwh4\u002BGlW4ueIl8eBnlHIgK5JTw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/animate_icon/icon_1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\animate_icon\icon_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dFjNvXRTqEBJ79oDXmwh4\u002BGlW4ueIl8eBnlHIgK5JTw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2949"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022dFjNvXRTqEBJ79oDXmwh4\u002BGlW4ueIl8eBnlHIgK5JTw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/animate_icon/icon_2.mwy7nsdum7.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\animate_icon\icon_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mwy7nsdum7"},{"Name":"integrity","Value":"sha256-dmM\u002BODtnDa8VNbF8PCn1PV1Rjv5qVe\u002BOx/hxal5\u002BdJ4="},{"Name":"label","Value":"_content/Roya/img/animate_icon/icon_2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1289"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022dmM\u002BODtnDa8VNbF8PCn1PV1Rjv5qVe\u002BOx/hxal5\u002BdJ4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/animate_icon/icon_2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\animate_icon\icon_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dmM\u002BODtnDa8VNbF8PCn1PV1Rjv5qVe\u002BOx/hxal5\u002BdJ4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1289"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022dmM\u002BODtnDa8VNbF8PCn1PV1Rjv5qVe\u002BOx/hxal5\u002BdJ4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/animate_icon/icon_3.gduucvqum4.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\animate_icon\icon_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gduucvqum4"},{"Name":"integrity","Value":"sha256-vIKT0xpYAbaeIu4CKe2uTtO1\u002BJIoxllXpKn7T4I2rjs="},{"Name":"label","Value":"_content/Roya/img/animate_icon/icon_3.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1547"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022vIKT0xpYAbaeIu4CKe2uTtO1\u002BJIoxllXpKn7T4I2rjs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/animate_icon/icon_3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\animate_icon\icon_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vIKT0xpYAbaeIu4CKe2uTtO1\u002BJIoxllXpKn7T4I2rjs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1547"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022vIKT0xpYAbaeIu4CKe2uTtO1\u002BJIoxllXpKn7T4I2rjs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/animate_icon/icon_4.ojkxe1p351.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\animate_icon\icon_4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ojkxe1p351"},{"Name":"integrity","Value":"sha256-RKi1jkUY75B1GmgS4AV5VmXrPjF4zIc6je\u002BsBoB2JFI="},{"Name":"label","Value":"_content/Roya/img/animate_icon/icon_4.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1242"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022RKi1jkUY75B1GmgS4AV5VmXrPjF4zIc6je\u002BsBoB2JFI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/animate_icon/icon_4.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\animate_icon\icon_4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-RKi1jkUY75B1GmgS4AV5VmXrPjF4zIc6je\u002BsBoB2JFI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1242"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022RKi1jkUY75B1GmgS4AV5VmXrPjF4zIc6je\u002BsBoB2JFI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/animate_icon/icon_5.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\animate_icon\icon_5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hwX6ZT3pSUg4T2q1AVL6M9XtQkG29JKvFbyzA2A4rLg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1439"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022hwX6ZT3pSUg4T2q1AVL6M9XtQkG29JKvFbyzA2A4rLg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/animate_icon/icon_5.z9mvf1vm0j.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\animate_icon\icon_5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z9mvf1vm0j"},{"Name":"integrity","Value":"sha256-hwX6ZT3pSUg4T2q1AVL6M9XtQkG29JKvFbyzA2A4rLg="},{"Name":"label","Value":"_content/Roya/img/animate_icon/icon_5.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1439"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022hwX6ZT3pSUg4T2q1AVL6M9XtQkG29JKvFbyzA2A4rLg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/animate_icon/icon_7.495lkd1k54.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\animate_icon\icon_7.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"495lkd1k54"},{"Name":"integrity","Value":"sha256-2AU7D/sK\u002BUZTxUe6CsNztPpSP2Hd/qe7jMHlGErrPJk="},{"Name":"label","Value":"_content/Roya/img/animate_icon/icon_7.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2474"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00222AU7D/sK\u002BUZTxUe6CsNztPpSP2Hd/qe7jMHlGErrPJk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/animate_icon/icon_7.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\animate_icon\icon_7.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-2AU7D/sK\u002BUZTxUe6CsNztPpSP2Hd/qe7jMHlGErrPJk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2474"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00222AU7D/sK\u002BUZTxUe6CsNztPpSP2Hd/qe7jMHlGErrPJk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/animate_icon/icon_8.aheplgtgvb.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\animate_icon\icon_8.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"aheplgtgvb"},{"Name":"integrity","Value":"sha256-cqXFUYyFhJubi\u002Bdk2MKIoIivXDCBCbEAEC7qo\u002BAZU3E="},{"Name":"label","Value":"_content/Roya/img/animate_icon/icon_8.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1266"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022cqXFUYyFhJubi\u002Bdk2MKIoIivXDCBCbEAEC7qo\u002BAZU3E=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/animate_icon/icon_8.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\animate_icon\icon_8.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-cqXFUYyFhJubi\u002Bdk2MKIoIivXDCBCbEAEC7qo\u002BAZU3E="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1266"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022cqXFUYyFhJubi\u002Bdk2MKIoIivXDCBCbEAEC7qo\u002BAZU3E=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/animate_icon/icon_9.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\animate_icon\icon_9.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-OK\u002BFUzdr8mcL9AYemF/piv31eQpkT5yWIoA7n0m5wxQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1596"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022OK\u002BFUzdr8mcL9AYemF/piv31eQpkT5yWIoA7n0m5wxQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/animate_icon/icon_9.wm4va7oxhx.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\animate_icon\icon_9.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wm4va7oxhx"},{"Name":"integrity","Value":"sha256-OK\u002BFUzdr8mcL9AYemF/piv31eQpkT5yWIoA7n0m5wxQ="},{"Name":"label","Value":"_content/Roya/img/animate_icon/icon_9.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1596"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022OK\u002BFUzdr8mcL9AYemF/piv31eQpkT5yWIoA7n0m5wxQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/author/author_1.2sjpubd8xb.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\author\author_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2sjpubd8xb"},{"Name":"integrity","Value":"sha256-nhUlxpNs/cLjVW2Y8Wqle7ELrX7y6wxPkYmnaXbWm6U="},{"Name":"label","Value":"_content/Roya/img/author/author_1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6901"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022nhUlxpNs/cLjVW2Y8Wqle7ELrX7y6wxPkYmnaXbWm6U=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/author/author_1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\author\author_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nhUlxpNs/cLjVW2Y8Wqle7ELrX7y6wxPkYmnaXbWm6U="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"6901"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022nhUlxpNs/cLjVW2Y8Wqle7ELrX7y6wxPkYmnaXbWm6U=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/author/author_2.dc4mm83bvy.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\author\author_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dc4mm83bvy"},{"Name":"integrity","Value":"sha256-g1o2WFnTpKa6JyS33qIA\u002B9hSN96OOq9wCqaQa60a7F0="},{"Name":"label","Value":"_content/Roya/img/author/author_2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6209"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022g1o2WFnTpKa6JyS33qIA\u002B9hSN96OOq9wCqaQa60a7F0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/author/author_2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\author\author_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-g1o2WFnTpKa6JyS33qIA\u002B9hSN96OOq9wCqaQa60a7F0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"6209"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022g1o2WFnTpKa6JyS33qIA\u002B9hSN96OOq9wCqaQa60a7F0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/author/author_3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\author\author_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Tpy62qhdBIRLYZ3MhzQE5LAxMA957ozD3wF1rwr985o="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"6321"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Tpy62qhdBIRLYZ3MhzQE5LAxMA957ozD3wF1rwr985o=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/author/author_3.uwbo69sga3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\author\author_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uwbo69sga3"},{"Name":"integrity","Value":"sha256-Tpy62qhdBIRLYZ3MhzQE5LAxMA957ozD3wF1rwr985o="},{"Name":"label","Value":"_content/Roya/img/author/author_3.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6321"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Tpy62qhdBIRLYZ3MhzQE5LAxMA957ozD3wF1rwr985o=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/banner_bg.e63tn8nrxl.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\banner_bg.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"e63tn8nrxl"},{"Name":"integrity","Value":"sha256-VnznOcHy65RjdxIlMZFXsoImMxEtRYNVU6jbG1QXkq4="},{"Name":"label","Value":"_content/Roya/img/banner_bg.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11456"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022VnznOcHy65RjdxIlMZFXsoImMxEtRYNVU6jbG1QXkq4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/banner_bg.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\banner_bg.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-VnznOcHy65RjdxIlMZFXsoImMxEtRYNVU6jbG1QXkq4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"11456"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022VnznOcHy65RjdxIlMZFXsoImMxEtRYNVU6jbG1QXkq4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/banner_img.072d1qkl7c.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\banner_img.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"072d1qkl7c"},{"Name":"integrity","Value":"sha256-XBXXa4aR1cwYQYw2MAFoNGYhmeYaofk4pIsBKZQoyTQ="},{"Name":"label","Value":"_content/Roya/img/banner_img.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"168983"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022XBXXa4aR1cwYQYw2MAFoNGYhmeYaofk4pIsBKZQoyTQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/banner_img.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\banner_img.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-XBXXa4aR1cwYQYw2MAFoNGYhmeYaofk4pIsBKZQoyTQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"168983"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022XBXXa4aR1cwYQYw2MAFoNGYhmeYaofk4pIsBKZQoyTQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/author.c0lhaa28ge.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\author.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"c0lhaa28ge"},{"Name":"integrity","Value":"sha256-nFaPJnGPR\u002Blhj4ViSD/ssMExcpsNFua0CgPuxoHv5YQ="},{"Name":"label","Value":"_content/Roya/img/blog/author.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"17489"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022nFaPJnGPR\u002Blhj4ViSD/ssMExcpsNFua0CgPuxoHv5YQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/author.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\author.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nFaPJnGPR\u002Blhj4ViSD/ssMExcpsNFua0CgPuxoHv5YQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"17489"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022nFaPJnGPR\u002Blhj4ViSD/ssMExcpsNFua0CgPuxoHv5YQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/blog_1.k5sm9imi24.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\blog_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"k5sm9imi24"},{"Name":"integrity","Value":"sha256-CElUztuXWmAIHsGkXR/mYSU0wYFztIPHdhslb/x/IBs="},{"Name":"label","Value":"_content/Roya/img/blog/blog_1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"190587"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022CElUztuXWmAIHsGkXR/mYSU0wYFztIPHdhslb/x/IBs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/blog_1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\blog_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CElUztuXWmAIHsGkXR/mYSU0wYFztIPHdhslb/x/IBs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"190587"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022CElUztuXWmAIHsGkXR/mYSU0wYFztIPHdhslb/x/IBs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/blog_2.ms181ebbgp.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\blog_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ms181ebbgp"},{"Name":"integrity","Value":"sha256-FsR6KfcVlRJt5wRF12dRKTyANAY/nSxSNak4J97oIvw="},{"Name":"label","Value":"_content/Roya/img/blog/blog_2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"200297"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022FsR6KfcVlRJt5wRF12dRKTyANAY/nSxSNak4J97oIvw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/blog_2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\blog_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-FsR6KfcVlRJt5wRF12dRKTyANAY/nSxSNak4J97oIvw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"200297"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022FsR6KfcVlRJt5wRF12dRKTyANAY/nSxSNak4J97oIvw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/blog_3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\blog_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-af1mqJ1McoaDAbv\u002BxXN77G79pOlk\u002BYdb32/OiR4jE2U="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"216619"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022af1mqJ1McoaDAbv\u002BxXN77G79pOlk\u002BYdb32/OiR4jE2U=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/blog_3.rj1z08naqy.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\blog_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rj1z08naqy"},{"Name":"integrity","Value":"sha256-af1mqJ1McoaDAbv\u002BxXN77G79pOlk\u002BYdb32/OiR4jE2U="},{"Name":"label","Value":"_content/Roya/img/blog/blog_3.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"216619"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022af1mqJ1McoaDAbv\u002BxXN77G79pOlk\u002BYdb32/OiR4jE2U=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/blog_4.ealnxnhzpn.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\blog_4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ealnxnhzpn"},{"Name":"integrity","Value":"sha256-euc0ZsY\u002Be/SPMsTSlekFqKl88EA2DsvrJtMkLa46Fmo="},{"Name":"label","Value":"_content/Roya/img/blog/blog_4.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"163377"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022euc0ZsY\u002Be/SPMsTSlekFqKl88EA2DsvrJtMkLa46Fmo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/blog_4.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\blog_4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-euc0ZsY\u002Be/SPMsTSlekFqKl88EA2DsvrJtMkLa46Fmo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"163377"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022euc0ZsY\u002Be/SPMsTSlekFqKl88EA2DsvrJtMkLa46Fmo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/learn_about_bg.h81xrxuynk.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\learn_about_bg.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"h81xrxuynk"},{"Name":"integrity","Value":"sha256-f/zly1PmZf\u002BCLhAK2IB1gp0wRxB\u002BDW9VQ2GfzfFzZxM="},{"Name":"label","Value":"_content/Roya/img/blog/learn_about_bg.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2054934"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022f/zly1PmZf\u002BCLhAK2IB1gp0wRxB\u002BDW9VQ2GfzfFzZxM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/learn_about_bg.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\learn_about_bg.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-f/zly1PmZf\u002BCLhAK2IB1gp0wRxB\u002BDW9VQ2GfzfFzZxM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2054934"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022f/zly1PmZf\u002BCLhAK2IB1gp0wRxB\u002BDW9VQ2GfzfFzZxM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/single_blog_1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\single_blog_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-q7QEpuQtDweRPPtUutroKGCGeCKnhXaSJ8N/8uK61Zc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"453240"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022q7QEpuQtDweRPPtUutroKGCGeCKnhXaSJ8N/8uK61Zc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/single_blog_1.xl0lxlmrj4.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\single_blog_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"xl0lxlmrj4"},{"Name":"integrity","Value":"sha256-q7QEpuQtDweRPPtUutroKGCGeCKnhXaSJ8N/8uK61Zc="},{"Name":"label","Value":"_content/Roya/img/blog/single_blog_1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"453240"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022q7QEpuQtDweRPPtUutroKGCGeCKnhXaSJ8N/8uK61Zc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/single_blog_2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\single_blog_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YqNi6e5yEnKPWK08pC3XLY9ooj3mAmc1tZwTeVm/IZw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"382831"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022YqNi6e5yEnKPWK08pC3XLY9ooj3mAmc1tZwTeVm/IZw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/single_blog_2.uww4plj27u.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\single_blog_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uww4plj27u"},{"Name":"integrity","Value":"sha256-YqNi6e5yEnKPWK08pC3XLY9ooj3mAmc1tZwTeVm/IZw="},{"Name":"label","Value":"_content/Roya/img/blog/single_blog_2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"382831"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022YqNi6e5yEnKPWK08pC3XLY9ooj3mAmc1tZwTeVm/IZw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/single_blog_3.7lck46me5l.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\single_blog_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7lck46me5l"},{"Name":"integrity","Value":"sha256-u7iQwQysl3d1eVus\u002BXRV4iAo801ByuLRA7RZDmQ\u002BfJ0="},{"Name":"label","Value":"_content/Roya/img/blog/single_blog_3.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"452936"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022u7iQwQysl3d1eVus\u002BXRV4iAo801ByuLRA7RZDmQ\u002BfJ0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/single_blog_3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\single_blog_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-u7iQwQysl3d1eVus\u002BXRV4iAo801ByuLRA7RZDmQ\u002BfJ0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"452936"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022u7iQwQysl3d1eVus\u002BXRV4iAo801ByuLRA7RZDmQ\u002BfJ0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/single_blog_4.imerez9buq.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\single_blog_4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"imerez9buq"},{"Name":"integrity","Value":"sha256-NvMWAKdRA8P92ijVAMF6v33hL9Ld1jHRN674gjwAcP8="},{"Name":"label","Value":"_content/Roya/img/blog/single_blog_4.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"346773"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022NvMWAKdRA8P92ijVAMF6v33hL9Ld1jHRN674gjwAcP8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/single_blog_4.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\single_blog_4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NvMWAKdRA8P92ijVAMF6v33hL9Ld1jHRN674gjwAcP8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"346773"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022NvMWAKdRA8P92ijVAMF6v33hL9Ld1jHRN674gjwAcP8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/single_blog_5.7je6nnm067.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\single_blog_5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7je6nnm067"},{"Name":"integrity","Value":"sha256-7d07uquhP0nIm4Nm5FFS/a0iEaffUjPUKKAWrRH14KE="},{"Name":"label","Value":"_content/Roya/img/blog/single_blog_5.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"485273"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00227d07uquhP0nIm4Nm5FFS/a0iEaffUjPUKKAWrRH14KE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/single_blog_5.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\single_blog_5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-7d07uquhP0nIm4Nm5FFS/a0iEaffUjPUKKAWrRH14KE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"485273"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00227d07uquhP0nIm4Nm5FFS/a0iEaffUjPUKKAWrRH14KE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/slide_thumb_1.fu4uh4gsr8.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\slide_thumb_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fu4uh4gsr8"},{"Name":"integrity","Value":"sha256-B7UujTVI0QAaY1Z6\u002BbFRW\u002B1NfP4Tr8au7ROMxdP76z8="},{"Name":"label","Value":"_content/Roya/img/blog/slide_thumb_1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"91092"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022B7UujTVI0QAaY1Z6\u002BbFRW\u002B1NfP4Tr8au7ROMxdP76z8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/blog/slide_thumb_1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\blog\slide_thumb_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-B7UujTVI0QAaY1Z6\u002BbFRW\u002B1NfP4Tr8au7ROMxdP76z8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"91092"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022B7UujTVI0QAaY1Z6\u002BbFRW\u002B1NfP4Tr8au7ROMxdP76z8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/breadcrumb.4ve9vdlttb.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\breadcrumb.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4ve9vdlttb"},{"Name":"integrity","Value":"sha256-gOlxdVV6LB3jbDRKlDfNWF247whL/sDInNl15FZSe2Y="},{"Name":"label","Value":"_content/Roya/img/breadcrumb.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1399933"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022gOlxdVV6LB3jbDRKlDfNWF247whL/sDInNl15FZSe2Y=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/breadcrumb.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\breadcrumb.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gOlxdVV6LB3jbDRKlDfNWF247whL/sDInNl15FZSe2Y="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1399933"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022gOlxdVV6LB3jbDRKlDfNWF247whL/sDInNl15FZSe2Y=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/client/client_1.n583st2zya.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\client\client_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"n583st2zya"},{"Name":"integrity","Value":"sha256-9aOQuEvh/xfj\u002BiL8bBVXyGE\u002BV8whBesC6\u002BCM\u002BAJnIlk="},{"Name":"label","Value":"_content/Roya/img/client/client_1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"22903"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00229aOQuEvh/xfj\u002BiL8bBVXyGE\u002BV8whBesC6\u002BCM\u002BAJnIlk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/client/client_1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\client\client_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9aOQuEvh/xfj\u002BiL8bBVXyGE\u002BV8whBesC6\u002BCM\u002BAJnIlk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"22903"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00229aOQuEvh/xfj\u002BiL8bBVXyGE\u002BV8whBesC6\u002BCM\u002BAJnIlk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/client/client_2.g78z4hh73d.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\client\client_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"g78z4hh73d"},{"Name":"integrity","Value":"sha256-TEzfP9ifKmEYlTmK6xpYt0NcFWveee78Msnh1e1iyYU="},{"Name":"label","Value":"_content/Roya/img/client/client_2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"22230"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022TEzfP9ifKmEYlTmK6xpYt0NcFWveee78Msnh1e1iyYU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/client/client_2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\client\client_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-TEzfP9ifKmEYlTmK6xpYt0NcFWveee78Msnh1e1iyYU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"22230"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022TEzfP9ifKmEYlTmK6xpYt0NcFWveee78Msnh1e1iyYU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/comment/comment_1.438ag86sgz.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\comment\comment_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"438ag86sgz"},{"Name":"integrity","Value":"sha256-kCikANu2Y/LmZQdZf7gVqozU8Qc8d3qU4LyJZ6Gy660="},{"Name":"label","Value":"_content/Roya/img/comment/comment_1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11303"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022kCikANu2Y/LmZQdZf7gVqozU8Qc8d3qU4LyJZ6Gy660=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/comment/comment_1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\comment\comment_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-kCikANu2Y/LmZQdZf7gVqozU8Qc8d3qU4LyJZ6Gy660="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"11303"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022kCikANu2Y/LmZQdZf7gVqozU8Qc8d3qU4LyJZ6Gy660=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/comment/comment_2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\comment\comment_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CjKTUhCESLrY0yjnRrA5sBKxWf5\u002BxgG\u002BcXbfnTzc3kI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"10561"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022CjKTUhCESLrY0yjnRrA5sBKxWf5\u002BxgG\u002BcXbfnTzc3kI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/comment/comment_2.qzfsaxmvo7.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\comment\comment_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qzfsaxmvo7"},{"Name":"integrity","Value":"sha256-CjKTUhCESLrY0yjnRrA5sBKxWf5\u002BxgG\u002BcXbfnTzc3kI="},{"Name":"label","Value":"_content/Roya/img/comment/comment_2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"10561"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022CjKTUhCESLrY0yjnRrA5sBKxWf5\u002BxgG\u002BcXbfnTzc3kI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/comment/comment_3.kl7ocetq2n.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\comment\comment_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kl7ocetq2n"},{"Name":"integrity","Value":"sha256-iO/wQ9Dm29LWgY2TVP9i/L/NFnv0HJqH6T\u002BS3c2rAdk="},{"Name":"label","Value":"_content/Roya/img/comment/comment_3.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11401"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022iO/wQ9Dm29LWgY2TVP9i/L/NFnv0HJqH6T\u002BS3c2rAdk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/comment/comment_3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\comment\comment_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-iO/wQ9Dm29LWgY2TVP9i/L/NFnv0HJqH6T\u002BS3c2rAdk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"11401"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022iO/wQ9Dm29LWgY2TVP9i/L/NFnv0HJqH6T\u002BS3c2rAdk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/cource/cource_1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\cource\cource_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-agZlFu\u002BT5Pk3QRC/z3jwmR70sLVku2c/Z3cr/l1ZRes="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"11288"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022agZlFu\u002BT5Pk3QRC/z3jwmR70sLVku2c/Z3cr/l1ZRes=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/cource/cource_1.yigjo1nwjg.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\cource\cource_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"yigjo1nwjg"},{"Name":"integrity","Value":"sha256-agZlFu\u002BT5Pk3QRC/z3jwmR70sLVku2c/Z3cr/l1ZRes="},{"Name":"label","Value":"_content/Roya/img/cource/cource_1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11288"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022agZlFu\u002BT5Pk3QRC/z3jwmR70sLVku2c/Z3cr/l1ZRes=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/cource/cource_2.is3g8bfe7c.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\cource\cource_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"is3g8bfe7c"},{"Name":"integrity","Value":"sha256-MvuND2KUqIVVpl9UjgDRceg13RV1t\u002B3wzIGr6z6UQgA="},{"Name":"label","Value":"_content/Roya/img/cource/cource_2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11356"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022MvuND2KUqIVVpl9UjgDRceg13RV1t\u002B3wzIGr6z6UQgA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/cource/cource_2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\cource\cource_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MvuND2KUqIVVpl9UjgDRceg13RV1t\u002B3wzIGr6z6UQgA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"11356"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022MvuND2KUqIVVpl9UjgDRceg13RV1t\u002B3wzIGr6z6UQgA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/cource/cource_3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\cource\cource_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vXdpVlwfd9DJ0e7Xij3fz9HGc5Z5rb8UFNl8ElRkCn0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"10110"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022vXdpVlwfd9DJ0e7Xij3fz9HGc5Z5rb8UFNl8ElRkCn0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/cource/cource_3.rezkcb0wrv.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\cource\cource_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rezkcb0wrv"},{"Name":"integrity","Value":"sha256-vXdpVlwfd9DJ0e7Xij3fz9HGc5Z5rb8UFNl8ElRkCn0="},{"Name":"label","Value":"_content/Roya/img/cource/cource_3.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"10110"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022vXdpVlwfd9DJ0e7Xij3fz9HGc5Z5rb8UFNl8ElRkCn0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/a.c6cclzpb4o.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\a.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"c6cclzpb4o"},{"Name":"integrity","Value":"sha256-pLmgGLXKWrhqgPxrYbnthhiJJAkahvpV23pg01vfmLI="},{"Name":"label","Value":"_content/Roya/img/elements/a.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4784"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022pLmgGLXKWrhqgPxrYbnthhiJJAkahvpV23pg01vfmLI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/a.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\a.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-pLmgGLXKWrhqgPxrYbnthhiJJAkahvpV23pg01vfmLI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4784"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022pLmgGLXKWrhqgPxrYbnthhiJJAkahvpV23pg01vfmLI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/a2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\a2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ZQNaiBO6fAx4H6XBZSqVXncvPw\u002BQHxzjwEqePn4Ctqk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"17427"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022ZQNaiBO6fAx4H6XBZSqVXncvPw\u002BQHxzjwEqePn4Ctqk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/a2.t20kabvi7v.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\a2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"t20kabvi7v"},{"Name":"integrity","Value":"sha256-ZQNaiBO6fAx4H6XBZSqVXncvPw\u002BQHxzjwEqePn4Ctqk="},{"Name":"label","Value":"_content/Roya/img/elements/a2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"17427"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022ZQNaiBO6fAx4H6XBZSqVXncvPw\u002BQHxzjwEqePn4Ctqk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/d.35e4qoxnyj.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\d.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"35e4qoxnyj"},{"Name":"integrity","Value":"sha256-aVUce1Fu4tiP29SPkQI9c6P1SdOVGm0uRTyvNzMwrYE="},{"Name":"label","Value":"_content/Roya/img/elements/d.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"14520"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022aVUce1Fu4tiP29SPkQI9c6P1SdOVGm0uRTyvNzMwrYE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/d.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\d.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-aVUce1Fu4tiP29SPkQI9c6P1SdOVGm0uRTyvNzMwrYE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"14520"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022aVUce1Fu4tiP29SPkQI9c6P1SdOVGm0uRTyvNzMwrYE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/disabled-check.13hhhmus7v.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\disabled-check.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"13hhhmus7v"},{"Name":"integrity","Value":"sha256-vRmQNVnpCnlYxbpGf/5\u002BIpmjwHs3N9WMQRqEh9nL7ic="},{"Name":"label","Value":"_content/Roya/img/elements/disabled-check.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1249"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022vRmQNVnpCnlYxbpGf/5\u002BIpmjwHs3N9WMQRqEh9nL7ic=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/disabled-check.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\disabled-check.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vRmQNVnpCnlYxbpGf/5\u002BIpmjwHs3N9WMQRqEh9nL7ic="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1249"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022vRmQNVnpCnlYxbpGf/5\u002BIpmjwHs3N9WMQRqEh9nL7ic=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/disabled-radio.7fv3n8zbq1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\disabled-radio.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7fv3n8zbq1"},{"Name":"integrity","Value":"sha256-LbcwECRb00ulL5SA0v15TCJReOSYfVNwl0TCExBJUHE="},{"Name":"label","Value":"_content/Roya/img/elements/disabled-radio.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1173"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022LbcwECRb00ulL5SA0v15TCJReOSYfVNwl0TCExBJUHE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/disabled-radio.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\disabled-radio.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-LbcwECRb00ulL5SA0v15TCJReOSYfVNwl0TCExBJUHE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1173"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022LbcwECRb00ulL5SA0v15TCJReOSYfVNwl0TCExBJUHE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/f1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\f1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Xwq1dqGn6vHbyh\u002BQ\u002BGutQ9B4PE/\u002ByPRStuh7yx0c6Cc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1879"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Xwq1dqGn6vHbyh\u002BQ\u002BGutQ9B4PE/\u002ByPRStuh7yx0c6Cc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/f1.lr460eeral.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\f1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lr460eeral"},{"Name":"integrity","Value":"sha256-Xwq1dqGn6vHbyh\u002BQ\u002BGutQ9B4PE/\u002ByPRStuh7yx0c6Cc="},{"Name":"label","Value":"_content/Roya/img/elements/f1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1879"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Xwq1dqGn6vHbyh\u002BQ\u002BGutQ9B4PE/\u002ByPRStuh7yx0c6Cc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/f2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\f2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-oK52\u002B2d9eC1G6KrySZWQk7aVScmbeKIi4Qhip4xf3Bw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1957"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022oK52\u002B2d9eC1G6KrySZWQk7aVScmbeKIi4Qhip4xf3Bw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/f2.wcdkyzypsf.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\f2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wcdkyzypsf"},{"Name":"integrity","Value":"sha256-oK52\u002B2d9eC1G6KrySZWQk7aVScmbeKIi4Qhip4xf3Bw="},{"Name":"label","Value":"_content/Roya/img/elements/f2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1957"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022oK52\u002B2d9eC1G6KrySZWQk7aVScmbeKIi4Qhip4xf3Bw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/f3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\f3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-S2Q5mvMmQNKv7Yo21osmK3WbtgqcxiAdghpX5epGeEw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2423"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022S2Q5mvMmQNKv7Yo21osmK3WbtgqcxiAdghpX5epGeEw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/f3.pz71p55qut.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\f3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pz71p55qut"},{"Name":"integrity","Value":"sha256-S2Q5mvMmQNKv7Yo21osmK3WbtgqcxiAdghpX5epGeEw="},{"Name":"label","Value":"_content/Roya/img/elements/f3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2423"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022S2Q5mvMmQNKv7Yo21osmK3WbtgqcxiAdghpX5epGeEw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/f4.290osb73s5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\f4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"290osb73s5"},{"Name":"integrity","Value":"sha256-uhDo2guLguKtZVwZkUWm/DiyQIESAjABOLQ4QuC3eVI="},{"Name":"label","Value":"_content/Roya/img/elements/f4.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1559"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022uhDo2guLguKtZVwZkUWm/DiyQIESAjABOLQ4QuC3eVI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/f4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\f4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-uhDo2guLguKtZVwZkUWm/DiyQIESAjABOLQ4QuC3eVI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1559"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022uhDo2guLguKtZVwZkUWm/DiyQIESAjABOLQ4QuC3eVI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/f5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\f5.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-kEEWKpPUL2EgT9a9QvLZvlZs5mQvjsVZdbiZBFUUmAg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1825"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022kEEWKpPUL2EgT9a9QvLZvlZs5mQvjsVZdbiZBFUUmAg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/f5.s1hhtd8vqv.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\f5.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"s1hhtd8vqv"},{"Name":"integrity","Value":"sha256-kEEWKpPUL2EgT9a9QvLZvlZs5mQvjsVZdbiZBFUUmAg="},{"Name":"label","Value":"_content/Roya/img/elements/f5.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1825"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022kEEWKpPUL2EgT9a9QvLZvlZs5mQvjsVZdbiZBFUUmAg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/f6.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\f6.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-yZCiwpAGIpTL15rlGWwtNFgVOHZF1tXLO\u002BexEVZxtbI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1427"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022yZCiwpAGIpTL15rlGWwtNFgVOHZF1tXLO\u002BexEVZxtbI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/f6.jxsr0l3g16.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\f6.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jxsr0l3g16"},{"Name":"integrity","Value":"sha256-yZCiwpAGIpTL15rlGWwtNFgVOHZF1tXLO\u002BexEVZxtbI="},{"Name":"label","Value":"_content/Roya/img/elements/f6.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1427"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022yZCiwpAGIpTL15rlGWwtNFgVOHZF1tXLO\u002BexEVZxtbI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/f7.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\f7.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-o4P/knvZM83tcftA3BZuJ1DrH3ljz6jq4\u002BoCV/oFE8w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1516"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022o4P/knvZM83tcftA3BZuJ1DrH3ljz6jq4\u002BoCV/oFE8w=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/f7.xfy4d6btp5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\f7.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"xfy4d6btp5"},{"Name":"integrity","Value":"sha256-o4P/knvZM83tcftA3BZuJ1DrH3ljz6jq4\u002BoCV/oFE8w="},{"Name":"label","Value":"_content/Roya/img/elements/f7.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1516"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022o4P/knvZM83tcftA3BZuJ1DrH3ljz6jq4\u002BoCV/oFE8w=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/f8.edqo9bspyr.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\f8.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"edqo9bspyr"},{"Name":"integrity","Value":"sha256-fq9qYSWuOoajuTYbB05ve/QC3Sn0Mr5m3kLpD9uUqM4="},{"Name":"label","Value":"_content/Roya/img/elements/f8.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1343"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022fq9qYSWuOoajuTYbB05ve/QC3Sn0Mr5m3kLpD9uUqM4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/f8.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\f8.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-fq9qYSWuOoajuTYbB05ve/QC3Sn0Mr5m3kLpD9uUqM4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1343"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022fq9qYSWuOoajuTYbB05ve/QC3Sn0Mr5m3kLpD9uUqM4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/g1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\g1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4euZMJpmNYjOssFKCkqGPK9D\u002BQRmMkbhNVs6KXsI3DI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"124531"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00224euZMJpmNYjOssFKCkqGPK9D\u002BQRmMkbhNVs6KXsI3DI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/g1.rxy1d0wmc0.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\g1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rxy1d0wmc0"},{"Name":"integrity","Value":"sha256-4euZMJpmNYjOssFKCkqGPK9D\u002BQRmMkbhNVs6KXsI3DI="},{"Name":"label","Value":"_content/Roya/img/elements/g1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"124531"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00224euZMJpmNYjOssFKCkqGPK9D\u002BQRmMkbhNVs6KXsI3DI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/g2.23sz01xx3y.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\g2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"23sz01xx3y"},{"Name":"integrity","Value":"sha256-EpOJ06ZKgxS1FDIdT\u002BrTMgF6Oy8QOtHKu\u002BHGo/mM9sQ="},{"Name":"label","Value":"_content/Roya/img/elements/g2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"116798"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022EpOJ06ZKgxS1FDIdT\u002BrTMgF6Oy8QOtHKu\u002BHGo/mM9sQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/g2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\g2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-EpOJ06ZKgxS1FDIdT\u002BrTMgF6Oy8QOtHKu\u002BHGo/mM9sQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"116798"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022EpOJ06ZKgxS1FDIdT\u002BrTMgF6Oy8QOtHKu\u002BHGo/mM9sQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/g3.91xrseto4d.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\g3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"91xrseto4d"},{"Name":"integrity","Value":"sha256-A7w2JqmzNYGpetP4/cRoFcs68YlphXtPT5q1gDJFdyc="},{"Name":"label","Value":"_content/Roya/img/elements/g3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"81581"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022A7w2JqmzNYGpetP4/cRoFcs68YlphXtPT5q1gDJFdyc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/g3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\g3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-A7w2JqmzNYGpetP4/cRoFcs68YlphXtPT5q1gDJFdyc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"81581"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022A7w2JqmzNYGpetP4/cRoFcs68YlphXtPT5q1gDJFdyc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/g4.hy6s45r7gl.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\g4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hy6s45r7gl"},{"Name":"integrity","Value":"sha256-x8VBWk6pyRSoii/ju5MuA9rwlsHtsWF4FOqVg\u002Bz1ICE="},{"Name":"label","Value":"_content/Roya/img/elements/g4.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"84926"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022x8VBWk6pyRSoii/ju5MuA9rwlsHtsWF4FOqVg\u002Bz1ICE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/g4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\g4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-x8VBWk6pyRSoii/ju5MuA9rwlsHtsWF4FOqVg\u002Bz1ICE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"84926"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022x8VBWk6pyRSoii/ju5MuA9rwlsHtsWF4FOqVg\u002Bz1ICE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/g5.0dgq433kc5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\g5.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0dgq433kc5"},{"Name":"integrity","Value":"sha256-VHUzQCB1eD01vrfmeRc/5ug0IF0Llf4XL777QNcFKh0="},{"Name":"label","Value":"_content/Roya/img/elements/g5.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"64607"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022VHUzQCB1eD01vrfmeRc/5ug0IF0Llf4XL777QNcFKh0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/g5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\g5.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-VHUzQCB1eD01vrfmeRc/5ug0IF0Llf4XL777QNcFKh0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"64607"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022VHUzQCB1eD01vrfmeRc/5ug0IF0Llf4XL777QNcFKh0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/g6.id4w7kud95.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\g6.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"id4w7kud95"},{"Name":"integrity","Value":"sha256-JjUZXXR6YAN1vRHtBQyQ\u002BQuxM0IHxb7cAAapz2BsM7k="},{"Name":"label","Value":"_content/Roya/img/elements/g6.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"74445"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JjUZXXR6YAN1vRHtBQyQ\u002BQuxM0IHxb7cAAapz2BsM7k=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/g6.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\g6.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JjUZXXR6YAN1vRHtBQyQ\u002BQuxM0IHxb7cAAapz2BsM7k="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"74445"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JjUZXXR6YAN1vRHtBQyQ\u002BQuxM0IHxb7cAAapz2BsM7k=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/g7.8rkkqjfm6e.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\g7.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"8rkkqjfm6e"},{"Name":"integrity","Value":"sha256-7GMTjMQloObiezIjDnI/DUN\u002Bdmo7srv9Z6rJVf2JXpc="},{"Name":"label","Value":"_content/Roya/img/elements/g7.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"157297"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00227GMTjMQloObiezIjDnI/DUN\u002Bdmo7srv9Z6rJVf2JXpc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/g7.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\g7.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-7GMTjMQloObiezIjDnI/DUN\u002Bdmo7srv9Z6rJVf2JXpc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"157297"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00227GMTjMQloObiezIjDnI/DUN\u002Bdmo7srv9Z6rJVf2JXpc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/g8.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\g8.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-q33Ks0LNEqXDCR9he9ni6wI4aZHgOiGfnZViSI7h3Yw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"16790"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022q33Ks0LNEqXDCR9he9ni6wI4aZHgOiGfnZViSI7h3Yw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/g8.pf2ogcaste.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\g8.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pf2ogcaste"},{"Name":"integrity","Value":"sha256-q33Ks0LNEqXDCR9he9ni6wI4aZHgOiGfnZViSI7h3Yw="},{"Name":"label","Value":"_content/Roya/img/elements/g8.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16790"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022q33Ks0LNEqXDCR9he9ni6wI4aZHgOiGfnZViSI7h3Yw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/primary-check.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\primary-check.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-41m\u002Bn0daq6SP5DsvPwoFVzCY9XnpB5tVU1SbvoNG2XI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1661"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u002241m\u002Bn0daq6SP5DsvPwoFVzCY9XnpB5tVU1SbvoNG2XI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/primary-check.tg4y7dqw0n.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\primary-check.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tg4y7dqw0n"},{"Name":"integrity","Value":"sha256-41m\u002Bn0daq6SP5DsvPwoFVzCY9XnpB5tVU1SbvoNG2XI="},{"Name":"label","Value":"_content/Roya/img/elements/primary-check.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1661"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u002241m\u002Bn0daq6SP5DsvPwoFVzCY9XnpB5tVU1SbvoNG2XI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/primary-radio.fysintaf3r.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\primary-radio.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fysintaf3r"},{"Name":"integrity","Value":"sha256-F866KOAUYighyMtzwJu0nXyHY9h7PxHiGsMI98L6mFA="},{"Name":"label","Value":"_content/Roya/img/elements/primary-radio.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1284"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022F866KOAUYighyMtzwJu0nXyHY9h7PxHiGsMI98L6mFA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/primary-radio.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\primary-radio.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-F866KOAUYighyMtzwJu0nXyHY9h7PxHiGsMI98L6mFA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1284"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022F866KOAUYighyMtzwJu0nXyHY9h7PxHiGsMI98L6mFA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/success-check.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\success-check.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-FSXQrJeNDiytQtpju3viWshKwaAIaanr85ZKPbi\u002BNYo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1205"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022FSXQrJeNDiytQtpju3viWshKwaAIaanr85ZKPbi\u002BNYo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/success-check.rk00sr3vo1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\success-check.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rk00sr3vo1"},{"Name":"integrity","Value":"sha256-FSXQrJeNDiytQtpju3viWshKwaAIaanr85ZKPbi\u002BNYo="},{"Name":"label","Value":"_content/Roya/img/elements/success-check.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1205"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022FSXQrJeNDiytQtpju3viWshKwaAIaanr85ZKPbi\u002BNYo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/success-radio.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\success-radio.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-sU\u002BQh52JZXmHG3qu2trJHbwQodW2\u002BZLjqHJ0CWvMdCA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1209"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022sU\u002BQh52JZXmHG3qu2trJHbwQodW2\u002BZLjqHJ0CWvMdCA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/elements/success-radio.rl8nd2j6ll.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\elements\success-radio.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rl8nd2j6ll"},{"Name":"integrity","Value":"sha256-sU\u002BQh52JZXmHG3qu2trJHbwQodW2\u002BZLjqHJ0CWvMdCA="},{"Name":"label","Value":"_content/Roya/img/elements/success-radio.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1209"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022sU\u002BQh52JZXmHG3qu2trJHbwQodW2\u002BZLjqHJ0CWvMdCA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/favicon.9dzt21oeb8.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\favicon.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9dzt21oeb8"},{"Name":"integrity","Value":"sha256-s8GgBD6WtOzoAMbqc8A2S7PdkBUmmU/o58YGXf/Kohw="},{"Name":"label","Value":"_content/Roya/img/favicon.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2445"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022s8GgBD6WtOzoAMbqc8A2S7PdkBUmmU/o58YGXf/Kohw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/favicon.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\favicon.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-s8GgBD6WtOzoAMbqc8A2S7PdkBUmmU/o58YGXf/Kohw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2445"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022s8GgBD6WtOzoAMbqc8A2S7PdkBUmmU/o58YGXf/Kohw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-AVzRPAZExFWuP0E4waL09K8R6f3zdBzO8171a8Y/Fmk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"249277"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022AVzRPAZExFWuP0E4waL09K8R6f3zdBzO8171a8Y/Fmk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item.vs7e58cu7m.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vs7e58cu7m"},{"Name":"integrity","Value":"sha256-AVzRPAZExFWuP0E4waL09K8R6f3zdBzO8171a8Y/Fmk="},{"Name":"label","Value":"_content/Roya/img/gallery/gallery_item.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"249277"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022AVzRPAZExFWuP0E4waL09K8R6f3zdBzO8171a8Y/Fmk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_1.mq53xsn95z.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mq53xsn95z"},{"Name":"integrity","Value":"sha256-Ml9KTQ\u002BmeZbvHhurUxypD2MmgrfXHnOWlz\u002BampaeINw="},{"Name":"label","Value":"_content/Roya/img/gallery/gallery_item_1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"292847"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Ml9KTQ\u002BmeZbvHhurUxypD2MmgrfXHnOWlz\u002BampaeINw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Ml9KTQ\u002BmeZbvHhurUxypD2MmgrfXHnOWlz\u002BampaeINw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"292847"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Ml9KTQ\u002BmeZbvHhurUxypD2MmgrfXHnOWlz\u002BampaeINw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_2.gq2chbamp7.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gq2chbamp7"},{"Name":"integrity","Value":"sha256-2HXmvoc7/KR1CjlsaEZfVsSBchDjf2wFssct2zBjZfA="},{"Name":"label","Value":"_content/Roya/img/gallery/gallery_item_2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"219838"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00222HXmvoc7/KR1CjlsaEZfVsSBchDjf2wFssct2zBjZfA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-2HXmvoc7/KR1CjlsaEZfVsSBchDjf2wFssct2zBjZfA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"219838"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00222HXmvoc7/KR1CjlsaEZfVsSBchDjf2wFssct2zBjZfA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_3.4yzgtw3t7l.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4yzgtw3t7l"},{"Name":"integrity","Value":"sha256-BLkyhFT3qxHNNZt/fY\u002BJKbIqBUKFZmE0NjKreiS\u002BTg0="},{"Name":"label","Value":"_content/Roya/img/gallery/gallery_item_3.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"389010"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022BLkyhFT3qxHNNZt/fY\u002BJKbIqBUKFZmE0NjKreiS\u002BTg0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-BLkyhFT3qxHNNZt/fY\u002BJKbIqBUKFZmE0NjKreiS\u002BTg0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"389010"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022BLkyhFT3qxHNNZt/fY\u002BJKbIqBUKFZmE0NjKreiS\u002BTg0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_4.1hrh72tsny.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"1hrh72tsny"},{"Name":"integrity","Value":"sha256-q9uXy4Vf6\u002B6\u002Bpy/aCfdK0FLUq7llcBYcxrAK1XMEPGQ="},{"Name":"label","Value":"_content/Roya/img/gallery/gallery_item_4.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"432433"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022q9uXy4Vf6\u002B6\u002Bpy/aCfdK0FLUq7llcBYcxrAK1XMEPGQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_4.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-q9uXy4Vf6\u002B6\u002Bpy/aCfdK0FLUq7llcBYcxrAK1XMEPGQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"432433"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022q9uXy4Vf6\u002B6\u002Bpy/aCfdK0FLUq7llcBYcxrAK1XMEPGQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_5.goatoollmm.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"goatoollmm"},{"Name":"integrity","Value":"sha256-sBwPt99SiBH9quJIqZTK3AS1V6XtPLZzYLkpDCJBXtQ="},{"Name":"label","Value":"_content/Roya/img/gallery/gallery_item_5.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"496522"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022sBwPt99SiBH9quJIqZTK3AS1V6XtPLZzYLkpDCJBXtQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_5.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-sBwPt99SiBH9quJIqZTK3AS1V6XtPLZzYLkpDCJBXtQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"496522"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022sBwPt99SiBH9quJIqZTK3AS1V6XtPLZzYLkpDCJBXtQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_6.okrdko35tq.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_6.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"okrdko35tq"},{"Name":"integrity","Value":"sha256-KM9McKGQrWG2cjUvqPUKceNg\u002Bj3O6mv3NUkZR/wXujo="},{"Name":"label","Value":"_content/Roya/img/gallery/gallery_item_6.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"449737"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022KM9McKGQrWG2cjUvqPUKceNg\u002Bj3O6mv3NUkZR/wXujo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_6.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_6.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-KM9McKGQrWG2cjUvqPUKceNg\u002Bj3O6mv3NUkZR/wXujo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"449737"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022KM9McKGQrWG2cjUvqPUKceNg\u002Bj3O6mv3NUkZR/wXujo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_7.9kvu3jn38d.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_7.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9kvu3jn38d"},{"Name":"integrity","Value":"sha256-t8UBttpCOpGLd6aR\u002Bs3y4wfr0kHSsyFYUKg7f7RlT8g="},{"Name":"label","Value":"_content/Roya/img/gallery/gallery_item_7.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"450512"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022t8UBttpCOpGLd6aR\u002Bs3y4wfr0kHSsyFYUKg7f7RlT8g=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_7.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_7.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-t8UBttpCOpGLd6aR\u002Bs3y4wfr0kHSsyFYUKg7f7RlT8g="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"450512"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022t8UBttpCOpGLd6aR\u002Bs3y4wfr0kHSsyFYUKg7f7RlT8g=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_71.clg6qq0uza.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_71.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"clg6qq0uza"},{"Name":"integrity","Value":"sha256-gOTFdyUpZbuL0\u002BuGZZNf4a711PPr\u002BeBxsvxz9uozxr0="},{"Name":"label","Value":"_content/Roya/img/gallery/gallery_item_71.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"452925"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022gOTFdyUpZbuL0\u002BuGZZNf4a711PPr\u002BeBxsvxz9uozxr0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_71.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_71.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gOTFdyUpZbuL0\u002BuGZZNf4a711PPr\u002BeBxsvxz9uozxr0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"452925"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022gOTFdyUpZbuL0\u002BuGZZNf4a711PPr\u002BeBxsvxz9uozxr0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_8.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_8.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-UwFUXiy6NyQh6XjXEFOxZxAyqqYabeyfBcaEgTYmKqc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"301282"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022UwFUXiy6NyQh6XjXEFOxZxAyqqYabeyfBcaEgTYmKqc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/gallery_item_8.vtqms1heon.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\gallery_item_8.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vtqms1heon"},{"Name":"integrity","Value":"sha256-UwFUXiy6NyQh6XjXEFOxZxAyqqYabeyfBcaEgTYmKqc="},{"Name":"label","Value":"_content/Roya/img/gallery/gallery_item_8.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"301282"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022UwFUXiy6NyQh6XjXEFOxZxAyqqYabeyfBcaEgTYmKqc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/service_bg_2.lc6iopwjva.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\service_bg_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lc6iopwjva"},{"Name":"integrity","Value":"sha256-WzO6s6kZznCYmhZjXjxBAGfcVpvB2h0XkqtVMJqznNI="},{"Name":"label","Value":"_content/Roya/img/gallery/service_bg_2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"9401"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022WzO6s6kZznCYmhZjXjxBAGfcVpvB2h0XkqtVMJqznNI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/gallery/service_bg_2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\gallery\service_bg_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-WzO6s6kZznCYmhZjXjxBAGfcVpvB2h0XkqtVMJqznNI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"9401"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022WzO6s6kZznCYmhZjXjxBAGfcVpvB2h0XkqtVMJqznNI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/icon/color_star.fs63qm7ts0.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\icon\color_star.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fs63qm7ts0"},{"Name":"integrity","Value":"sha256-H2f5H7o/nKARyKzRjFAa4Cd/1XLyb63ofxDk/YlFDFo="},{"Name":"label","Value":"_content/Roya/img/icon/color_star.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"700"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022H2f5H7o/nKARyKzRjFAa4Cd/1XLyb63ofxDk/YlFDFo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/icon/color_star.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\icon\color_star.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-H2f5H7o/nKARyKzRjFAa4Cd/1XLyb63ofxDk/YlFDFo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"700"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022H2f5H7o/nKARyKzRjFAa4Cd/1XLyb63ofxDk/YlFDFo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/icon/left.7rsv4a09w5.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\icon\left.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7rsv4a09w5"},{"Name":"integrity","Value":"sha256-rTA0YVMcqpuU3g/bK6Ee2PwycRj0nSYecK2de7JtDp8="},{"Name":"label","Value":"_content/Roya/img/icon/left.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"632"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022rTA0YVMcqpuU3g/bK6Ee2PwycRj0nSYecK2de7JtDp8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/icon/left.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\icon\left.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-rTA0YVMcqpuU3g/bK6Ee2PwycRj0nSYecK2de7JtDp8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"632"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022rTA0YVMcqpuU3g/bK6Ee2PwycRj0nSYecK2de7JtDp8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/icon/play.ft0udbvghe.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\icon\play.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ft0udbvghe"},{"Name":"integrity","Value":"sha256-XXeYcG1VYo7Yf8pc5JzYZnit4iWFcybVP3xlZPqxKnI="},{"Name":"label","Value":"_content/Roya/img/icon/play.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"239"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022XXeYcG1VYo7Yf8pc5JzYZnit4iWFcybVP3xlZPqxKnI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/icon/play.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\icon\play.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-XXeYcG1VYo7Yf8pc5JzYZnit4iWFcybVP3xlZPqxKnI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"239"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022XXeYcG1VYo7Yf8pc5JzYZnit4iWFcybVP3xlZPqxKnI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/icon/quate.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\icon\quate.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0H4W90MkwLdGIVFG6ywr2Y8pW4GfYpCMP9gQt313rEU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"578"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u00220H4W90MkwLdGIVFG6ywr2Y8pW4GfYpCMP9gQt313rEU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/icon/quate.ws461g44hq.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\icon\quate.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ws461g44hq"},{"Name":"integrity","Value":"sha256-0H4W90MkwLdGIVFG6ywr2Y8pW4GfYpCMP9gQt313rEU="},{"Name":"label","Value":"_content/Roya/img/icon/quate.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"578"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u00220H4W90MkwLdGIVFG6ywr2Y8pW4GfYpCMP9gQt313rEU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/icon/right.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\icon\right.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gTzmWudzgt9ep132IiTJw/6DXRkMYsw65ior6uRXE/0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"632"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022gTzmWudzgt9ep132IiTJw/6DXRkMYsw65ior6uRXE/0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/icon/right.x49pa6y8io.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\icon\right.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"x49pa6y8io"},{"Name":"integrity","Value":"sha256-gTzmWudzgt9ep132IiTJw/6DXRkMYsw65ior6uRXE/0="},{"Name":"label","Value":"_content/Roya/img/icon/right.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"632"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022gTzmWudzgt9ep132IiTJw/6DXRkMYsw65ior6uRXE/0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/icon/star.lo0fsqzigf.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\icon\star.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lo0fsqzigf"},{"Name":"integrity","Value":"sha256-y1oHfC/MskO5Cuy2YD/GR5Hm9xe0STso2IwfTDBY\u002BdM="},{"Name":"label","Value":"_content/Roya/img/icon/star.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"701"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022y1oHfC/MskO5Cuy2YD/GR5Hm9xe0STso2IwfTDBY\u002BdM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/icon/star.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\icon\star.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-y1oHfC/MskO5Cuy2YD/GR5Hm9xe0STso2IwfTDBY\u002BdM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"701"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022y1oHfC/MskO5Cuy2YD/GR5Hm9xe0STso2IwfTDBY\u002BdM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/insta/instagram_1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\insta\instagram_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-1gXHSNhlcxVYJIcIOR1TzlUwtnCZd9QJ1UHwa23mPs0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"170213"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00221gXHSNhlcxVYJIcIOR1TzlUwtnCZd9QJ1UHwa23mPs0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/insta/instagram_1.qfim3vkc02.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\insta\instagram_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qfim3vkc02"},{"Name":"integrity","Value":"sha256-1gXHSNhlcxVYJIcIOR1TzlUwtnCZd9QJ1UHwa23mPs0="},{"Name":"label","Value":"_content/Roya/img/insta/instagram_1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"170213"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00221gXHSNhlcxVYJIcIOR1TzlUwtnCZd9QJ1UHwa23mPs0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/insta/instagram_2.9v9r4nbxre.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\insta\instagram_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9v9r4nbxre"},{"Name":"integrity","Value":"sha256-S2XyXBtn3jWUCRLFKceAOim3Zi8msDrquGoWucdE1jU="},{"Name":"label","Value":"_content/Roya/img/insta/instagram_2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"172904"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022S2XyXBtn3jWUCRLFKceAOim3Zi8msDrquGoWucdE1jU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/insta/instagram_2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\insta\instagram_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-S2XyXBtn3jWUCRLFKceAOim3Zi8msDrquGoWucdE1jU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"172904"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022S2XyXBtn3jWUCRLFKceAOim3Zi8msDrquGoWucdE1jU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/insta/instagram_3.hgehc91kqu.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\insta\instagram_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hgehc91kqu"},{"Name":"integrity","Value":"sha256-ccMtomKLhXdv\u002Bo9SPYHDtnzyDTFfnaXu/\u002Bk5/ddnAQ0="},{"Name":"label","Value":"_content/Roya/img/insta/instagram_3.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"146746"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022ccMtomKLhXdv\u002Bo9SPYHDtnzyDTFfnaXu/\u002Bk5/ddnAQ0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/insta/instagram_3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\insta\instagram_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ccMtomKLhXdv\u002Bo9SPYHDtnzyDTFfnaXu/\u002Bk5/ddnAQ0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"146746"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022ccMtomKLhXdv\u002Bo9SPYHDtnzyDTFfnaXu/\u002Bk5/ddnAQ0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/insta/instagram_4.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\insta\instagram_4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5jhZZ3CFqWLIYP/cNYITtnodr4KYA1K4ZhwrsAf0pXw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"138080"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00225jhZZ3CFqWLIYP/cNYITtnodr4KYA1K4ZhwrsAf0pXw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/insta/instagram_4.qwooj4bszb.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\insta\instagram_4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qwooj4bszb"},{"Name":"integrity","Value":"sha256-5jhZZ3CFqWLIYP/cNYITtnodr4KYA1K4ZhwrsAf0pXw="},{"Name":"label","Value":"_content/Roya/img/insta/instagram_4.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"138080"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00225jhZZ3CFqWLIYP/cNYITtnodr4KYA1K4ZhwrsAf0pXw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/insta/instagram_5.cr68lubl7d.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\insta\instagram_5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"cr68lubl7d"},{"Name":"integrity","Value":"sha256-ODEbd4yzEIoduwl6n5VXVP47rKCfBfHKCZYsztFE7W8="},{"Name":"label","Value":"_content/Roya/img/insta/instagram_5.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"235602"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022ODEbd4yzEIoduwl6n5VXVP47rKCfBfHKCZYsztFE7W8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/insta/instagram_5.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\insta\instagram_5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ODEbd4yzEIoduwl6n5VXVP47rKCfBfHKCZYsztFE7W8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"235602"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022ODEbd4yzEIoduwl6n5VXVP47rKCfBfHKCZYsztFE7W8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/insta/instagram_6.4jrihc4ah4.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\insta\instagram_6.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4jrihc4ah4"},{"Name":"integrity","Value":"sha256-4HzMY\u002BL6AP1PxhBz3Dgdhg9wjE615nElH7fCXtpIPCU="},{"Name":"label","Value":"_content/Roya/img/insta/instagram_6.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"178400"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00224HzMY\u002BL6AP1PxhBz3Dgdhg9wjE615nElH7fCXtpIPCU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/insta/instagram_6.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\insta\instagram_6.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4HzMY\u002BL6AP1PxhBz3Dgdhg9wjE615nElH7fCXtpIPCU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"178400"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00224HzMY\u002BL6AP1PxhBz3Dgdhg9wjE615nElH7fCXtpIPCU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/learning_img.chs7afukql.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\learning_img.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"chs7afukql"},{"Name":"integrity","Value":"sha256-8FFkz28Z6rYRQCFLt6ypS35BZAcwIJbz36uVTxXwppI="},{"Name":"label","Value":"_content/Roya/img/learning_img.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"51532"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00228FFkz28Z6rYRQCFLt6ypS35BZAcwIJbz36uVTxXwppI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/learning_img.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\learning_img.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8FFkz28Z6rYRQCFLt6ypS35BZAcwIJbz36uVTxXwppI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"51532"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00228FFkz28Z6rYRQCFLt6ypS35BZAcwIJbz36uVTxXwppI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/learning_img_bg.k2ojdrvyku.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\learning_img_bg.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"k2ojdrvyku"},{"Name":"integrity","Value":"sha256-ZBbyE0RObbWq/MUPA/6npzndkRsM0elgZgZoWLfSuBY="},{"Name":"label","Value":"_content/Roya/img/learning_img_bg.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11164"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022ZBbyE0RObbWq/MUPA/6npzndkRsM0elgZgZoWLfSuBY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/learning_img_bg.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\learning_img_bg.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ZBbyE0RObbWq/MUPA/6npzndkRsM0elgZgZoWLfSuBY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"11164"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022ZBbyE0RObbWq/MUPA/6npzndkRsM0elgZgZoWLfSuBY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/logo.7hgyttcxlf.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\logo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7hgyttcxlf"},{"Name":"integrity","Value":"sha256-61hQZLW\u002B68luqQvTPKAQhvoZkmVM\u002BcWGbCeL9WutSWY="},{"Name":"label","Value":"_content/Roya/img/logo.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4197"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u002261hQZLW\u002B68luqQvTPKAQhvoZkmVM\u002BcWGbCeL9WutSWY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/logo.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\logo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-61hQZLW\u002B68luqQvTPKAQhvoZkmVM\u002BcWGbCeL9WutSWY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4197"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u002261hQZLW\u002B68luqQvTPKAQhvoZkmVM\u002BcWGbCeL9WutSWY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/next.0g5ny4e5sv.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\next.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0g5ny4e5sv"},{"Name":"integrity","Value":"sha256-sHJ71e37vI36BP2DV6i2JUxn\u002BrmC8pv4\u002BAoF3EjEzjw="},{"Name":"label","Value":"_content/Roya/img/post/next.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"8977"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022sHJ71e37vI36BP2DV6i2JUxn\u002BrmC8pv4\u002BAoF3EjEzjw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/next.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\next.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-sHJ71e37vI36BP2DV6i2JUxn\u002BrmC8pv4\u002BAoF3EjEzjw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"8977"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022sHJ71e37vI36BP2DV6i2JUxn\u002BrmC8pv4\u002BAoF3EjEzjw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_1.fszaellkvs.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fszaellkvs"},{"Name":"integrity","Value":"sha256-r2VGoC7cThIpOIV4MVYdp9lyfT/XQapxFVIbcvvVhrY="},{"Name":"label","Value":"_content/Roya/img/post/post_1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16236"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022r2VGoC7cThIpOIV4MVYdp9lyfT/XQapxFVIbcvvVhrY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-r2VGoC7cThIpOIV4MVYdp9lyfT/XQapxFVIbcvvVhrY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"16236"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022r2VGoC7cThIpOIV4MVYdp9lyfT/XQapxFVIbcvvVhrY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_10.7lspsm0xt4.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_10.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7lspsm0xt4"},{"Name":"integrity","Value":"sha256-Ba\u002ByJVtSY4CTyD33CZebvGikIHFktug1EiFQq72Qfh4="},{"Name":"label","Value":"_content/Roya/img/post/post_10.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"19266"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Ba\u002ByJVtSY4CTyD33CZebvGikIHFktug1EiFQq72Qfh4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_10.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_10.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Ba\u002ByJVtSY4CTyD33CZebvGikIHFktug1EiFQq72Qfh4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"19266"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Ba\u002ByJVtSY4CTyD33CZebvGikIHFktug1EiFQq72Qfh4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Aaqi\u002BeT2M\u002B5LGuANGqdhlBDdF5mfGcE52dltI027RdU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"13468"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Aaqi\u002BeT2M\u002B5LGuANGqdhlBDdF5mfGcE52dltI027RdU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_2.pyu9pfxt1v.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pyu9pfxt1v"},{"Name":"integrity","Value":"sha256-Aaqi\u002BeT2M\u002B5LGuANGqdhlBDdF5mfGcE52dltI027RdU="},{"Name":"label","Value":"_content/Roya/img/post/post_2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"13468"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Aaqi\u002BeT2M\u002B5LGuANGqdhlBDdF5mfGcE52dltI027RdU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_3.4tdbugnfx6.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4tdbugnfx6"},{"Name":"integrity","Value":"sha256-WJezmcQm/pTvjfveIFCypzjmBYLUJbGFscnW1Yms3aI="},{"Name":"label","Value":"_content/Roya/img/post/post_3.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"14552"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022WJezmcQm/pTvjfveIFCypzjmBYLUJbGFscnW1Yms3aI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-WJezmcQm/pTvjfveIFCypzjmBYLUJbGFscnW1Yms3aI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"14552"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022WJezmcQm/pTvjfveIFCypzjmBYLUJbGFscnW1Yms3aI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_4.bio6twnbkp.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bio6twnbkp"},{"Name":"integrity","Value":"sha256-bVHQyksOUP2ZKvCAXK8FGFVefBFIl7j/DVO2iAtQe\u002Bo="},{"Name":"label","Value":"_content/Roya/img/post/post_4.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11818"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022bVHQyksOUP2ZKvCAXK8FGFVefBFIl7j/DVO2iAtQe\u002Bo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_4.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-bVHQyksOUP2ZKvCAXK8FGFVefBFIl7j/DVO2iAtQe\u002Bo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"11818"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022bVHQyksOUP2ZKvCAXK8FGFVefBFIl7j/DVO2iAtQe\u002Bo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_5.jebkf1u0z3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jebkf1u0z3"},{"Name":"integrity","Value":"sha256-RZbzmRc/BovhkS8IIEsGvvePVPH/HdpzEsrjlEP/yGc="},{"Name":"label","Value":"_content/Roya/img/post/post_5.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"21217"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022RZbzmRc/BovhkS8IIEsGvvePVPH/HdpzEsrjlEP/yGc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_5.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-RZbzmRc/BovhkS8IIEsGvvePVPH/HdpzEsrjlEP/yGc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"21217"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022RZbzmRc/BovhkS8IIEsGvvePVPH/HdpzEsrjlEP/yGc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_6.lvx6z6tbkw.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_6.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lvx6z6tbkw"},{"Name":"integrity","Value":"sha256-/6OUN0e7VEy6nZtoFzRb6iIubeTcfMXmjpjmST8nWzM="},{"Name":"label","Value":"_content/Roya/img/post/post_6.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"17631"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022/6OUN0e7VEy6nZtoFzRb6iIubeTcfMXmjpjmST8nWzM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_6.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_6.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/6OUN0e7VEy6nZtoFzRb6iIubeTcfMXmjpjmST8nWzM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"17631"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022/6OUN0e7VEy6nZtoFzRb6iIubeTcfMXmjpjmST8nWzM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_7.mlzb7hr4vn.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_7.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mlzb7hr4vn"},{"Name":"integrity","Value":"sha256-Bnw00zMK7Y/wRy2qX7RpOazVOgJ/UowYcpyxMyphWEw="},{"Name":"label","Value":"_content/Roya/img/post/post_7.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20969"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Bnw00zMK7Y/wRy2qX7RpOazVOgJ/UowYcpyxMyphWEw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_7.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_7.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Bnw00zMK7Y/wRy2qX7RpOazVOgJ/UowYcpyxMyphWEw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"20969"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Bnw00zMK7Y/wRy2qX7RpOazVOgJ/UowYcpyxMyphWEw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_8.kftp1o3x2s.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_8.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kftp1o3x2s"},{"Name":"integrity","Value":"sha256-QNLgvio4iBYwxh\u002BLctZENhLc/NkKENrOzLLscAPVUnQ="},{"Name":"label","Value":"_content/Roya/img/post/post_8.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16499"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022QNLgvio4iBYwxh\u002BLctZENhLc/NkKENrOzLLscAPVUnQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_8.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_8.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-QNLgvio4iBYwxh\u002BLctZENhLc/NkKENrOzLLscAPVUnQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"16499"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022QNLgvio4iBYwxh\u002BLctZENhLc/NkKENrOzLLscAPVUnQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_9.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_9.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-\u002B4w/onDyw9IVHb2/J/a2PEwMmnjSfO7Bm\u002Bk00yPCHjE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"17231"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022\u002B4w/onDyw9IVHb2/J/a2PEwMmnjSfO7Bm\u002Bk00yPCHjE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/post_9.v76eeaw1c2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\post_9.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"v76eeaw1c2"},{"Name":"integrity","Value":"sha256-\u002B4w/onDyw9IVHb2/J/a2PEwMmnjSfO7Bm\u002Bk00yPCHjE="},{"Name":"label","Value":"_content/Roya/img/post/post_9.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"17231"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022\u002B4w/onDyw9IVHb2/J/a2PEwMmnjSfO7Bm\u002Bk00yPCHjE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/preview.5aw53rldsc.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\preview.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5aw53rldsc"},{"Name":"integrity","Value":"sha256-Uw6\u002Bc3TBN6\u002BQRkx1H7fkEdACBQHzaJvVyFYSOv\u002BpJPY="},{"Name":"label","Value":"_content/Roya/img/post/preview.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"9547"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Uw6\u002Bc3TBN6\u002BQRkx1H7fkEdACBQHzaJvVyFYSOv\u002BpJPY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/post/preview.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\post\preview.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Uw6\u002Bc3TBN6\u002BQRkx1H7fkEdACBQHzaJvVyFYSOv\u002BpJPY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"9547"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Uw6\u002Bc3TBN6\u002BQRkx1H7fkEdACBQHzaJvVyFYSOv\u002BpJPY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/quote.fj0jtzayzj.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\quote.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fj0jtzayzj"},{"Name":"integrity","Value":"sha256-ha2M8clGwTz764ckFlUGeX8V20LS9Gmm2HXWpi32dzo="},{"Name":"label","Value":"_content/Roya/img/quote.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4508"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022ha2M8clGwTz764ckFlUGeX8V20LS9Gmm2HXWpi32dzo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/quote.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\quote.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ha2M8clGwTz764ckFlUGeX8V20LS9Gmm2HXWpi32dzo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4508"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022ha2M8clGwTz764ckFlUGeX8V20LS9Gmm2HXWpi32dzo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/service_bg_2.j0vceal3tb.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\service_bg_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"j0vceal3tb"},{"Name":"integrity","Value":"sha256-/ZG1x9xnRlDJxANyVhlCdkekvH6cE81N7oTcMXrgngY="},{"Name":"label","Value":"_content/Roya/img/service_bg_2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"9401"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022/ZG1x9xnRlDJxANyVhlCdkekvH6cE81N7oTcMXrgngY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/service_bg_2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\service_bg_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/ZG1x9xnRlDJxANyVhlCdkekvH6cE81N7oTcMXrgngY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"9401"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022/ZG1x9xnRlDJxANyVhlCdkekvH6cE81N7oTcMXrgngY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/single_cource.3gd0n3c6hv.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\single_cource.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3gd0n3c6hv"},{"Name":"integrity","Value":"sha256-E\u002BfUWkMiNuNuMwVbALZhhbe4C5Ec19GbuRM3YZfAVDk="},{"Name":"label","Value":"_content/Roya/img/single_cource.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"577456"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022E\u002BfUWkMiNuNuMwVbALZhhbe4C5Ec19GbuRM3YZfAVDk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/single_cource.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\single_cource.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-E\u002BfUWkMiNuNuMwVbALZhhbe4C5Ec19GbuRM3YZfAVDk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"577456"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022E\u002BfUWkMiNuNuMwVbALZhhbe4C5Ec19GbuRM3YZfAVDk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/single_page_logo.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\single_page_logo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-c0wDsEkrbUtwcaZUz8UQ8bLHhmvdJzj15GvR3/VLKiQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4123"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022c0wDsEkrbUtwcaZUz8UQ8bLHhmvdJzj15GvR3/VLKiQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/single_page_logo.zoz31tozu6.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\single_page_logo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zoz31tozu6"},{"Name":"integrity","Value":"sha256-c0wDsEkrbUtwcaZUz8UQ8bLHhmvdJzj15GvR3/VLKiQ="},{"Name":"label","Value":"_content/Roya/img/single_page_logo.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4123"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022c0wDsEkrbUtwcaZUz8UQ8bLHhmvdJzj15GvR3/VLKiQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/single_project.in1v9rjlib.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\single_project.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"in1v9rjlib"},{"Name":"integrity","Value":"sha256-Hn8DmTB\u002BXEgVRKuGAueEeCK1D52YeOvxGhPSNs4TqbA="},{"Name":"label","Value":"_content/Roya/img/single_project.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"517101"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Hn8DmTB\u002BXEgVRKuGAueEeCK1D52YeOvxGhPSNs4TqbA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/single_project.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\single_project.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Hn8DmTB\u002BXEgVRKuGAueEeCK1D52YeOvxGhPSNs4TqbA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"517101"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Hn8DmTB\u002BXEgVRKuGAueEeCK1D52YeOvxGhPSNs4TqbA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/special_cource_1.oiobrflvj1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\special_cource_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"oiobrflvj1"},{"Name":"integrity","Value":"sha256-IAi361T6ZTjq/uX8dCC0in1alw0JG60\u002BfZyGobcVxCQ="},{"Name":"label","Value":"_content/Roya/img/special_cource_1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"211604"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022IAi361T6ZTjq/uX8dCC0in1alw0JG60\u002BfZyGobcVxCQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/special_cource_1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\special_cource_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-IAi361T6ZTjq/uX8dCC0in1alw0JG60\u002BfZyGobcVxCQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"211604"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022IAi361T6ZTjq/uX8dCC0in1alw0JG60\u002BfZyGobcVxCQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/special_cource_2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\special_cource_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b0UredUSb\u002BqrS2NHuP4nJlRM/whwN\u002BCye4M6L9ejXeM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"183012"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022b0UredUSb\u002BqrS2NHuP4nJlRM/whwN\u002BCye4M6L9ejXeM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/special_cource_2.tprhovmfqr.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\special_cource_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tprhovmfqr"},{"Name":"integrity","Value":"sha256-b0UredUSb\u002BqrS2NHuP4nJlRM/whwN\u002BCye4M6L9ejXeM="},{"Name":"label","Value":"_content/Roya/img/special_cource_2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"183012"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022b0UredUSb\u002BqrS2NHuP4nJlRM/whwN\u002BCye4M6L9ejXeM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/special_cource_3.3hqwgj83r5.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\special_cource_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3hqwgj83r5"},{"Name":"integrity","Value":"sha256-B8q9Mlx3lrpPW32ZshZmf3SummT15IakwDHjz7gaPK8="},{"Name":"label","Value":"_content/Roya/img/special_cource_3.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"218909"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022B8q9Mlx3lrpPW32ZshZmf3SummT15IakwDHjz7gaPK8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/special_cource_3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\special_cource_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-B8q9Mlx3lrpPW32ZshZmf3SummT15IakwDHjz7gaPK8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"218909"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022B8q9Mlx3lrpPW32ZshZmf3SummT15IakwDHjz7gaPK8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/team/team_1.nsycv1udf1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\team\team_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"nsycv1udf1"},{"Name":"integrity","Value":"sha256-J34\u002BfGtJnvlyYefCobCvF5P3DwfgI9u1C5DhYpO5HXQ="},{"Name":"label","Value":"_content/Roya/img/team/team_1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"122207"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022J34\u002BfGtJnvlyYefCobCvF5P3DwfgI9u1C5DhYpO5HXQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/team/team_1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\team\team_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-J34\u002BfGtJnvlyYefCobCvF5P3DwfgI9u1C5DhYpO5HXQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"122207"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022J34\u002BfGtJnvlyYefCobCvF5P3DwfgI9u1C5DhYpO5HXQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/team/team_2.loyjs9msb4.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\team\team_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"loyjs9msb4"},{"Name":"integrity","Value":"sha256-VfbAlISvAmwB\u002Bjf3kKnpgejYgP6/CPu\u002ByAxPQGgi6Wc="},{"Name":"label","Value":"_content/Roya/img/team/team_2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"137301"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022VfbAlISvAmwB\u002Bjf3kKnpgejYgP6/CPu\u002ByAxPQGgi6Wc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/team/team_2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\team\team_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-VfbAlISvAmwB\u002Bjf3kKnpgejYgP6/CPu\u002ByAxPQGgi6Wc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"137301"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022VfbAlISvAmwB\u002Bjf3kKnpgejYgP6/CPu\u002ByAxPQGgi6Wc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/team/team_3.ja4ameojvz.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\team\team_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ja4ameojvz"},{"Name":"integrity","Value":"sha256-xb383rWd75CTTg1Ogjt5ZbUnEXD/JoJ6B0W\u002Bwm\u002Bjpbs="},{"Name":"label","Value":"_content/Roya/img/team/team_3.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"140976"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022xb383rWd75CTTg1Ogjt5ZbUnEXD/JoJ6B0W\u002Bwm\u002Bjpbs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/team/team_3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\team\team_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-xb383rWd75CTTg1Ogjt5ZbUnEXD/JoJ6B0W\u002Bwm\u002Bjpbs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"140976"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022xb383rWd75CTTg1Ogjt5ZbUnEXD/JoJ6B0W\u002Bwm\u002Bjpbs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/team/team_4.h89v50oy7v.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\team\team_4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"h89v50oy7v"},{"Name":"integrity","Value":"sha256-gWVXFM\u002B\u002BRH1NtM7\u002BOfJlsP/Nbazgjm5m\u002BJGgwH7XJkQ="},{"Name":"label","Value":"_content/Roya/img/team/team_4.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"159181"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022gWVXFM\u002B\u002BRH1NtM7\u002BOfJlsP/Nbazgjm5m\u002BJGgwH7XJkQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/team/team_4.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\team\team_4.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gWVXFM\u002B\u002BRH1NtM7\u002BOfJlsP/Nbazgjm5m\u002BJGgwH7XJkQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"159181"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022gWVXFM\u002B\u002BRH1NtM7\u002BOfJlsP/Nbazgjm5m\u002BJGgwH7XJkQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/testimonial_img_1.lmlysircv8.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\testimonial_img_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lmlysircv8"},{"Name":"integrity","Value":"sha256-A4oJlF0bOLjAsSvcAdCfJMjTeG22RUHIl2JPtk7Y1pA="},{"Name":"label","Value":"_content/Roya/img/testimonial_img_1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"107510"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022A4oJlF0bOLjAsSvcAdCfJMjTeG22RUHIl2JPtk7Y1pA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/testimonial_img_1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\testimonial_img_1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-A4oJlF0bOLjAsSvcAdCfJMjTeG22RUHIl2JPtk7Y1pA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"107510"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022A4oJlF0bOLjAsSvcAdCfJMjTeG22RUHIl2JPtk7Y1pA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/testimonial_img_2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\testimonial_img_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-r5m2GhsTtfP\u002BHwZD3NN1xbP7fBMZvpFbxfl2\u002B6tQaQM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"177785"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022r5m2GhsTtfP\u002BHwZD3NN1xbP7fBMZvpFbxfl2\u002B6tQaQM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/testimonial_img_2.py7srli3sn.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\testimonial_img_2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"py7srli3sn"},{"Name":"integrity","Value":"sha256-r5m2GhsTtfP\u002BHwZD3NN1xbP7fBMZvpFbxfl2\u002B6tQaQM="},{"Name":"label","Value":"_content/Roya/img/testimonial_img_2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"177785"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022r5m2GhsTtfP\u002BHwZD3NN1xbP7fBMZvpFbxfl2\u002B6tQaQM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/testimonial_img_3.2wv7p85yt2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\testimonial_img_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2wv7p85yt2"},{"Name":"integrity","Value":"sha256-MkaAv9Ks8nFBaIfHHjuoOn6sh7sVybkkiMw7PJiE\u002B6s="},{"Name":"label","Value":"_content/Roya/img/testimonial_img_3.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"170046"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022MkaAv9Ks8nFBaIfHHjuoOn6sh7sVybkkiMw7PJiE\u002B6s=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/img/testimonial_img_3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\img\testimonial_img_3.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MkaAv9Ks8nFBaIfHHjuoOn6sh7sVybkkiMw7PJiE\u002B6s="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"170046"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022MkaAv9Ks8nFBaIfHHjuoOn6sh7sVybkkiMw7PJiE\u002B6s=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:30 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/js/datatables-simple-demo.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\datatables-simple-demo.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-A5r4X24Y4UOeA7FBC6HS3voj76xkmczywh9qO\u002BLy5hQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"312"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022A5r4X24Y4UOeA7FBC6HS3voj76xkmczywh9qO\u002BLy5hQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 21 May 2025 22:55:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/js/datatables-simple-demo.xnqwbb6zwd.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\datatables-simple-demo.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"xnqwbb6zwd"},{"Name":"integrity","Value":"sha256-A5r4X24Y4UOeA7FBC6HS3voj76xkmczywh9qO\u002BLy5hQ="},{"Name":"label","Value":"_content/Roya/js/datatables-simple-demo.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"312"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022A5r4X24Y4UOeA7FBC6HS3voj76xkmczywh9qO\u002BLy5hQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 21 May 2025 22:55:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/js/scripts.dqle62frat.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\scripts.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dqle62frat"},{"Name":"integrity","Value":"sha256-hSOKtaoLbtAX3QLxogVcaVg8Fv7Y6HTCcpJqVePv720="},{"Name":"label","Value":"_content/Roya/js/scripts.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"976"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022hSOKtaoLbtAX3QLxogVcaVg8Fv7Y6HTCcpJqVePv720=\u0022"},{"Name":"Last-Modified","Value":"Wed, 21 May 2025 22:55:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/js/scripts.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\scripts.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hSOKtaoLbtAX3QLxogVcaVg8Fv7Y6HTCcpJqVePv720="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"976"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022hSOKtaoLbtAX3QLxogVcaVg8Fv7Y6HTCcpJqVePv720=\u0022"},{"Name":"Last-Modified","Value":"Wed, 21 May 2025 22:55:52 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/aos.0ztkn9dbyg.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\aos.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0ztkn9dbyg"},{"Name":"integrity","Value":"sha256-RGDxWWF00GzKlX/ayixx4aN3zx1vB\u002B5Mdf\u002Bzvz/JegM="},{"Name":"label","Value":"_content/Roya/jsvist/aos.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"14243"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022RGDxWWF00GzKlX/ayixx4aN3zx1vB\u002B5Mdf\u002Bzvz/JegM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/aos.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\aos.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-RGDxWWF00GzKlX/ayixx4aN3zx1vB\u002B5Mdf\u002Bzvz/JegM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"14243"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022RGDxWWF00GzKlX/ayixx4aN3zx1vB\u002B5Mdf\u002Bzvz/JegM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/bootstrap.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\bootstrap.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CjSoeELFOcH0/uxWu6mC/Vlrc1AARqbm/jiiImDGV3s="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"58072"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022CjSoeELFOcH0/uxWu6mC/Vlrc1AARqbm/jiiImDGV3s=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/bootstrap.min.m8so7gk97h.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\bootstrap.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"m8so7gk97h"},{"Name":"integrity","Value":"sha256-CjSoeELFOcH0/uxWu6mC/Vlrc1AARqbm/jiiImDGV3s="},{"Name":"label","Value":"_content/Roya/jsvist/bootstrap.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"58072"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022CjSoeELFOcH0/uxWu6mC/Vlrc1AARqbm/jiiImDGV3s=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/contact.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\contact.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-aRz1f9\u002BFqP/bqUGfnfCdF\u002BsE/ieKsCdjbaCmn3MZ8ng="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3024"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022aRz1f9\u002BFqP/bqUGfnfCdF\u002BsE/ieKsCdjbaCmn3MZ8ng=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/contact.v2bq9sl1hr.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\contact.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"v2bq9sl1hr"},{"Name":"integrity","Value":"sha256-aRz1f9\u002BFqP/bqUGfnfCdF\u002BsE/ieKsCdjbaCmn3MZ8ng="},{"Name":"label","Value":"_content/Roya/jsvist/contact.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3024"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022aRz1f9\u002BFqP/bqUGfnfCdF\u002BsE/ieKsCdjbaCmn3MZ8ng=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/custom.htdm3j9a34.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\custom.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"htdm3j9a34"},{"Name":"integrity","Value":"sha256-dQ6QWkqTqIMv2nRuvWQFkPOe1fJLVbehx29mfWBSNXg="},{"Name":"label","Value":"_content/Roya/jsvist/custom.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3815"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022dQ6QWkqTqIMv2nRuvWQFkPOe1fJLVbehx29mfWBSNXg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/custom.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\custom.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dQ6QWkqTqIMv2nRuvWQFkPOe1fJLVbehx29mfWBSNXg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3815"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022dQ6QWkqTqIMv2nRuvWQFkPOe1fJLVbehx29mfWBSNXg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/gmaps.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\gmaps.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-C3vPkcHJk9t5Pi6C\u002BNc\u002Bfaqv/EZZqNBnxoDfTXEorQk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"31794"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022C3vPkcHJk9t5Pi6C\u002BNc\u002Bfaqv/EZZqNBnxoDfTXEorQk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/gmaps.min.z69e9n3jap.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\gmaps.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z69e9n3jap"},{"Name":"integrity","Value":"sha256-C3vPkcHJk9t5Pi6C\u002BNc\u002Bfaqv/EZZqNBnxoDfTXEorQk="},{"Name":"label","Value":"_content/Roya/jsvist/gmaps.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"31794"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022C3vPkcHJk9t5Pi6C\u002BNc\u002Bfaqv/EZZqNBnxoDfTXEorQk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery-1.12.1.min.byhlrw1upa.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery-1.12.1.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"byhlrw1upa"},{"Name":"integrity","Value":"sha256-I1nTg78tSrZev3kjvfdM5A5Ak/blglGzlaZANLPDl3I="},{"Name":"label","Value":"_content/Roya/jsvist/jquery-1.12.1.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"97403"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022I1nTg78tSrZev3kjvfdM5A5Ak/blglGzlaZANLPDl3I=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery-1.12.1.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery-1.12.1.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-I1nTg78tSrZev3kjvfdM5A5Ak/blglGzlaZANLPDl3I="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"97403"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022I1nTg78tSrZev3kjvfdM5A5Ak/blglGzlaZANLPDl3I=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery-3.3.1.slim.min.7fg0hl6hkg.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery-3.3.1.slim.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7fg0hl6hkg"},{"Name":"integrity","Value":"sha256-3edrmyuQ0w65f8gfBsqowzjJe2iM6n0nKciPUp8y\u002B7E="},{"Name":"label","Value":"_content/Roya/jsvist/jquery-3.3.1.slim.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"69917"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00223edrmyuQ0w65f8gfBsqowzjJe2iM6n0nKciPUp8y\u002B7E=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery-3.3.1.slim.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery-3.3.1.slim.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-3edrmyuQ0w65f8gfBsqowzjJe2iM6n0nKciPUp8y\u002B7E="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"69917"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00223edrmyuQ0w65f8gfBsqowzjJe2iM6n0nKciPUp8y\u002B7E=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery.ajaxchimp.min.87ka3f0y8m.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery.ajaxchimp.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"87ka3f0y8m"},{"Name":"integrity","Value":"sha256-PHwXRZ7FeGf2gSYl8bleL4eDY8pyi5Kllo0vyOYNlxI="},{"Name":"label","Value":"_content/Roya/jsvist/jquery.ajaxchimp.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4820"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022PHwXRZ7FeGf2gSYl8bleL4eDY8pyi5Kllo0vyOYNlxI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery.ajaxchimp.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery.ajaxchimp.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-PHwXRZ7FeGf2gSYl8bleL4eDY8pyi5Kllo0vyOYNlxI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4820"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022PHwXRZ7FeGf2gSYl8bleL4eDY8pyi5Kllo0vyOYNlxI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery.counterup.min.gsua8nsoy9.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery.counterup.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gsua8nsoy9"},{"Name":"integrity","Value":"sha256-YD47YGHEn4wC8H5RUsXXOB3vuhDI/nP5UJDhmnjeA5c="},{"Name":"label","Value":"_content/Roya/jsvist/jquery.counterup.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1071"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022YD47YGHEn4wC8H5RUsXXOB3vuhDI/nP5UJDhmnjeA5c=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery.counterup.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery.counterup.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YD47YGHEn4wC8H5RUsXXOB3vuhDI/nP5UJDhmnjeA5c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1071"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022YD47YGHEn4wC8H5RUsXXOB3vuhDI/nP5UJDhmnjeA5c=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery.easing.min.g9gb8xza5s.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery.easing.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"g9gb8xza5s"},{"Name":"integrity","Value":"sha256-7PwYPjPSXSSqfAYhjgpBNIj/\u002BHdOS0uHVDx2bbmwuLo="},{"Name":"label","Value":"_content/Roya/jsvist/jquery.easing.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5564"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00227PwYPjPSXSSqfAYhjgpBNIj/\u002BHdOS0uHVDx2bbmwuLo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery.easing.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery.easing.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-7PwYPjPSXSSqfAYhjgpBNIj/\u002BHdOS0uHVDx2bbmwuLo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5564"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00227PwYPjPSXSSqfAYhjgpBNIj/\u002BHdOS0uHVDx2bbmwuLo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery.form.8zqera5a4p.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery.form.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"8zqera5a4p"},{"Name":"integrity","Value":"sha256-FLMKk042zfxTAXCb9RWtwx4GC9cnXrBWlkz30u1udVA="},{"Name":"label","Value":"_content/Roya/jsvist/jquery.form.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"41095"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022FLMKk042zfxTAXCb9RWtwx4GC9cnXrBWlkz30u1udVA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery.form.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery.form.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-FLMKk042zfxTAXCb9RWtwx4GC9cnXrBWlkz30u1udVA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"41095"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022FLMKk042zfxTAXCb9RWtwx4GC9cnXrBWlkz30u1udVA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery.magnific-popup.9ugm0d1n88.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery.magnific-popup.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9ugm0d1n88"},{"Name":"integrity","Value":"sha256-P93G0oq6PBPWTP1IR8Mz/0jHHUpaWL0aBJTKauisG7Q="},{"Name":"label","Value":"_content/Roya/jsvist/jquery.magnific-popup.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20216"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022P93G0oq6PBPWTP1IR8Mz/0jHHUpaWL0aBJTKauisG7Q=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery.magnific-popup.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery.magnific-popup.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-P93G0oq6PBPWTP1IR8Mz/0jHHUpaWL0aBJTKauisG7Q="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20216"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022P93G0oq6PBPWTP1IR8Mz/0jHHUpaWL0aBJTKauisG7Q=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery.nice-select.min.eix7w2fz8h.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery.nice-select.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"eix7w2fz8h"},{"Name":"integrity","Value":"sha256-Zr3vByTlMGQhvMfgkQ5BtWRSKBGa2QlspKYJnkjZTmo="},{"Name":"label","Value":"_content/Roya/jsvist/jquery.nice-select.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2942"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022Zr3vByTlMGQhvMfgkQ5BtWRSKBGa2QlspKYJnkjZTmo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery.nice-select.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery.nice-select.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Zr3vByTlMGQhvMfgkQ5BtWRSKBGa2QlspKYJnkjZTmo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2942"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022Zr3vByTlMGQhvMfgkQ5BtWRSKBGa2QlspKYJnkjZTmo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery.validate.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery.validate.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dwX\u002B4TQXIp1xjxSUfphg1bsrJb0VyfXNg08lRce60KY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"21068"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022dwX\u002B4TQXIp1xjxSUfphg1bsrJb0VyfXNg08lRce60KY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/jquery.validate.min.ve6oznthgw.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\jquery.validate.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ve6oznthgw"},{"Name":"integrity","Value":"sha256-dwX\u002B4TQXIp1xjxSUfphg1bsrJb0VyfXNg08lRce60KY="},{"Name":"label","Value":"_content/Roya/jsvist/jquery.validate.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"21068"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022dwX\u002B4TQXIp1xjxSUfphg1bsrJb0VyfXNg08lRce60KY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/mail-script.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\mail-script.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-AfY5/UxBGVA\u002BcuK/LrnIpZhPfIPHaDyC3QNQ7p9js/0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1230"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022AfY5/UxBGVA\u002BcuK/LrnIpZhPfIPHaDyC3QNQ7p9js/0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/mail-script.py6vtxmges.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\mail-script.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"py6vtxmges"},{"Name":"integrity","Value":"sha256-AfY5/UxBGVA\u002BcuK/LrnIpZhPfIPHaDyC3QNQ7p9js/0="},{"Name":"label","Value":"_content/Roya/jsvist/mail-script.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1230"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022AfY5/UxBGVA\u002BcuK/LrnIpZhPfIPHaDyC3QNQ7p9js/0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/masonry.pkgd.3xm4y7tfy7.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\masonry.pkgd.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3xm4y7tfy7"},{"Name":"integrity","Value":"sha256-B7td5cMYv\u002BNH9ELBd6giqg7AfrYQ4HPG9HR0wGTQ\u002BCU="},{"Name":"label","Value":"_content/Roya/jsvist/masonry.pkgd.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"63316"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022B7td5cMYv\u002BNH9ELBd6giqg7AfrYQ4HPG9HR0wGTQ\u002BCU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/masonry.pkgd.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\masonry.pkgd.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-B7td5cMYv\u002BNH9ELBd6giqg7AfrYQ4HPG9HR0wGTQ\u002BCU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"63316"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022B7td5cMYv\u002BNH9ELBd6giqg7AfrYQ4HPG9HR0wGTQ\u002BCU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/masonry.pkgd.min.d04h01n2i5.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\masonry.pkgd.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"d04h01n2i5"},{"Name":"integrity","Value":"sha256-cz18JqX7ckDoPoryyCIhizIbUUPijC3WWrJJIpesa9c="},{"Name":"label","Value":"_content/Roya/jsvist/masonry.pkgd.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"28953"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022cz18JqX7ckDoPoryyCIhizIbUUPijC3WWrJJIpesa9c=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/masonry.pkgd.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\masonry.pkgd.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-cz18JqX7ckDoPoryyCIhizIbUUPijC3WWrJJIpesa9c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"28953"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022cz18JqX7ckDoPoryyCIhizIbUUPijC3WWrJJIpesa9c=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/owl.carousel.min.hevc79pcik.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\owl.carousel.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hevc79pcik"},{"Name":"integrity","Value":"sha256-Bxv\u002BIGPWKrDqEyQniXj5vKIypcxR4AcUmpZZq3HWkxo="},{"Name":"label","Value":"_content/Roya/jsvist/owl.carousel.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"44245"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022Bxv\u002BIGPWKrDqEyQniXj5vKIypcxR4AcUmpZZq3HWkxo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/owl.carousel.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\owl.carousel.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Bxv\u002BIGPWKrDqEyQniXj5vKIypcxR4AcUmpZZq3HWkxo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"44245"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022Bxv\u002BIGPWKrDqEyQniXj5vKIypcxR4AcUmpZZq3HWkxo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/particles.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\particles.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-\u002Bu54FaX9J\u002Bk40eAcg5K2YzICSQjrEYBI9gju5nE3HfY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"23364"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022\u002Bu54FaX9J\u002Bk40eAcg5K2YzICSQjrEYBI9gju5nE3HfY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/particles.min.y2trbij6sz.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\particles.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"y2trbij6sz"},{"Name":"integrity","Value":"sha256-\u002Bu54FaX9J\u002Bk40eAcg5K2YzICSQjrEYBI9gju5nE3HfY="},{"Name":"label","Value":"_content/Roya/jsvist/particles.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"23364"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022\u002Bu54FaX9J\u002Bk40eAcg5K2YzICSQjrEYBI9gju5nE3HfY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/popper.min.ilm83z02t0.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\popper.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ilm83z02t0"},{"Name":"integrity","Value":"sha256-ZvOgfh\u002Bptkpoa2Y4HkRY28ir89u/\u002BVRyDE7sB7hEEcI="},{"Name":"label","Value":"_content/Roya/jsvist/popper.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"21004"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022ZvOgfh\u002Bptkpoa2Y4HkRY28ir89u/\u002BVRyDE7sB7hEEcI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/popper.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\popper.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ZvOgfh\u002Bptkpoa2Y4HkRY28ir89u/\u002BVRyDE7sB7hEEcI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"21004"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022ZvOgfh\u002Bptkpoa2Y4HkRY28ir89u/\u002BVRyDE7sB7hEEcI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/slick.min.60v6x95242.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\slick.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"60v6x95242"},{"Name":"integrity","Value":"sha256-isiJEI9aKZGKlAAm1PGY4rwov4Q0shJ0k714Ry8HbpI="},{"Name":"label","Value":"_content/Roya/jsvist/slick.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"33293"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022isiJEI9aKZGKlAAm1PGY4rwov4Q0shJ0k714Ry8HbpI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/slick.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\slick.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-isiJEI9aKZGKlAAm1PGY4rwov4Q0shJ0k714Ry8HbpI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"33293"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022isiJEI9aKZGKlAAm1PGY4rwov4Q0shJ0k714Ry8HbpI=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/swiper.min.ddzknr22un.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\swiper.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ddzknr22un"},{"Name":"integrity","Value":"sha256-76xv7CukN7apBuJJ\u002BtnePH08EFpIE2sBVTdrWYnE12o="},{"Name":"label","Value":"_content/Roya/jsvist/swiper.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"122735"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u002276xv7CukN7apBuJJ\u002BtnePH08EFpIE2sBVTdrWYnE12o=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/swiper.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\swiper.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-76xv7CukN7apBuJJ\u002BtnePH08EFpIE2sBVTdrWYnE12o="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"122735"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u002276xv7CukN7apBuJJ\u002BtnePH08EFpIE2sBVTdrWYnE12o=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/swiper_custom.6by7z09x4d.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\swiper_custom.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6by7z09x4d"},{"Name":"integrity","Value":"sha256-cnzMWOFj9cVF\u002B4VpW8\u002BRD\u002BOlsyP82ERX0m5JN7cOV\u002Bw="},{"Name":"label","Value":"_content/Roya/jsvist/swiper_custom.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"595"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022cnzMWOFj9cVF\u002B4VpW8\u002BRD\u002BOlsyP82ERX0m5JN7cOV\u002Bw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/swiper_custom.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\swiper_custom.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-cnzMWOFj9cVF\u002B4VpW8\u002BRD\u002BOlsyP82ERX0m5JN7cOV\u002Bw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"595"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022cnzMWOFj9cVF\u002B4VpW8\u002BRD\u002BOlsyP82ERX0m5JN7cOV\u002Bw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/waypoints.min.0snsn14les.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\waypoints.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0snsn14les"},{"Name":"integrity","Value":"sha256-oP3taRrtdn\u002BFEBHNMYW5KGGSmKIaD72tSAip6ItJCDM="},{"Name":"label","Value":"_content/Roya/jsvist/waypoints.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"8044"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022oP3taRrtdn\u002BFEBHNMYW5KGGSmKIaD72tSAip6ItJCDM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/jsvist/waypoints.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\jsvist\waypoints.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-oP3taRrtdn\u002BFEBHNMYW5KGGSmKIaD72tSAip6ItJCDM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"8044"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022oP3taRrtdn\u002BFEBHNMYW5KGGSmKIaD72tSAip6ItJCDM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 11 Jun 2025 16:13:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.agp80tu62r.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"agp80tu62r"},{"Name":"integrity","Value":"sha256-JtktgiuQAd\u002BAXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"70538"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022JtktgiuQAd\u002BAXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JtktgiuQAd\u002BAXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"70538"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022JtktgiuQAd\u002BAXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"196535"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.css.st1cbwfwo5.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"st1cbwfwo5"},{"Name":"integrity","Value":"sha256-QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"196535"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ysBT/JYxH9gcMnwxT4\u002BMB4sPxOx/JMg9wi77FA13T9A="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"51319"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022ysBT/JYxH9gcMnwxT4\u002BMB4sPxOx/JMg9wi77FA13T9A=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.min.css.5vj65cig9w.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5vj65cig9w"},{"Name":"integrity","Value":"sha256-72C/qDCGu\u002BOwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"117439"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u002272C/qDCGu\u002BOwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-72C/qDCGu\u002BOwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"117439"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u002272C/qDCGu\u002BOwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.min.unj9p35syc.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"unj9p35syc"},{"Name":"integrity","Value":"sha256-ysBT/JYxH9gcMnwxT4\u002BMB4sPxOx/JMg9wi77FA13T9A="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"51319"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022ysBT/JYxH9gcMnwxT4\u002BMB4sPxOx/JMg9wi77FA13T9A=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.rtl.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"70612"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00223vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.2q4vfeazbq.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2q4vfeazbq"},{"Name":"integrity","Value":"sha256-qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs\u002BWu6U8g="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"196539"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs\u002BWu6U8g=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs\u002BWu6U8g="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"196539"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs\u002BWu6U8g=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD\u002B3j0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"51394"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD\u002B3j0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NDSZjIiMPRIoO7/w7\u002BjHef8retP4riQa8PMj4BVRGok="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"117516"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022NDSZjIiMPRIoO7/w7\u002BjHef8retP4riQa8PMj4BVRGok=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.o371a8zbv2.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"o371a8zbv2"},{"Name":"integrity","Value":"sha256-NDSZjIiMPRIoO7/w7\u002BjHef8retP4riQa8PMj4BVRGok="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"117516"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022NDSZjIiMPRIoO7/w7\u002BjHef8retP4riQa8PMj4BVRGok=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.n1oizzvkh6.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"n1oizzvkh6"},{"Name":"integrity","Value":"sha256-O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD\u002B3j0="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"51394"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD\u002B3j0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.rtl.q2ku51ktnl.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-grid.rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"q2ku51ktnl"},{"Name":"integrity","Value":"sha256-3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"70612"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00223vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.7na4sro3qu.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7na4sro3qu"},{"Name":"integrity","Value":"sha256-4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5850"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00224zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5850"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00224zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.css.jeal3x0ldm.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jeal3x0ldm"},{"Name":"integrity","Value":"sha256-FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"105138"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"105138"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4646"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-2BbRsE/\u002BczX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"35330"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00222BbRsE/\u002BczX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.min.css.okkk44j0xs.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"okkk44j0xs"},{"Name":"integrity","Value":"sha256-2BbRsE/\u002BczX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"35330"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00222BbRsE/\u002BczX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.min.f8imaxxbri.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"f8imaxxbri"},{"Name":"integrity","Value":"sha256-z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4646"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.rtl.0wve5yxp74.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0wve5yxp74"},{"Name":"integrity","Value":"sha256-8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5827"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00228NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5827"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00228NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.cwzlr5n8x4.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"cwzlr5n8x4"},{"Name":"integrity","Value":"sha256-/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb\u002B0YBVU8="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"105151"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb\u002B0YBVU8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb\u002B0YBVU8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"105151"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb\u002B0YBVU8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4718"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GMDk5pA5dFkOimkBAWeEjYZ\u002B7lgHPS0jYln6p/WJVYs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"41570"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022GMDk5pA5dFkOimkBAWeEjYZ\u002B7lgHPS0jYln6p/WJVYs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.wmug9u23qg.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wmug9u23qg"},{"Name":"integrity","Value":"sha256-GMDk5pA5dFkOimkBAWeEjYZ\u002B7lgHPS0jYln6p/WJVYs="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"41570"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022GMDk5pA5dFkOimkBAWeEjYZ\u002B7lgHPS0jYln6p/WJVYs=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.npxfuf8dg6.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"npxfuf8dg6"},{"Name":"integrity","Value":"sha256-a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4718"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NbFZxZLmBVNLzb/7B0WdFfb6\u002B8jXHGX6XY190uwgbec="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"71584"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022NbFZxZLmBVNLzb/7B0WdFfb6\u002B8jXHGX6XY190uwgbec=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.css.j75batdsum.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"j75batdsum"},{"Name":"integrity","Value":"sha256-4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d\u002B9/qNju20="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"192271"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00224WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d\u002B9/qNju20=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d\u002B9/qNju20="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"192271"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u00224WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d\u002B9/qNju20=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.min.16095smhkz.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"16095smhkz"},{"Name":"integrity","Value":"sha256-5\u002BExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"53479"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00225\u002BExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5\u002BExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"53479"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00225\u002BExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-p1dop4slefZhL4zG2pa6\u002B2HUrOY1UUArGJXmet8Md9c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"111875"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022p1dop4slefZhL4zG2pa6\u002B2HUrOY1UUArGJXmet8Md9c=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.min.css.vy0bq9ydhf.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vy0bq9ydhf"},{"Name":"integrity","Value":"sha256-p1dop4slefZhL4zG2pa6\u002B2HUrOY1UUArGJXmet8Md9c="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"111875"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022p1dop4slefZhL4zG2pa6\u002B2HUrOY1UUArGJXmet8Md9c=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.rtl.b4skse8du6.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"b4skse8du6"},{"Name":"integrity","Value":"sha256-peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"71451"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"71451"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.ab1c3rmv7g.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ab1c3rmv7g"},{"Name":"integrity","Value":"sha256-puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"192214"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"192214"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj\u002BXFifSSqN4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"53407"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj\u002BXFifSSqN4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.56d2bn4wt9.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"56d2bn4wt9"},{"Name":"integrity","Value":"sha256-02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX\u002B96Pt/88="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"111710"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u002202ka4ymoE5yEecLUncLG3/SouTQMnTJOktX\u002B96Pt/88=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX\u002B96Pt/88="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"111710"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u002202ka4ymoE5yEecLUncLG3/SouTQMnTJOktX\u002B96Pt/88=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.u3xrusw2ol.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"u3xrusw2ol"},{"Name":"integrity","Value":"sha256-Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj\u002BXFifSSqN4="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"53407"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj\u002BXFifSSqN4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.tey0rigmnh.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap-utilities.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tey0rigmnh"},{"Name":"integrity","Value":"sha256-NbFZxZLmBVNLzb/7B0WdFfb6\u002B8jXHGX6XY190uwgbec="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap-utilities.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"71584"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022NbFZxZLmBVNLzb/7B0WdFfb6\u002B8jXHGX6XY190uwgbec=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"204136"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap.css.73kdqttayv.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"73kdqttayv"},{"Name":"integrity","Value":"sha256-DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"536547"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"536547"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap.min.bpk8xqwxhs.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bpk8xqwxhs"},{"Name":"integrity","Value":"sha256-z8OR40MowJ8GgK6P89Y\u002BhiJK5\u002BcclzFHzLhFQLL92bg="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"162720"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022z8OR40MowJ8GgK6P89Y\u002BhiJK5\u002BcclzFHzLhFQLL92bg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-z8OR40MowJ8GgK6P89Y\u002BhiJK5\u002BcclzFHzLhFQLL92bg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"162720"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022z8OR40MowJ8GgK6P89Y\u002BhiJK5\u002BcclzFHzLhFQLL92bg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap.min.css.8inm30yfxf.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"8inm30yfxf"},{"Name":"integrity","Value":"sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"449111"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"449111"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap.mpyigms19s.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mpyigms19s"},{"Name":"integrity","Value":"sha256-xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"204136"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap.rtl.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-SZ2mKaD4A\u002Bb\u002BHIvttwl\u002BTvLFnVy8o8/X40j\u002BEKVwyvY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"203803"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022SZ2mKaD4A\u002Bb\u002BHIvttwl\u002BTvLFnVy8o8/X40j\u002BEKVwyvY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap.rtl.css.4gxs3k148c.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4gxs3k148c"},{"Name":"integrity","Value":"sha256-VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap.rtl.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"536461"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap.rtl.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"536461"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap.rtl.min.9b9oa1qrmt.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9b9oa1qrmt"},{"Name":"integrity","Value":"sha256-22wR6QTidoeiRZXp6zkRQyMSUb/FB\u002BAv11jqmZJF6uU="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap.rtl.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"162825"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u002222wR6QTidoeiRZXp6zkRQyMSUb/FB\u002BAv11jqmZJF6uU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap.rtl.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-22wR6QTidoeiRZXp6zkRQyMSUb/FB\u002BAv11jqmZJF6uU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"162825"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u002222wR6QTidoeiRZXp6zkRQyMSUb/FB\u002BAv11jqmZJF6uU=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap.rtl.min.css.fctod5rc9n.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fctod5rc9n"},{"Name":"integrity","Value":"sha256-j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"661035"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.min.css.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"661035"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/css/bootstrap.rtl.ve6x09088i.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\css\bootstrap.rtl.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ve6x09088i"},{"Name":"integrity","Value":"sha256-SZ2mKaD4A\u002Bb\u002BHIvttwl\u002BTvLFnVy8o8/X40j\u002BEKVwyvY="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/css/bootstrap.rtl.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"203803"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022SZ2mKaD4A\u002Bb\u002BHIvttwl\u002BTvLFnVy8o8/X40j\u002BEKVwyvY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.bundle.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"208492"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.bundle.js.kbynt5jhd9.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kbynt5jhd9"},{"Name":"integrity","Value":"sha256-gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/js/bootstrap.bundle.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"425643"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.bundle.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"425643"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.bundle.l2av4jpuoj.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"l2av4jpuoj"},{"Name":"integrity","Value":"sha256-vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/js/bootstrap.bundle.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"208492"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.bundle.min.25iw1kog22.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"25iw1kog22"},{"Name":"integrity","Value":"sha256-KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/js/bootstrap.bundle.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"78468"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.bundle.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"78468"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.bundle.min.js.c2nslu3uf3.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"c2nslu3uf3"},{"Name":"integrity","Value":"sha256-xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"327261"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"327261"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.esm.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"139019"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.esm.js.2lgwfvgpvi.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2lgwfvgpvi"},{"Name":"integrity","Value":"sha256-CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/js/bootstrap.esm.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"288320"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.esm.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"288320"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.esm.m39kt2b5c9.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"m39kt2b5c9"},{"Name":"integrity","Value":"sha256-EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/js/bootstrap.esm.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"139019"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.esm.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ\u002Bw4v6TE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"72016"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ\u002Bw4v6TE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.esm.min.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.min.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"222508"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.esm.min.js.wsezl0heh6.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.min.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wsezl0heh6"},{"Name":"integrity","Value":"sha256-sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"222508"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.esm.min.um2aeqy4ik.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.esm.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"um2aeqy4ik"},{"Name":"integrity","Value":"sha256-Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ\u002Bw4v6TE="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/js/bootstrap.esm.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"72016"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ\u002Bw4v6TE=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"148168"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00226IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.js.6ukhryfubh.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6ukhryfubh"},{"Name":"integrity","Value":"sha256-Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/js/bootstrap.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"289522"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"289522"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy\u002BNjNM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"59511"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy\u002BNjNM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.min.js.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.min.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ui/FQI\u002By0IUsY8Pbi80b8s3GeEL\u002BPsvdaLTONobpn88="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"217145"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022ui/FQI\u002By0IUsY8Pbi80b8s3GeEL\u002BPsvdaLTONobpn88=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.min.js.u33ctipx7g.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.min.js.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"u33ctipx7g"},{"Name":"integrity","Value":"sha256-ui/FQI\u002By0IUsY8Pbi80b8s3GeEL\u002BPsvdaLTONobpn88="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/js/bootstrap.min.js.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"217145"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022ui/FQI\u002By0IUsY8Pbi80b8s3GeEL\u002BPsvdaLTONobpn88=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.min.zwph15dxgs.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zwph15dxgs"},{"Name":"integrity","Value":"sha256-c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy\u002BNjNM="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/js/bootstrap.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"59511"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy\u002BNjNM=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/dist/js/bootstrap.o4kw7cc6tf.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\dist\js\bootstrap.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"o4kw7cc6tf"},{"Name":"integrity","Value":"sha256-6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/dist/js/bootstrap.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"148168"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00226IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/LICENSE">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\LICENSE'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ\u002BVSYilIiEQu8ilnvqk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1153"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ZH6pA6BSx6fuHZvdaKph1DwUJ\u002BVSYilIiEQu8ilnvqk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/bootstrap/LICENSE.81b7ukuj9c">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\bootstrap\LICENSE'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"81b7ukuj9c"},{"Name":"integrity","Value":"sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ\u002BVSYilIiEQu8ilnvqk="},{"Name":"label","Value":"_content/Roya/lib/bootstrap/LICENSE"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1153"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022ZH6pA6BSx6fuHZvdaKph1DwUJ\u002BVSYilIiEQu8ilnvqk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.47otxtyo56.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"47otxtyo56"},{"Name":"integrity","Value":"sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo\u002BWQ="},{"Name":"label","Value":"_content/Roya/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"19385"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo\u002BWQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo\u002BWQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"19385"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo\u002BWQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.4v8eqarkd7.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4v8eqarkd7"},{"Name":"integrity","Value":"sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="},{"Name":"label","Value":"_content/Roya/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5824"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5824"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery-validation-unobtrusive/LICENSE.356vix0kms.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"356vix0kms"},{"Name":"integrity","Value":"sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="},{"Name":"label","Value":"_content/Roya/lib/jquery-validation-unobtrusive/LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1139"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u002216aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery-validation-unobtrusive/LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1139"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u002216aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery-validation/dist/additional-methods.ay5nd8zt9x.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ay5nd8zt9x"},{"Name":"integrity","Value":"sha256-4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E="},{"Name":"label","Value":"_content/Roya/lib/jquery-validation/dist/additional-methods.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"52977"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00224jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery-validation/dist/additional-methods.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"52977"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00224jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery-validation/dist/additional-methods.min.9oaff4kq20.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9oaff4kq20"},{"Name":"integrity","Value":"sha256-N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc="},{"Name":"label","Value":"_content/Roya/lib/jquery-validation/dist/additional-methods.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"22177"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery-validation/dist/additional-methods.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"22177"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery-validation/dist/jquery.validate.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"51171"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery-validation/dist/jquery.validate.min.b7iojwaux1.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"b7iojwaux1"},{"Name":"integrity","Value":"sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="},{"Name":"label","Value":"_content/Roya/lib/jquery-validation/dist/jquery.validate.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"24601"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery-validation/dist/jquery.validate.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"24601"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery-validation/dist/jquery.validate.pzqfkb6aqo.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pzqfkb6aqo"},{"Name":"integrity","Value":"sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="},{"Name":"label","Value":"_content/Roya/lib/jquery-validation/dist/jquery.validate.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"51171"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery-validation/LICENSE.md">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\LICENSE.md'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1117"},{"Name":"Content-Type","Value":"text/markdown"},{"Name":"ETag","Value":"\u0022geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery-validation/LICENSE.x0q3zqp4vz.md">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\LICENSE.md'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"x0q3zqp4vz"},{"Name":"integrity","Value":"sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="},{"Name":"label","Value":"_content/Roya/lib/jquery-validation/LICENSE.md"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1117"},{"Name":"Content-Type","Value":"text/markdown"},{"Name":"ETag","Value":"\u0022geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery/dist/jquery.fwhahm2icz.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fwhahm2icz"},{"Name":"integrity","Value":"sha256-H\u002BK7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk="},{"Name":"label","Value":"_content/Roya/lib/jquery/dist/jquery.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"288580"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022H\u002BK7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery/dist/jquery.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-H\u002BK7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"288580"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022H\u002BK7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery/dist/jquery.min.5pze98is44.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5pze98is44"},{"Name":"integrity","Value":"sha256-OZVI\u002Bw57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I="},{"Name":"label","Value":"_content/Roya/lib/jquery/dist/jquery.min.map"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"137972"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022OZVI\u002Bw57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery/dist/jquery.min.dd6z7egasc.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dd6z7egasc"},{"Name":"integrity","Value":"sha256-/xUj\u002B3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="},{"Name":"label","Value":"_content/Roya/lib/jquery/dist/jquery.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"89501"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022/xUj\u002B3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery/dist/jquery.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/xUj\u002B3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"89501"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022/xUj\u002B3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery/dist/jquery.min.map">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\dist\jquery.min.map'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-OZVI\u002Bw57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"137972"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022OZVI\u002Bw57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:19 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery/LICENSE.mlv21k5csn.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mlv21k5csn"},{"Name":"integrity","Value":"sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="},{"Name":"label","Value":"_content/Roya/lib/jquery/LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1117"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/lib/jquery/LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery\LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1117"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\u0022"},{"Name":"Last-Modified","Value":"Wed, 07 May 2025 15:58:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/Pictures/7215c746-97f0-468e-b9b7-cb5808f8357b.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Pictures\7215c746-97f0-468e-b9b7-cb5808f8357b.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Uwvb\u002BNwd\u002BkHU7s98XFKstmqyK40qjMTJXu1M2JcbSng="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"200310"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Uwvb\u002BNwd\u002BkHU7s98XFKstmqyK40qjMTJXu1M2JcbSng=\u0022"},{"Name":"Last-Modified","Value":"Thu, 12 Jun 2025 01:17:51 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/Pictures/7215c746-97f0-468e-b9b7-cb5808f8357b.t8jdqa5h1u.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Pictures\7215c746-97f0-468e-b9b7-cb5808f8357b.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"t8jdqa5h1u"},{"Name":"integrity","Value":"sha256-Uwvb\u002BNwd\u002BkHU7s98XFKstmqyK40qjMTJXu1M2JcbSng="},{"Name":"label","Value":"_content/Roya/Pictures/7215c746-97f0-468e-b9b7-cb5808f8357b.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"200310"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Uwvb\u002BNwd\u002BkHU7s98XFKstmqyK40qjMTJXu1M2JcbSng=\u0022"},{"Name":"Last-Modified","Value":"Thu, 12 Jun 2025 01:17:51 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/Pictures/b87e8a36-4309-453c-95ef-12ce156b2d7c.f4iofc7osp.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Pictures\b87e8a36-4309-453c-95ef-12ce156b2d7c.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"f4iofc7osp"},{"Name":"integrity","Value":"sha256-Qf/eDOyIIiOwh361rIvonUhZFR4Ba\u002BdDo402hwHNptg="},{"Name":"label","Value":"_content/Roya/Pictures/b87e8a36-4309-453c-95ef-12ce156b2d7c.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"12840"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Qf/eDOyIIiOwh361rIvonUhZFR4Ba\u002BdDo402hwHNptg=\u0022"},{"Name":"Last-Modified","Value":"Tue, 27 May 2025 04:54:48 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/Pictures/b87e8a36-4309-453c-95ef-12ce156b2d7c.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Pictures\b87e8a36-4309-453c-95ef-12ce156b2d7c.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Qf/eDOyIIiOwh361rIvonUhZFR4Ba\u002BdDo402hwHNptg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"12840"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Qf/eDOyIIiOwh361rIvonUhZFR4Ba\u002BdDo402hwHNptg=\u0022"},{"Name":"Last-Modified","Value":"Tue, 27 May 2025 04:54:48 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/Pictures/cd836df4-da3e-4097-8e89-3c45187c2788.4kt0nrc13z.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Pictures\cd836df4-da3e-4097-8e89-3c45187c2788.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4kt0nrc13z"},{"Name":"integrity","Value":"sha256-XKcwcj0s\u002BOfV9OhUBDuzCJONhWvzhyCkzPqjSCNm7/U="},{"Name":"label","Value":"_content/Roya/Pictures/cd836df4-da3e-4097-8e89-3c45187c2788.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"157147"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022XKcwcj0s\u002BOfV9OhUBDuzCJONhWvzhyCkzPqjSCNm7/U=\u0022"},{"Name":"Last-Modified","Value":"Tue, 27 May 2025 15:03:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/Pictures/cd836df4-da3e-4097-8e89-3c45187c2788.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Pictures\cd836df4-da3e-4097-8e89-3c45187c2788.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-XKcwcj0s\u002BOfV9OhUBDuzCJONhWvzhyCkzPqjSCNm7/U="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"157147"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022XKcwcj0s\u002BOfV9OhUBDuzCJONhWvzhyCkzPqjSCNm7/U=\u0022"},{"Name":"Last-Modified","Value":"Tue, 27 May 2025 15:03:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/Pictures/ea0ac251-1de0-46dc-aead-17a45e7e4cbf.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Pictures\ea0ac251-1de0-46dc-aead-17a45e7e4cbf.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MAhgdQq8z2idcTzlt0RnYp9kH1OH87Q80JX2zEkybMU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"206619"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022MAhgdQq8z2idcTzlt0RnYp9kH1OH87Q80JX2zEkybMU=\u0022"},{"Name":"Last-Modified","Value":"Tue, 27 May 2025 04:39:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/Pictures/ea0ac251-1de0-46dc-aead-17a45e7e4cbf.wcwg08lm9w.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Pictures\ea0ac251-1de0-46dc-aead-17a45e7e4cbf.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wcwg08lm9w"},{"Name":"integrity","Value":"sha256-MAhgdQq8z2idcTzlt0RnYp9kH1OH87Q80JX2zEkybMU="},{"Name":"label","Value":"_content/Roya/Pictures/ea0ac251-1de0-46dc-aead-17a45e7e4cbf.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"206619"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022MAhgdQq8z2idcTzlt0RnYp9kH1OH87Q80JX2zEkybMU=\u0022"},{"Name":"Last-Modified","Value":"Tue, 27 May 2025 04:39:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/Pictures/Roya 2030 Logo.i2412cuxfp.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Pictures\Roya 2030 Logo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"i2412cuxfp"},{"Name":"integrity","Value":"sha256-2twAtLhlWQYWaRXvqiakDJ1A8jCXlWxNPG8bgPoLq4M="},{"Name":"label","Value":"_content/Roya/Pictures/Roya 2030 Logo.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"433395"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00222twAtLhlWQYWaRXvqiakDJ1A8jCXlWxNPG8bgPoLq4M=\u0022"},{"Name":"Last-Modified","Value":"Fri, 20 Jun 2025 18:08:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/Pictures/Roya 2030 Logo.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Pictures\Roya 2030 Logo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-2twAtLhlWQYWaRXvqiakDJ1A8jCXlWxNPG8bgPoLq4M="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"433395"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00222twAtLhlWQYWaRXvqiakDJ1A8jCXlWxNPG8bgPoLq4M=\u0022"},{"Name":"Last-Modified","Value":"Fri, 20 Jun 2025 18:08:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/Pictures/unnamed-removebg-preview.fkz2u9cjri.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Pictures\unnamed-removebg-preview.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fkz2u9cjri"},{"Name":"integrity","Value":"sha256-j0JJdtFK63tvaIzOZBou/CaGSCjf\u002BidZT4nynSRBoJg="},{"Name":"label","Value":"_content/Roya/Pictures/unnamed-removebg-preview.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"210496"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022j0JJdtFK63tvaIzOZBou/CaGSCjf\u002BidZT4nynSRBoJg=\u0022"},{"Name":"Last-Modified","Value":"Mon, 30 Jun 2025 10:32:15 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/Pictures/unnamed-removebg-preview.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Pictures\unnamed-removebg-preview.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-j0JJdtFK63tvaIzOZBou/CaGSCjf\u002BidZT4nynSRBoJg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"210496"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022j0JJdtFK63tvaIzOZBou/CaGSCjf\u002BidZT4nynSRBoJg=\u0022"},{"Name":"Last-Modified","Value":"Mon, 30 Jun 2025 10:32:15 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/Roya.3md91uct9v.bundle.scp.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Roya.3md91uct9v.bundle.scp.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3md91uct9v"},{"Name":"integrity","Value":"sha256-K4Zm78jmwW4Aebdl1/t/4Kw0\u002Bfnz3hlbeY8cqmhM1m0="},{"Name":"label","Value":"_content/Roya/Roya.bundle.scp.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3405"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022K4Zm78jmwW4Aebdl1/t/4Kw0\u002Bfnz3hlbeY8cqmhM1m0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 30 Jun 2025 11:19:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/Roya.bundle.scp.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Roya.3md91uct9v.bundle.scp.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-K4Zm78jmwW4Aebdl1/t/4Kw0\u002Bfnz3hlbeY8cqmhM1m0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3405"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022K4Zm78jmwW4Aebdl1/t/4Kw0\u002Bfnz3hlbeY8cqmhM1m0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 30 Jun 2025 11:19:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CourseImages/d0d66a84-2c43-40f0-80e5-57ba244db2f5.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CourseImages\d0d66a84-2c43-40f0-80e5-57ba244db2f5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ttxeaTUWbXeTuRISNHRIiBLSaOJLAqxaNoe48UNAQFc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"86151"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022ttxeaTUWbXeTuRISNHRIiBLSaOJLAqxaNoe48UNAQFc=\u0022"},{"Name":"Last-Modified","Value":"Mon, 07 Jul 2025 18:04:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CourseImages/d0d66a84-2c43-40f0-80e5-57ba244db2f5.ujklkti6cr.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CourseImages\d0d66a84-2c43-40f0-80e5-57ba244db2f5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ujklkti6cr"},{"Name":"integrity","Value":"sha256-ttxeaTUWbXeTuRISNHRIiBLSaOJLAqxaNoe48UNAQFc="},{"Name":"label","Value":"_content/Roya/uploads/CourseImages/d0d66a84-2c43-40f0-80e5-57ba244db2f5.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"86151"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022ttxeaTUWbXeTuRISNHRIiBLSaOJLAqxaNoe48UNAQFc=\u0022"},{"Name":"Last-Modified","Value":"Mon, 07 Jul 2025 18:04:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CourseImeges/19a018e3-6c14-4cd9-9ccc-e71cebd66bd0.0lc5vqnm50.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CourseImeges\19a018e3-6c14-4cd9-9ccc-e71cebd66bd0.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0lc5vqnm50"},{"Name":"integrity","Value":"sha256-DBqkC/5xilrJnXUT2etslpRlNmAXFF5aQnBzIHWtQ7o="},{"Name":"label","Value":"_content/Roya/uploads/CourseImeges/19a018e3-6c14-4cd9-9ccc-e71cebd66bd0.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"30333"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022DBqkC/5xilrJnXUT2etslpRlNmAXFF5aQnBzIHWtQ7o=\u0022"},{"Name":"Last-Modified","Value":"Thu, 03 Jul 2025 21:51:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CourseImeges/19a018e3-6c14-4cd9-9ccc-e71cebd66bd0.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CourseImeges\19a018e3-6c14-4cd9-9ccc-e71cebd66bd0.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-DBqkC/5xilrJnXUT2etslpRlNmAXFF5aQnBzIHWtQ7o="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"30333"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022DBqkC/5xilrJnXUT2etslpRlNmAXFF5aQnBzIHWtQ7o=\u0022"},{"Name":"Last-Modified","Value":"Thu, 03 Jul 2025 21:51:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CourseImeges/45f13724-cd75-46d5-b0d8-20e8e242517d.4asifzpe2l.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CourseImeges\45f13724-cd75-46d5-b0d8-20e8e242517d.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4asifzpe2l"},{"Name":"integrity","Value":"sha256-VLWHGbYaEIn2O\u002B2KiO8mx\u002BayeFjYVSwj4ync/WsRE98="},{"Name":"label","Value":"_content/Roya/uploads/CourseImeges/45f13724-cd75-46d5-b0d8-20e8e242517d.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"26980"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022VLWHGbYaEIn2O\u002B2KiO8mx\u002BayeFjYVSwj4ync/WsRE98=\u0022"},{"Name":"Last-Modified","Value":"Thu, 03 Jul 2025 20:23:14 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CourseImeges/45f13724-cd75-46d5-b0d8-20e8e242517d.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CourseImeges\45f13724-cd75-46d5-b0d8-20e8e242517d.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-VLWHGbYaEIn2O\u002B2KiO8mx\u002BayeFjYVSwj4ync/WsRE98="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"26980"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022VLWHGbYaEIn2O\u002B2KiO8mx\u002BayeFjYVSwj4ync/WsRE98=\u0022"},{"Name":"Last-Modified","Value":"Thu, 03 Jul 2025 20:23:14 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CourseImeges/8a82cba0-f3c9-4f13-b835-189a9ea21bb1.bnvcbnrg2o.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CourseImeges\8a82cba0-f3c9-4f13-b835-189a9ea21bb1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bnvcbnrg2o"},{"Name":"integrity","Value":"sha256-yX37Cud28YzyUzT2LXEEYdE5Y9LeGvElj59qGzqaNjU="},{"Name":"label","Value":"_content/Roya/uploads/CourseImeges/8a82cba0-f3c9-4f13-b835-189a9ea21bb1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"146"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022yX37Cud28YzyUzT2LXEEYdE5Y9LeGvElj59qGzqaNjU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 03 Jul 2025 21:43:43 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CourseImeges/8a82cba0-f3c9-4f13-b835-189a9ea21bb1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CourseImeges\8a82cba0-f3c9-4f13-b835-189a9ea21bb1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-yX37Cud28YzyUzT2LXEEYdE5Y9LeGvElj59qGzqaNjU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"146"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022yX37Cud28YzyUzT2LXEEYdE5Y9LeGvElj59qGzqaNjU=\u0022"},{"Name":"Last-Modified","Value":"Thu, 03 Jul 2025 21:43:43 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CourseImeges/9e34a172-5dba-46be-9730-45f121795c18.9wld8efyjt.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CourseImeges\9e34a172-5dba-46be-9730-45f121795c18.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9wld8efyjt"},{"Name":"integrity","Value":"sha256-598GL8txOujQLo6fxUgaD87GTdY6e5rYbz0wwiR6nuE="},{"Name":"label","Value":"_content/Roya/uploads/CourseImeges/9e34a172-5dba-46be-9730-45f121795c18.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"24382"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022598GL8txOujQLo6fxUgaD87GTdY6e5rYbz0wwiR6nuE=\u0022"},{"Name":"Last-Modified","Value":"Tue, 01 Jul 2025 22:58:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CourseImeges/9e34a172-5dba-46be-9730-45f121795c18.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CourseImeges\9e34a172-5dba-46be-9730-45f121795c18.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-598GL8txOujQLo6fxUgaD87GTdY6e5rYbz0wwiR6nuE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"24382"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022598GL8txOujQLo6fxUgaD87GTdY6e5rYbz0wwiR6nuE=\u0022"},{"Name":"Last-Modified","Value":"Tue, 01 Jul 2025 22:58:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CourseImeges/c98b356d-ed69-4fd2-926f-a29bdee13b56.5wldg6cfj5.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CourseImeges\c98b356d-ed69-4fd2-926f-a29bdee13b56.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5wldg6cfj5"},{"Name":"integrity","Value":"sha256-FRg1U61Ov2FrsBt\u002BBjwXDJUrbB7e1IP1N5\u002B2DA8pFns="},{"Name":"label","Value":"_content/Roya/uploads/CourseImeges/c98b356d-ed69-4fd2-926f-a29bdee13b56.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"30004"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022FRg1U61Ov2FrsBt\u002BBjwXDJUrbB7e1IP1N5\u002B2DA8pFns=\u0022"},{"Name":"Last-Modified","Value":"Thu, 10 Jul 2025 03:16:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CourseImeges/c98b356d-ed69-4fd2-926f-a29bdee13b56.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CourseImeges\c98b356d-ed69-4fd2-926f-a29bdee13b56.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-FRg1U61Ov2FrsBt\u002BBjwXDJUrbB7e1IP1N5\u002B2DA8pFns="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"30004"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022FRg1U61Ov2FrsBt\u002BBjwXDJUrbB7e1IP1N5\u002B2DA8pFns=\u0022"},{"Name":"Last-Modified","Value":"Thu, 10 Jul 2025 03:16:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CourseImeges/d3f95191-29f9-486e-9c4d-9553d0d06de8.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CourseImeges\d3f95191-29f9-486e-9c4d-9553d0d06de8.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MIwC1KcI\u002BTSxiVy5f/VvecHPigQmnVThXa9EiR9V3ho="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"24454"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022MIwC1KcI\u002BTSxiVy5f/VvecHPigQmnVThXa9EiR9V3ho=\u0022"},{"Name":"Last-Modified","Value":"Tue, 01 Jul 2025 23:13:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CourseImeges/d3f95191-29f9-486e-9c4d-9553d0d06de8.w0bcxg79nr.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CourseImeges\d3f95191-29f9-486e-9c4d-9553d0d06de8.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"w0bcxg79nr"},{"Name":"integrity","Value":"sha256-MIwC1KcI\u002BTSxiVy5f/VvecHPigQmnVThXa9EiR9V3ho="},{"Name":"label","Value":"_content/Roya/uploads/CourseImeges/d3f95191-29f9-486e-9c4d-9553d0d06de8.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"24454"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022MIwC1KcI\u002BTSxiVy5f/VvecHPigQmnVThXa9EiR9V3ho=\u0022"},{"Name":"Last-Modified","Value":"Tue, 01 Jul 2025 23:13:20 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CVs/1d50fa0d-232a-4253-9001-30f9b3ad2164.gzvaewjo7s.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CVs\1d50fa0d-232a-4253-9001-30f9b3ad2164.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gzvaewjo7s"},{"Name":"integrity","Value":"sha256-lMGhOOM9CQS/1wBuWV6mFMImc0WcJJDXGZnMJ3lgA\u002Bc="},{"Name":"label","Value":"_content/Roya/uploads/CVs/1d50fa0d-232a-4253-9001-30f9b3ad2164.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"449218"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022lMGhOOM9CQS/1wBuWV6mFMImc0WcJJDXGZnMJ3lgA\u002Bc=\u0022"},{"Name":"Last-Modified","Value":"Fri, 20 Jun 2025 09:30:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CVs/1d50fa0d-232a-4253-9001-30f9b3ad2164.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CVs\1d50fa0d-232a-4253-9001-30f9b3ad2164.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-lMGhOOM9CQS/1wBuWV6mFMImc0WcJJDXGZnMJ3lgA\u002Bc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"449218"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022lMGhOOM9CQS/1wBuWV6mFMImc0WcJJDXGZnMJ3lgA\u002Bc=\u0022"},{"Name":"Last-Modified","Value":"Fri, 20 Jun 2025 09:30:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CVs/1fd0d641-d673-4d92-b407-c8596c3b83b4.l8r19tymtl.mp4">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CVs\1fd0d641-d673-4d92-b407-c8596c3b83b4.mp4'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"l8r19tymtl"},{"Name":"integrity","Value":"sha256-2\u002BLYxaOj7b/wmQ5Y0e\u002BOLr45JnOJ/0c4zF5pkktnUPs="},{"Name":"label","Value":"_content/Roya/uploads/CVs/1fd0d641-d673-4d92-b407-c8596c3b83b4.mp4"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"655538"},{"Name":"Content-Type","Value":"video/mp4"},{"Name":"ETag","Value":"\u00222\u002BLYxaOj7b/wmQ5Y0e\u002BOLr45JnOJ/0c4zF5pkktnUPs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 06 Jul 2025 20:04:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CVs/1fd0d641-d673-4d92-b407-c8596c3b83b4.mp4">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CVs\1fd0d641-d673-4d92-b407-c8596c3b83b4.mp4'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-2\u002BLYxaOj7b/wmQ5Y0e\u002BOLr45JnOJ/0c4zF5pkktnUPs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"655538"},{"Name":"Content-Type","Value":"video/mp4"},{"Name":"ETag","Value":"\u00222\u002BLYxaOj7b/wmQ5Y0e\u002BOLr45JnOJ/0c4zF5pkktnUPs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 06 Jul 2025 20:04:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CVs/42840a77-53f8-4fc7-87a2-2f773e44cd1c.c49vc3g4jt.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CVs\42840a77-53f8-4fc7-87a2-2f773e44cd1c.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"c49vc3g4jt"},{"Name":"integrity","Value":"sha256-FEh1e6zK773ZOkmO62IwtFckd9B7sB68vNx9N7CDgV0="},{"Name":"label","Value":"_content/Roya/uploads/CVs/42840a77-53f8-4fc7-87a2-2f773e44cd1c.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"47837"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022FEh1e6zK773ZOkmO62IwtFckd9B7sB68vNx9N7CDgV0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 30 Jun 2025 23:09:02 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CVs/42840a77-53f8-4fc7-87a2-2f773e44cd1c.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CVs\42840a77-53f8-4fc7-87a2-2f773e44cd1c.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-FEh1e6zK773ZOkmO62IwtFckd9B7sB68vNx9N7CDgV0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"47837"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022FEh1e6zK773ZOkmO62IwtFckd9B7sB68vNx9N7CDgV0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 30 Jun 2025 23:09:02 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CVs/4ac41654-a3c8-4167-a2aa-dcf2d11ede42.hzo92wdni0.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CVs\4ac41654-a3c8-4167-a2aa-dcf2d11ede42.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hzo92wdni0"},{"Name":"integrity","Value":"sha256-vTgsdb58CxNHQMbH21O694ipbfsWajVdqCCLE\u002BblOrM="},{"Name":"label","Value":"_content/Roya/uploads/CVs/4ac41654-a3c8-4167-a2aa-dcf2d11ede42.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"33384"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022vTgsdb58CxNHQMbH21O694ipbfsWajVdqCCLE\u002BblOrM=\u0022"},{"Name":"Last-Modified","Value":"Mon, 30 Jun 2025 23:32:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CVs/4ac41654-a3c8-4167-a2aa-dcf2d11ede42.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CVs\4ac41654-a3c8-4167-a2aa-dcf2d11ede42.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vTgsdb58CxNHQMbH21O694ipbfsWajVdqCCLE\u002BblOrM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"33384"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022vTgsdb58CxNHQMbH21O694ipbfsWajVdqCCLE\u002BblOrM=\u0022"},{"Name":"Last-Modified","Value":"Mon, 30 Jun 2025 23:32:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CVs/5e1b589e-12a4-4f58-b123-d61c7dc65273.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CVs\5e1b589e-12a4-4f58-b123-d61c7dc65273.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-xhzwM9gKhIja\u002BmxhtV3AXWRJaTCpj1ds5h4gbC4cGFE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"192895"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022xhzwM9gKhIja\u002BmxhtV3AXWRJaTCpj1ds5h4gbC4cGFE=\u0022"},{"Name":"Last-Modified","Value":"Fri, 20 Jun 2025 09:31:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CVs/5e1b589e-12a4-4f58-b123-d61c7dc65273.qg6i2wru1a.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CVs\5e1b589e-12a4-4f58-b123-d61c7dc65273.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qg6i2wru1a"},{"Name":"integrity","Value":"sha256-xhzwM9gKhIja\u002BmxhtV3AXWRJaTCpj1ds5h4gbC4cGFE="},{"Name":"label","Value":"_content/Roya/uploads/CVs/5e1b589e-12a4-4f58-b123-d61c7dc65273.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"192895"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022xhzwM9gKhIja\u002BmxhtV3AXWRJaTCpj1ds5h4gbC4cGFE=\u0022"},{"Name":"Last-Modified","Value":"Fri, 20 Jun 2025 09:31:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CVs/70868615-c704-4416-8571-ff43e8d6cb6c.1f01o87cgz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CVs\70868615-c704-4416-8571-ff43e8d6cb6c.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"1f01o87cgz"},{"Name":"integrity","Value":"sha256-o59SjkmOcaKqZ\u002BGMUNrNX3E29xQavUrZoHpdpDnlcaE="},{"Name":"label","Value":"_content/Roya/uploads/CVs/70868615-c704-4416-8571-ff43e8d6cb6c.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"32365"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022o59SjkmOcaKqZ\u002BGMUNrNX3E29xQavUrZoHpdpDnlcaE=\u0022"},{"Name":"Last-Modified","Value":"Sat, 12 Jul 2025 16:27:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CVs/70868615-c704-4416-8571-ff43e8d6cb6c.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CVs\70868615-c704-4416-8571-ff43e8d6cb6c.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-o59SjkmOcaKqZ\u002BGMUNrNX3E29xQavUrZoHpdpDnlcaE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"32365"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022o59SjkmOcaKqZ\u002BGMUNrNX3E29xQavUrZoHpdpDnlcaE=\u0022"},{"Name":"Last-Modified","Value":"Sat, 12 Jul 2025 16:27:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CVs/8f117439-7861-4346-ada6-3f1a3b9ed8df.057g30a0ay.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CVs\8f117439-7861-4346-ada6-3f1a3b9ed8df.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"057g30a0ay"},{"Name":"integrity","Value":"sha256-3PQOgcX5pxqWCjwFjFkzQQ0kID8Qlt/m3zAcv6LOK6A="},{"Name":"label","Value":"_content/Roya/uploads/CVs/8f117439-7861-4346-ada6-3f1a3b9ed8df.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"175634"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00223PQOgcX5pxqWCjwFjFkzQQ0kID8Qlt/m3zAcv6LOK6A=\u0022"},{"Name":"Last-Modified","Value":"Sat, 21 Jun 2025 09:57:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CVs/8f117439-7861-4346-ada6-3f1a3b9ed8df.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CVs\8f117439-7861-4346-ada6-3f1a3b9ed8df.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-3PQOgcX5pxqWCjwFjFkzQQ0kID8Qlt/m3zAcv6LOK6A="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"175634"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00223PQOgcX5pxqWCjwFjFkzQQ0kID8Qlt/m3zAcv6LOK6A=\u0022"},{"Name":"Last-Modified","Value":"Sat, 21 Jun 2025 09:57:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CVs/b0c30c3c-958d-45d2-a84b-974c20d96f8b.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CVs\b0c30c3c-958d-45d2-a84b-974c20d96f8b.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-UjZS/8UCdjm7VFDeegXO\u002Bin1fzt9xEEv0nJo2sc9Sz0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"180785"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022UjZS/8UCdjm7VFDeegXO\u002Bin1fzt9xEEv0nJo2sc9Sz0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 30 Jun 2025 23:07:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/CVs/b0c30c3c-958d-45d2-a84b-974c20d96f8b.ql6dbnkaye.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\CVs\b0c30c3c-958d-45d2-a84b-974c20d96f8b.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ql6dbnkaye"},{"Name":"integrity","Value":"sha256-UjZS/8UCdjm7VFDeegXO\u002Bin1fzt9xEEv0nJo2sc9Sz0="},{"Name":"label","Value":"_content/Roya/uploads/CVs/b0c30c3c-958d-45d2-a84b-974c20d96f8b.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"180785"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022UjZS/8UCdjm7VFDeegXO\u002Bin1fzt9xEEv0nJo2sc9Sz0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 30 Jun 2025 23:07:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/TrainerImages/03e309d4-a1cd-46bf-9372-bd3494d5d996.i0lamgey9r.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\TrainerImages\03e309d4-a1cd-46bf-9372-bd3494d5d996.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"i0lamgey9r"},{"Name":"integrity","Value":"sha256-4v6B8TGxtNhhh0ENU8Rmj6CRyk8vf8HZGG85EAfkTNo="},{"Name":"label","Value":"_content/Roya/uploads/TrainerImages/03e309d4-a1cd-46bf-9372-bd3494d5d996.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"911205"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00224v6B8TGxtNhhh0ENU8Rmj6CRyk8vf8HZGG85EAfkTNo=\u0022"},{"Name":"Last-Modified","Value":"Mon, 30 Jun 2025 23:09:02 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/TrainerImages/03e309d4-a1cd-46bf-9372-bd3494d5d996.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\TrainerImages\03e309d4-a1cd-46bf-9372-bd3494d5d996.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4v6B8TGxtNhhh0ENU8Rmj6CRyk8vf8HZGG85EAfkTNo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"911205"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00224v6B8TGxtNhhh0ENU8Rmj6CRyk8vf8HZGG85EAfkTNo=\u0022"},{"Name":"Last-Modified","Value":"Mon, 30 Jun 2025 23:09:02 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/TrainerImages/7ab819ae-ff6c-462d-bda1-e18dde9ca4aa.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\TrainerImages\7ab819ae-ff6c-462d-bda1-e18dde9ca4aa.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nSwTynRFX8j9XyUUKbdRQ1PSDGcNCRJr6fE1Igm9Ol4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"36360"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022nSwTynRFX8j9XyUUKbdRQ1PSDGcNCRJr6fE1Igm9Ol4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 12 Jul 2025 16:27:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/TrainerImages/7ab819ae-ff6c-462d-bda1-e18dde9ca4aa.v9wknfqdg4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\TrainerImages\7ab819ae-ff6c-462d-bda1-e18dde9ca4aa.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"v9wknfqdg4"},{"Name":"integrity","Value":"sha256-nSwTynRFX8j9XyUUKbdRQ1PSDGcNCRJr6fE1Igm9Ol4="},{"Name":"label","Value":"_content/Roya/uploads/TrainerImages/7ab819ae-ff6c-462d-bda1-e18dde9ca4aa.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"36360"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022nSwTynRFX8j9XyUUKbdRQ1PSDGcNCRJr6fE1Igm9Ol4=\u0022"},{"Name":"Last-Modified","Value":"Sat, 12 Jul 2025 16:27:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/TrainerImages/a65f1f6f-ed92-4c09-83d5-92fe282a69b5.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\TrainerImages\a65f1f6f-ed92-4c09-83d5-92fe282a69b5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-O/2\u002BiHSaDxcgt7I0x1\u002Bv2o6zFx4GeeYptQpPyqxFmBU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"16773"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022O/2\u002BiHSaDxcgt7I0x1\u002Bv2o6zFx4GeeYptQpPyqxFmBU=\u0022"},{"Name":"Last-Modified","Value":"Sat, 21 Jun 2025 09:57:35 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/TrainerImages/a65f1f6f-ed92-4c09-83d5-92fe282a69b5.ro89tribuy.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\TrainerImages\a65f1f6f-ed92-4c09-83d5-92fe282a69b5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ro89tribuy"},{"Name":"integrity","Value":"sha256-O/2\u002BiHSaDxcgt7I0x1\u002Bv2o6zFx4GeeYptQpPyqxFmBU="},{"Name":"label","Value":"_content/Roya/uploads/TrainerImages/a65f1f6f-ed92-4c09-83d5-92fe282a69b5.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16773"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022O/2\u002BiHSaDxcgt7I0x1\u002Bv2o6zFx4GeeYptQpPyqxFmBU=\u0022"},{"Name":"Last-Modified","Value":"Sat, 21 Jun 2025 09:57:35 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/TrainerImages/b36e0ff0-5ecc-426e-80ee-3387fa3dfb11.i0lamgey9r.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\TrainerImages\b36e0ff0-5ecc-426e-80ee-3387fa3dfb11.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"i0lamgey9r"},{"Name":"integrity","Value":"sha256-4v6B8TGxtNhhh0ENU8Rmj6CRyk8vf8HZGG85EAfkTNo="},{"Name":"label","Value":"_content/Roya/uploads/TrainerImages/b36e0ff0-5ecc-426e-80ee-3387fa3dfb11.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"911205"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00224v6B8TGxtNhhh0ENU8Rmj6CRyk8vf8HZGG85EAfkTNo=\u0022"},{"Name":"Last-Modified","Value":"Mon, 30 Jun 2025 23:07:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/TrainerImages/b36e0ff0-5ecc-426e-80ee-3387fa3dfb11.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\TrainerImages\b36e0ff0-5ecc-426e-80ee-3387fa3dfb11.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4v6B8TGxtNhhh0ENU8Rmj6CRyk8vf8HZGG85EAfkTNo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"911205"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00224v6B8TGxtNhhh0ENU8Rmj6CRyk8vf8HZGG85EAfkTNo=\u0022"},{"Name":"Last-Modified","Value":"Mon, 30 Jun 2025 23:07:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/TrainerImages/dc554658-e089-4221-8ee2-fb1d5323ab1d.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\TrainerImages\dc554658-e089-4221-8ee2-fb1d5323ab1d.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-haTVNMGJQq02hNpJniWa56Vq3W5Kqm6L/ahmbzNy4Js="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"31222"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022haTVNMGJQq02hNpJniWa56Vq3W5Kqm6L/ahmbzNy4Js=\u0022"},{"Name":"Last-Modified","Value":"Mon, 30 Jun 2025 23:32:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/TrainerImages/dc554658-e089-4221-8ee2-fb1d5323ab1d.x53i0h2rtq.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\TrainerImages\dc554658-e089-4221-8ee2-fb1d5323ab1d.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"x53i0h2rtq"},{"Name":"integrity","Value":"sha256-haTVNMGJQq02hNpJniWa56Vq3W5Kqm6L/ahmbzNy4Js="},{"Name":"label","Value":"_content/Roya/uploads/TrainerImages/dc554658-e089-4221-8ee2-fb1d5323ab1d.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"31222"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022haTVNMGJQq02hNpJniWa56Vq3W5Kqm6L/ahmbzNy4Js=\u0022"},{"Name":"Last-Modified","Value":"Mon, 30 Jun 2025 23:32:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/TrainerImages/e77db8e9-8dc5-4ac2-a9cf-f75c40647058.6abcce1co8.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\TrainerImages\e77db8e9-8dc5-4ac2-a9cf-f75c40647058.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6abcce1co8"},{"Name":"integrity","Value":"sha256-4qdcKgBP2N3OpVhzjsZyLejVoqQOD55mrw7x0g1GWjo="},{"Name":"label","Value":"_content/Roya/uploads/TrainerImages/e77db8e9-8dc5-4ac2-a9cf-f75c40647058.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"134105"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00224qdcKgBP2N3OpVhzjsZyLejVoqQOD55mrw7x0g1GWjo=\u0022"},{"Name":"Last-Modified","Value":"Fri, 20 Jun 2025 09:30:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/TrainerImages/e77db8e9-8dc5-4ac2-a9cf-f75c40647058.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\TrainerImages\e77db8e9-8dc5-4ac2-a9cf-f75c40647058.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-4qdcKgBP2N3OpVhzjsZyLejVoqQOD55mrw7x0g1GWjo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"134105"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00224qdcKgBP2N3OpVhzjsZyLejVoqQOD55mrw7x0g1GWjo=\u0022"},{"Name":"Last-Modified","Value":"Fri, 20 Jun 2025 09:30:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/TrainerImages/f8a6a4f4-5207-4351-b574-616b17e00f42.0txlytjbtt.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\TrainerImages\f8a6a4f4-5207-4351-b574-616b17e00f42.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0txlytjbtt"},{"Name":"integrity","Value":"sha256-nHu4AmhiByzq01S/Uz6P1Z7X3X6HTsxGV4E8h1Kk6zM="},{"Name":"label","Value":"_content/Roya/uploads/TrainerImages/f8a6a4f4-5207-4351-b574-616b17e00f42.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2007476"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022nHu4AmhiByzq01S/Uz6P1Z7X3X6HTsxGV4E8h1Kk6zM=\u0022"},{"Name":"Last-Modified","Value":"Fri, 20 Jun 2025 09:31:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/Roya/uploads/TrainerImages/f8a6a4f4-5207-4351-b574-616b17e00f42.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\TrainerImages\f8a6a4f4-5207-4351-b574-616b17e00f42.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nHu4AmhiByzq01S/Uz6P1Z7X3X6HTsxGV4E8h1Kk6zM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2007476"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022nHu4AmhiByzq01S/Uz6P1Z7X3X6HTsxGV4E8h1Kk6zM=\u0022"},{"Name":"Last-Modified","Value":"Fri, 20 Jun 2025 09:31:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>
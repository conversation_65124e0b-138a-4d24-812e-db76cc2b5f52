// JavaScript للوحة التحكم

document.addEventListener('DOMContentLoaded', function() {
    // تفعيل القوائم الفرعية
    initSubmenuToggle();

    // تفعيل الشريط الجانبي للهواتف
    initMobileSidebar();

    // تفعيل تأثيرات الكروت
    initCardAnimations();

    // تفعيل زر إخفاء/إظهار الشريط الجانبي
    initSidebarToggle();

    // تفعيل التنقل بين الصفحات في الجداول
    initPagination();
});

// تفعيل القوائم الفرعية
function initSubmenuToggle() {
    const submenuItems = document.querySelectorAll('.has-submenu > .nav-link');
    
    submenuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const parent = this.parentElement;
            const isOpen = parent.classList.contains('open');
            
            // إغلاق جميع القوائم الفرعية الأخرى
            document.querySelectorAll('.has-submenu').forEach(menu => {
                menu.classList.remove('open');
            });
            
            // فتح/إغلاق القائمة الحالية
            if (!isOpen) {
                parent.classList.add('open');
            }
        });
    });
}

// تفعيل الشريط الجانبي للهواتف
function initMobileSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const toggleBtn = document.querySelector('.sidebar-toggle');
    
    if (toggleBtn) {
        toggleBtn.addEventListener('click', function() {
            sidebar.classList.toggle('open');
        });
    }
    
    // إغلاق الشريط الجانبي عند النقر خارجه
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 768) {
            if (!sidebar.contains(e.target) && !e.target.classList.contains('sidebar-toggle')) {
                sidebar.classList.remove('open');
            }
        }
    });
}

// تفعيل تأثيرات الكروت
function initCardAnimations() {
    const cards = document.querySelectorAll('.stat-card, .request-card, .activity-item');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// وظائف إدارة المدربين
function approveTrainer(trainerId) {
    if (confirm('هل أنت متأكد من قبول هذا المدرب؟')) {
        // محاكاة طلب AJAX
        showLoading();
        
        setTimeout(() => {
            hideLoading();
            showNotification('تم قبول المدرب بنجاح!', 'success');
            updateTrainerStatus(trainerId, 'approved');
        }, 1500);
    }
}

function rejectTrainer(trainerId) {
    if (confirm('هل أنت متأكد من رفض هذا المدرب؟')) {
        showLoading();
        
        setTimeout(() => {
            hideLoading();
            showNotification('تم رفض المدرب!', 'error');
            updateTrainerStatus(trainerId, 'rejected');
        }, 1500);
    }
}

function viewTrainerDetails(trainerId) {
    // الانتقال لصفحة تفاصيل المدرب
    window.location.href = `trainer-details.html?id=${trainerId}`;
}

// تحديث حالة المدرب
function updateTrainerStatus(trainerId, status) {
    const card = document.querySelector(`[data-trainer-id="${trainerId}"]`);
    if (card) {
        const statusElement = card.querySelector('.request-status');
        const actionButtons = card.querySelector('.card-actions');
        
        // تحديث النص والألوان
        statusElement.className = `request-status ${status}`;
        
        if (status === 'approved') {
            statusElement.innerHTML = '<i class="fas fa-check"></i> مقبول';
            actionButtons.style.display = 'none';
        } else if (status === 'rejected') {
            statusElement.innerHTML = '<i class="fas fa-times"></i> مرفوض';
            actionButtons.style.display = 'none';
        }
        
        // تأثير بصري
        card.style.animation = 'pulse 0.5s ease-in-out';
    }
}

// وظائف إدارة الدورات
function approveCourse(courseId) {
    if (confirm('هل أنت متأكد من قبول هذه الدورة؟')) {
        showLoading();
        
        setTimeout(() => {
            hideLoading();
            showNotification('تم قبول الدورة بنجاح!', 'success');
            updateCourseStatus(courseId, 'approved');
        }, 1500);
    }
}

function rejectCourse(courseId) {
    if (confirm('هل أنت متأكد من رفض هذه الدورة؟')) {
        showLoading();
        
        setTimeout(() => {
            hideLoading();
            showNotification('تم رفض الدورة!', 'error');
            updateCourseStatus(courseId, 'rejected');
        }, 1500);
    }
}

function editCourse(courseId) {
    window.location.href = `edit-course.html?id=${courseId}`;
}

function viewCourseDetails(courseId) {
    window.location.href = `course-admin-details.html?id=${courseId}`;
}

// تحديث حالة الدورة
function updateCourseStatus(courseId, status) {
    const card = document.querySelector(`[data-course-id="${courseId}"]`);
    if (card) {
        const statusElement = card.querySelector('.request-status');
        const actionButtons = card.querySelector('.card-actions');
        
        statusElement.className = `request-status ${status}`;
        
        if (status === 'approved') {
            statusElement.innerHTML = '<i class="fas fa-check"></i> مقبولة';
        } else if (status === 'rejected') {
            statusElement.innerHTML = '<i class="fas fa-times"></i> مرفوضة';
        }
        
        card.style.animation = 'pulse 0.5s ease-in-out';
    }
}

// وظائف المساعدة
function showLoading() {
    // إنشاء عنصر التحميل
    const loader = document.createElement('div');
    loader.id = 'loading-overlay';
    loader.innerHTML = `
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>جاري المعالجة...</p>
        </div>
    `;
    document.body.appendChild(loader);
}

function hideLoading() {
    const loader = document.getElementById('loading-overlay');
    if (loader) {
        loader.remove();
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // إظهار الإشعار
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // إخفاء الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// وظائف البحث والفلترة
function initSearch() {
    const searchInput = document.getElementById('search-input');
    const filterSelect = document.getElementById('status-filter');
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterItems(this.value, filterSelect ? filterSelect.value : '');
        });
    }
    
    if (filterSelect) {
        filterSelect.addEventListener('change', function() {
            filterItems(searchInput ? searchInput.value : '', this.value);
        });
    }
}

function filterItems(searchTerm, statusFilter) {
    const items = document.querySelectorAll('.request-card, .table-row');
    
    items.forEach(item => {
        const text = item.textContent.toLowerCase();
        const status = item.dataset.status || '';
        
        const matchesSearch = text.includes(searchTerm.toLowerCase());
        const matchesStatus = !statusFilter || status === statusFilter;
        
        if (matchesSearch && matchesStatus) {
            item.style.display = '';
        } else {
            item.style.display = 'none';
        }
    });
}

// تفعيل البحث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initSearch();
});

// CSS للتأثيرات الإضافية
const additionalStyles = `
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    #loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }
    
    .loading-spinner {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        text-align: center;
    }
    
    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .notification {
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%) translateY(-100px);
        background: white;
        padding: 1rem 2rem;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        z-index: 9999;
        transition: all 0.3s ease;
    }
    
    .notification.show {
        transform: translateX(-50%) translateY(0);
    }
    
    .notification.success {
        border-left: 4px solid #4caf50;
    }
    
    .notification.error {
        border-left: 4px solid #f44336;
    }
    
    .notification.info {
        border-left: 4px solid #2196f3;
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
`;

// إضافة الأنماط للصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);

// تحسين الشريط الجانبي للهواتف
function enhanceMobileSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const mainHeader = document.querySelector('.main-header');

    if (window.innerWidth <= 768 && mainHeader && !mainHeader.querySelector('.mobile-menu-toggle')) {
        // إضافة زر القائمة للهواتف
        const menuToggle = document.createElement('button');
        menuToggle.className = 'mobile-menu-toggle';
        menuToggle.innerHTML = '<i class="fas fa-bars"></i>';
        menuToggle.style.cssText = `
            position: absolute;
            top: 50%;
            right: 1rem;
            transform: translateY(-50%);
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem;
            border-radius: 5px;
            cursor: pointer;
            z-index: 1001;
            font-size: 1.2rem;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        mainHeader.appendChild(menuToggle);

        // تفعيل النقر على زر القائمة
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');

            // إضافة overlay
            if (sidebar.classList.contains('active')) {
                const overlay = document.createElement('div');
                overlay.className = 'sidebar-overlay';
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    z-index: 999;
                `;
                document.body.appendChild(overlay);

                // إغلاق عند النقر على الـ overlay
                overlay.addEventListener('click', function() {
                    sidebar.classList.remove('active');
                    document.body.removeChild(overlay);
                });
            } else {
                const overlay = document.querySelector('.sidebar-overlay');
                if (overlay) {
                    document.body.removeChild(overlay);
                }
            }
        });
    }
}

// تشغيل التحسينات
enhanceMobileSidebar();

// إعادة تشغيل عند تغيير حجم الشاشة
window.addEventListener('resize', function() {
    if (window.innerWidth > 768) {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        const menuToggle = document.querySelector('.mobile-menu-toggle');

        if (sidebar) sidebar.classList.remove('active');
        if (overlay) document.body.removeChild(overlay);
        if (menuToggle) menuToggle.remove();
    } else {
        enhanceMobileSidebar();
    }
});

// تفعيل زر تصغير/توسيع الشريط الجانبي
function initSidebarToggle() {
    const toggleBtn = document.getElementById('sidebarToggle');
    const adminLayout = document.querySelector('.admin-layout');

    if (toggleBtn && adminLayout) {
        toggleBtn.addEventListener('click', function() {
            adminLayout.classList.toggle('sidebar-collapsed');

            // تغيير عنوان الزر
            if (adminLayout.classList.contains('sidebar-collapsed')) {
                this.title = 'توسيع القائمة الجانبية';
            } else {
                this.title = 'تصغير القائمة الجانبية';
            }

            // حفظ الحالة في localStorage
            localStorage.setItem('sidebarCollapsed', adminLayout.classList.contains('sidebar-collapsed'));
        });

        // استرجاع الحالة المحفوظة
        const savedState = localStorage.getItem('sidebarCollapsed');
        if (savedState === 'true') {
            adminLayout.classList.add('sidebar-collapsed');
            toggleBtn.title = 'توسيع القائمة الجانبية';
        }
    }
}

// تفعيل التنقل بين الصفحات في الجداول
function initPagination() {
    const paginationBtns = document.querySelectorAll('.pagination-btn');

    paginationBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();

            const action = this.dataset.action;
            const currentPageElement = document.querySelector('.current-page');
            const totalPagesElement = document.querySelector('.total-pages');

            if (!currentPageElement || !totalPagesElement) return;

            let currentPage = parseInt(currentPageElement.textContent);
            const totalPages = parseInt(totalPagesElement.textContent);

            if (action === 'prev' && currentPage > 1) {
                currentPage--;
            } else if (action === 'next' && currentPage < totalPages) {
                currentPage++;
            }

            // تحديث رقم الصفحة
            currentPageElement.textContent = currentPage;

            // تحديث حالة الأزرار
            updatePaginationButtons(currentPage, totalPages);

            // تحديث محتوى الجدول (محاكاة)
            loadTablePage(currentPage);
        });
    });
}

// تحديث حالة أزرار التنقل
function updatePaginationButtons(currentPage, totalPages) {
    const prevBtn = document.querySelector('[data-action="prev"]');
    const nextBtn = document.querySelector('[data-action="next"]');

    if (prevBtn) {
        prevBtn.disabled = currentPage <= 1;
        prevBtn.classList.toggle('disabled', currentPage <= 1);
    }

    if (nextBtn) {
        nextBtn.disabled = currentPage >= totalPages;
        nextBtn.classList.toggle('disabled', currentPage >= totalPages);
    }
}

// محاكاة تحميل صفحة جديدة من الجدول
function loadTablePage(page) {
    const tableBody = document.querySelector('.data-table tbody');
    if (!tableBody) return;

    // إضافة تأثير التحميل
    tableBody.style.opacity = '0.5';

    setTimeout(() => {
        // هنا يمكن إضافة منطق تحميل البيانات الفعلية
        console.log(`تحميل الصفحة ${page}`);

        // إزالة تأثير التحميل
        tableBody.style.opacity = '1';

        // إظهار رسالة نجاح
        showNotification(`تم تحميل الصفحة ${page}`, 'success');
    }, 500);
}

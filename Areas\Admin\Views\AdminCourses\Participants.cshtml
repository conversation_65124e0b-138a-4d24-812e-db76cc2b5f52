﻿@model List<Roya.ViewModels.CourseParticipantVM>
@{
    ViewData["Title"] = "الطلبة المشاركون";
    var courseName = ViewBag.CourseName as string;
}

<!-- ✅ تأثير هوفر وتكبير -->
<style>
    .card-hover {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

        .card-hover:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
            z-index: 1;
        }
</style>

<div class="container mt-5" dir="rtl">
    <h2 class="text-center text-info mb-4">👥 الطلبة المشاركون في الدورة: "@courseName"</h2>

    @if (Model.Any())
    {
        <div class="row">
            @foreach (var trainee in Model)
            {
                <div class="col-md-4 mb-4">
                    <div class="card card-hover h-100 border-primary shadow-sm">
                        <div class="card-body d-flex flex-column justify-content-between">
                            <div>
                                <h5 class="card-title text-primary fw-bold">📛 @trainee.FullName</h5>
                                <p class="card-text"><strong>📅 تاريخ التسجيل:</strong> @trainee.RegistrationDate.ToString("yyyy/MM/dd")</p>
                                <p class="card-text">
                                    <strong>⚙️ الحالة:</strong>
                                    @switch (trainee.Status)
                                    {
                                        case RegistrationStatus.Pending:
                                            <span class="badge bg-warning text-dark">قيد المراجعة</span>
                                            break;
                                        case RegistrationStatus.Approved:
                                            <span class="badge bg-success">مقبول</span>
                                            break;
                                        case RegistrationStatus.Rejected:
                                            <span class="badge bg-danger">مرفوض</span>
                                            break;
                                    }
                                </p>
                            </div>
                            <div class="text-center mt-3">
                                <a asp-controller="AdminCourses"
                                   asp-action="ViewAnswers"
                                   asp-route-courseId="@trainee.CourseId"
                                   asp-route-traineeId="@trainee.TraineeId"
                                   class="btn btn-outline-primary btn-sm">
                                    📄 عرض الإجابات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="alert alert-info text-center">
            لا يوجد طلاب مسجلين في هذه الدورة.
        </div>
    }
</div>

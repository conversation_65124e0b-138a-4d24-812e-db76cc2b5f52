﻿using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Roya.Models
{
    public enum CourseStatus
    {
        Pending,    // 0 - قيد المراجعة
        Approved,   // 1 - مق<PERSON>ول
        Rejected    // 2 - مرفوض
    }

    public class Course
    {
        [Key]
        public int CourseId { get; set; }

        [DataType(DataType.DateTime)]
        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; private set; } = DateTime.Now;


        [Required(ErrorMessage = "اسم الكورس مطلوب.")]
        public string? CourseName { get; set; }


        [Required(ErrorMessage = "الموقع مطلوب.")]
        public string? Location { get; set; }


        [Required(ErrorMessage = "مواضيع الدورة مطلوبة.")]
        public string? CourseTopics { get; set; }


        [Required(ErrorMessage = "وصف الدورة مطلوب.")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "الفئة المستهدفة مطلوبة.")]
        public string? Targetpeople { get; set; }


        [FutureDate(ErrorMessage = "تاريخ البدء يجب أن يكون اليوم أو في المستقبل")]
        public DateTime StartDate { get; set; } = DateTime.Now;

        [FutureDate(ErrorMessage = "تاريخ الانتهاء يجب أن يكون اليوم أو في المستقبل")]
        public DateTime EndDate { get; set; } = DateTime.Now;

        [Required(ErrorMessage = "مدة الدورة مطلوبة.")]
        public string? Duration { get; set; }


        [Required(ErrorMessage = "الوقت مطلوب.")]
        public string? Time { get; set; }


        [ValidateNever]
        public string? Image { get; set; }

        // 🔄 حالة الكورس: قيد المراجعة / مقبول / مرفوض
        public CourseStatus Status { get; set; } = CourseStatus.Pending;


        [Required(ErrorMessage = "يجب اختيار تصنيف.")]
        public int CategoryId { get; set; }

        [ValidateNever]
        public Category Category { get; set; }

        public List<Question> Questions { get; set; } = new List<Question>();

        public List<Register> Registers { get; set; } = new List<Register>();
        public List<Answer> Answers { get; set; } = new List<Answer>();



        public string? UserId { get; set; }
        [ValidateNever]

        public Trainer Trainer { get; set; }


        [ValidateNever]
        [NotMapped]
        [Display(Name = "Upload Picture")]
        public IFormFile PictureFile { get; set; }

        public class FutureDateAttribute : ValidationAttribute
        {
            public override bool IsValid(object? value)
            {
                if (value is DateTime date)
                {
                    return date.Date >= DateTime.Now.Date;
                }
                return true;
            }
        }


    }

}

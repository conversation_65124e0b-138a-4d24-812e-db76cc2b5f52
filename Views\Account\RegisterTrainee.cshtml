﻿@model TraineeRegisterViewModel

@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <title>تسجيل كمتدرب</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet" />
    <style>
        body {
            background-color: #f5f5f5;
            font-family: 'Segoe UI', sans-serif;
        }

        .form-container {
            background-color: #fdf9f9;
            border-radius: 20px;
            padding: 40px 30px;
            width: 420px;
            margin: auto;
            margin-top: 50px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        .form-label {
            font-weight: bold;
        }

        .custom-input {
            background-color: white;
            border: none;
            border-radius: 12px;
            padding: 10px 15px;
            width: 100%;
            margin-bottom: 15px;
            box-shadow: inset 0 0 3px rgba(0,0,0,0.1);
        }

        .submit-btn {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            font-weight: bold;
            color: #fff;
            border: none;
            border-radius: 12px;
            background: linear-gradient(135deg, #fca311, #fb8500);
            cursor: pointer;
            transition: background 0.3s ease;
        }

            .submit-btn:hover {
                background: linear-gradient(135deg, #ffb703, #e85d04);
                transform: scale(1.02);
            }

        .form-title img {
            width: 50px;
            margin-bottom: 10px;
        }

        .form-title h4 {
            font-weight: bold;
        }
    </style>
</head>
<body>

    <div class="form-container text-center">
        <!-- تغيير الصورة -->
        <div class="form-title">
            <img src="~/Pictures/Roya 2030 Logo.png" alt="register icon" id="registerForm" />
            <h4>تسجيل كمتدرب</h4>
        </div>
        <form asp-action="RegisterTrainee" method="post" enctype="multipart/form-data">
           @*  @if (!ViewData.ModelState.IsValid)
            {
                <div class="alert alert-danger text-center">
                    @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                    {
                        <div>@error.ErrorMessage</div>
                    }
                </div>
            } *@

            <div asp-validation-summary="All" class="text-danger text-center mb-3"></div>


            <div class="text-end">

                <label class="form-label">الاسم</label>
                <input asp-for="FullName" class="custom-input" placeholder="اسمك كامل" />
                <span asp-validation-for="FullName" class="text-danger"></span>

                <label class="form-label">البريد الإلكتروني</label>
                <input asp-for="Email" class="custom-input" placeholder="<EMAIL>" />
                <span asp-validation-for="Email" class="text-danger"></span>

                <label class="form-label">كلمة المرور</label>
                <input asp-for="Password" type="password" class="custom-input" />
                <span asp-validation-for="Password" class="text-danger"></span>

                <label class="form-label">تأكيد كلمة المرور</label>
                <input asp-for="ConfirmPassword" type="password" class="custom-input" />
                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                <label class="form-label">رقم الهاتف</label>
                <div class="input-group">
                    <span class="input-group-text">+2189</span>
                    <input asp-for="PhoneNum"
                           id="phone"
                           class="form-control custom-input"
                           placeholder="أدخل 8 أرقام"
                           inputmode="numeric"
                           maxlength="8"
                           oninput="this.value = this.value.replace(/[^0-9]/g, '').slice(0,8);" />
                </div>
                <span asp-validation-for="PhoneNum" class="text-danger"></span>

                <span asp-validation-for="PhoneNum" class="text-danger"></span>

                <label class="form-label">المدينة</label>
                <input asp-for="City" class="custom-input" placeholder="بنغازي" />
                <span asp-validation-for="City" class="text-danger"></span>

                <label class="form-label">العمر</label>
                <input asp-for="Age" class="custom-input" placeholder="مثال: 30" />
                <span asp-validation-for="Age" class="text-danger"></span>

                <label class="form-label"> المهنة / التخصص</label>
                <input asp-for="MajorOrProfession" class="custom-input" />
                <span asp-validation-for="MajorOrProfession" class="text-danger"></span>

                

                <button type="submit" class="submit-btn">إنشاء</button>
            </div>
        </form>
    </div>
    <script>
        document.getElementById("registerForm").addEventListener("submit", function (e) {
            var phone = document.getElementById("phone").value;
            if (phone.length !== 10) {
                e.preventDefault(); // يمنع الإرسال
                alert("يرجى إدخال رقم هاتف مكون من 10 أرقام فقط.");
            }
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation-unobtrusive@3.2.12/jquery.validate.unobtrusive.min.js"></script>

</body>
</html>
@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}

﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />

    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>@ViewData["Title"] - Roya</title>
  
    <link rel="icon" href="img/favicon.png">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="~/cssvist/bootstrap.min.css">
    <!-- animate CSS -->
    <link rel="stylesheet" href="~/cssvist/animate.css">
    <!-- owl carousel CSS -->
    <link rel="stylesheet" href="~/cssvist/owl.carousel.min.css">
    <!-- themify CSS -->
    <link rel="stylesheet" href="~/cssvist/themify-icons.css">
    <!-- flaticon CSS -->
    <link rel="stylesheet" href="~/cssvist/flaticon.css">
    <!-- font awesome CSS -->
    <link rel="stylesheet" href="~/cssvist/magnific-popup.css">
    <!-- swiper CSS -->
    <link rel="stylesheet" href="~/cssvist/slick.css">
    <!-- style CSS -->
    <link rel="stylesheet" href="~/cssvist/style.css">
</head>


<body>
  
    <!--::header part start::-->
    <header class="main_menu home_menu">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-12">
                    <nav class="navbar navbar-expand-lg navbar-light">

                        <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index"> <img src="~/Pictures/Roya 2030 Logo.png" alt="logo" style="width: 120px !important; height: auto !important;"> </a>

                        <button class="navbar-toggler" type="button" data-toggle="collapse"
                                data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                                aria-expanded="false" aria-label="Toggle navigation">
                            <span class="navbar-toggler-icon"></span>
                        </button>

                        <div class="collapse navbar-collapse main-menu-item justify-content-end"
                             id="navbarSupportedContent">

                            <ul class="navbar-nav align-items-center">

                                <li class="nav-item active">
                                    <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index">Home</a>
                                </li>

                                <li class="nav-item">
                                    <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index">About</a>
                                </li>
                               
                                    <li class="nav-item">
                                        <a class="nav-link" asp-area="" asp-controller="BrowseCourses" asp-action="Index">Courses</a>
                                    </li>
                             
                                 @if (User.Identity.IsAuthenticated && User.IsInRole("Trainee"))
                                {

                                <li class="nav-item">
                                    <a class="nav-link" asp-area="" asp-controller="BrowseCourses" asp-action="MyCourses">MyCourses</a>
                                    </li>
                                }


                                <li class="nav-item">
                                    <a class="nav-link" asp-area="" asp-controller="Account" asp-action="RegisterTrainee">registerT</a>
                                </li>


                                <li class="nav-item">
                                    <a class="nav-link" asp-area="" asp-controller="Account" asp-action="RegisterTrainer">registerTr</a>
                                </li>

                               @*  <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="blog.html" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        Pages
                                    </a>
                                    <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                                        <a class="dropdown-item" href="single-blog.html">Single blog</a>
                                        <a class="dropdown-item" href="elements.html">Elements</a>
                                    </div>
                                </li> *@
                                <li class="nav-item">
                                    <a class="nav-link" asp-area="TrainerA" asp-controller="Home" asp-action="Index">Trainer</a>
                                </li>
                                @if (User.Identity.IsAuthenticated)
                                {
                                    
                                    <li class="d-none d-lg-block">
                                        <a class="btn_1" asp-area="" asp-controller="Account" asp-action="Logout">Logout</a>
                                    </li>
                                }
                                else
                                {
                                    <li class="d-none d-lg-block">
                                        <a class="btn_1" asp-area="" asp-controller="Account" asp-action="login">Login</a>
                                    </li>
                                  
                                }

                            </ul>
                        </div>
                    </nav>
                </div>
            </div>
        </div>
    </header>

    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>




    <footer class="footer-area">
        <div class="container">
            <div class="row justify-content-between">
                <div class="col-sm-6 col-md-4 col-xl-3">
                    <div class="single-footer-widget footer_1">
                        <a href="index.html"> <img src="img/logo.png" alt=""> </a>
                        <p>
                            But when shot real her. Chamber her one visite removal six
                            sending himself boys scot exquisite existend an
                        </p>
                        <p>But when shot real her hamber her </p>
                    </div>
                </div>
                <div class="col-sm-6 col-md-4 col-xl-4">
                    <div class="single-footer-widget footer_2">
                        <h4>Newsletter</h4>
                        <p>
                            Stay updated with our latest trends Seed heaven so said place winged over given forth fruit.
                        </p>
                        <form action="#">
                            <div class="form-group">
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control" placeholder='Enter email address'
                                           onfocus="this.placeholder = ''"
                                           onblur="this.placeholder = 'Enter email address'">
                                    <div class="input-group-append">
                                        <button class="btn btn_1" type="button"><i class="ti-angle-right"></i></button>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class="social_icon">
                            <a href="#"> <i class="ti-facebook"></i> </a>
                            <a href="#"> <i class="ti-instagram"></i> </a>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-sm-6 col-md-4">
                    <div class="single-footer-widget footer_2">
                        <h4>Contact us</h4>
                        <div class="contact_info">
                            <p><span> Address :</span> Hath of it fly signs bear be one blessed after </p>
                            <p><span> Phone :</span> ***** 265 (8060)</p>
                            <p><span> Email : </span><EMAIL> </p>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <div class="copyright_part_text text-center">
                        <div class="row">
                            <div class="col-lg-12">
                                <p class="footer-text m-0">
                                    <!-- Link back to Colorlib can't be removed. Template is licensed under CC BY 3.0. -->
                                    Copyright &copy;<script>document.write(new Date().getFullYear());</script> All rights reserved | This template is made with <i class="ti-heart" aria-hidden="true"></i> by <a href="https://colorlib.com" target="_blank">Colorlib</a>
                                    <!-- Link back to Colorlib can't be removed. Template is licensed under CC BY 3.0. -->
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <!-- footer part end-->

    <!-- jquery plugins here-->
    <!-- jquery -->
    <script src="~/jsvist/jquery-1.12.1.min.js"></script>
    <!-- popper js -->
    <script src="~/jsvist/popper.min.js"></script>
    <!-- bootstrap js -->
    <script src="~/jsvist/bootstrap.min.js"></script>
    <!-- easing js -->
    <script src="~/jsvist/jquery.magnific-popup.js"></script>
    <!-- swiper js -->
    <script src="~/jsvist/swiper.min.js"></script>
    <!-- swiper js -->
    <script src="~/jsvist/masonry.pkgd.js"></script>
    <!-- particles js -->
    <script src="~/jsvist/owl.carousel.min.js"></script>
    <script src="~/jsvist/jquery.nice-select.min.js"></script>
    <!-- swiper js -->
    <script src="~/jsvist/slick.min.js"></script>
    <script src="~/jsvist/jquery.counterup.min.js"></script>
    <script src="~/jsvist/waypoints.min.js"></script>
    <!-- custom js -->
    <script src="~/jsvist/custom.js"></script>

   
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>

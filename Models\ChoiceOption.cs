﻿using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using System.ComponentModel.DataAnnotations;

namespace Roya.Models
{
    public class ChoiceOption
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public string OptionText { get; set; }

        public int QuestionId { get; set; }

        [ValidateNever]
        public Question Question { get; set; }
    }

}

﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Roya.Data;
using Roya.Models;
using Roya.ViewModels;

namespace Roya.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class AdminCoursesController : Controller  // ✅ أضفنا التوريث
    {
        private readonly AppDbContext _context;
        private readonly UserManager<IdentityUser> _userManager;
        public AdminCoursesController(AppDbContext context, UserManager<IdentityUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }
        public async Task<IActionResult> CoursesWithParticipants(string filterBy, string search)
        {
            var query = _context.Courses
                .Include(c => c.Trainer)
                .Include(c => c.Registers)
                .Where(c =>
                    (c.Status == CourseStatus.Approved) ||
                    (c.Status == CourseStatus.Rejected && c.Registers.Any()))
                .AsQueryable();

            // 🧠 الفلترة بالبحث
            if (!string.IsNullOrEmpty(search))
            {
                if (filterBy == "course")
                {
                    query = query.Where(c => c.CourseName.Contains(search));
                }
                else if (filterBy == "trainer")
                {
                    query = query.Where(c => c.Trainer.FullName.Contains(search));
                }
            }

            var result = await query
                .Select(c => new AdminCourseVM
                {
                    CourseId = c.CourseId,
                    CourseName = c.CourseName,
                    TrainerName = c.Trainer.FullName,
                    CourseStatus = c.Status,
                    ParticipantCount = c.Registers.Count
                })
                .ToListAsync();

            return View(result);
        }

       
        public async Task<IActionResult> Participants(int id)
        {
            var course = await _context.Courses
                .Include(c => c.Registers).ThenInclude(r => r.Trainee)
                .FirstOrDefaultAsync(c => c.CourseId == id);

            if (course == null) return NotFound();

            var participants = course.Registers.Select(r => new CourseParticipantVM
            {
                TraineeId = r.Trainee.Id,
                FullName = r.Trainee.FullName,
                RegistrationDate = r.RegistrationDate,
                Status = r.RegistrationStatus,
                CourseId = course.CourseId // ✅ ضروري جدًا
            }).ToList();

            ViewBag.CourseName = course.CourseName;
            return View(participants);
        }

        // ✅ عرض إجابات متدرب معين في دورة معينة (للإدمن)
        public async Task<IActionResult> ViewAnswers(int courseId, string traineeId)
        {
            // نأتي بالتسجيل وليس فقط المتدرب
            var register = await _context.Registers
                .Include(r => r.Trainee)
                .FirstOrDefaultAsync(r => r.CourseId == courseId && r.userId == traineeId);

            if (register == null || register.Trainee == null) return NotFound();

            var answers = await _context.Answers
                .Include(a => a.Question)
                .Where(a => a.CourseId == courseId && a.UserId == traineeId)
                .ToListAsync();

            var vm = new TraineeAnswersVM
            {
                TraineeId = register.Trainee.Id,
                CourseId = courseId,
                FullName = register.Trainee.FullName,
                PhoneNum = register.Trainee.PhoneNum,
                City = register.Trainee.City,
                Age = register.Trainee.Age,
                MajorOrProfession = register.Trainee.MajorOrProfession,
                Answers = answers.Select(a => new AnswerDetailVM
                {
                    QuestionText = a.Question.QuestionText,
                    AnswerText = a.AnswerText
                }).ToList()
            };

            return View("ViewAnswers", vm);
        }
        [HttpPost]
        public async Task<IActionResult> Approve(int id)
        {
            var course = await _context.Courses
                .Include(c => c.Trainer)
                .FirstOrDefaultAsync(c => c.CourseId == id);

            if (course == null)
                return NotFound();

            if (course.Status == CourseStatus.Approved)
            {
                TempData["WarningMessage"] = "⚠️ هذه الدورة مقبولة بالفعل.";
                return Redirect(Request.Headers["Referer"].ToString());
            }

            course.Status = CourseStatus.Approved;

            var log = new CourseStatusLog
            {
                CourseId = course.CourseId,
                CourseName = course.CourseName,
                TrainerName = course.Trainer?.FullName,
                ActionType = "Approved",
                ActionDate = DateTime.Now,
                PerformedBy = "Admin"
            };

            _context.CourseStatusLogs.Add(log);
            await _context.SaveChangesAsync();

            string phone = course.Trainer?.PhoneNum;
            string message = Uri.EscapeDataString($"✔️ تم قبول دورتك ({course.CourseName})، يمكنك الآن متابعتها داخل المنصة.");
            string url = $"https://wa.me/{phone}?text={message}";
            return Redirect(url);
        }

        [HttpPost]
        public async Task<IActionResult> Reject(int id)
        {
            var course = await _context.Courses
                .Include(c => c.Trainer)
                .FirstOrDefaultAsync(c => c.CourseId == id);

            if (course == null)
                return NotFound();

            if (course.Status == CourseStatus.Rejected)
            {
                TempData["WarningMessage"] = "⚠️ هذه الدورة مرفوضة بالفعل.";
                return Redirect(Request.Headers["Referer"].ToString());
            }

            course.Status = CourseStatus.Rejected;

            var log = new CourseStatusLog
            {
                CourseId = course.CourseId,
                CourseName = course.CourseName,
                TrainerName = course.Trainer?.FullName,
                ActionType = "Rejected",
                ActionDate = DateTime.Now,
                PerformedBy = "Admin"
            };

            _context.CourseStatusLogs.Add(log);
            await _context.SaveChangesAsync();

            // إرسال رسالة واتساب إلى المدرب بعد الرفض
            string phone = course.Trainer?.PhoneNum;
            string message = Uri.EscapeDataString($"❌ تم رفض دورتك ({course.CourseName})، نرجو مراجعة ملاحظات المنصة أو التواصل معنا.");
            string url = $"https://wa.me/{phone}?text={message}";
            return Redirect(url);
        }

    }
}


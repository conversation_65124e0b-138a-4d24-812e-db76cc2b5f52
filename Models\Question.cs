﻿using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using System.ComponentModel.DataAnnotations;

namespace Roya.Models
{
    public enum QuestionType
    {
        Text,           // سؤال نصي
        MultipleChoice  // سؤال اختيار من متعدد
    }
    public class Question
    {
        [Key]  
        public int QuestionId { get; set; }

        [Required]
        public string? QuestionText { get; set; }

        public QuestionType Type { get; set; } = QuestionType.Text; // ✅ نوع السؤال


        public int CourseId { get; set; }

        [ValidateNever]
        public Course Course { get; set; }
        public List<Answer> Answers { get; set; } = new List<Answer>();
        public List<ChoiceOption> Options { get; set; } = new List<ChoiceOption>();
        public QuestionType QuestionType { get; internal set; }
    }
}

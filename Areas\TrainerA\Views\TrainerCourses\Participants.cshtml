﻿@model List<Roya.ViewModels.CourseParticipantVM>
@{
    ViewData["Title"] = "الطلبة المشاركون";
    var courseName = ViewBag.CourseName as string;
    int total = ViewBag.TotalCount ?? 0;
    int pending = ViewBag.PendingCount ?? 0;
    int approved = ViewBag.ApprovedCount ?? 0;
    int rejected = ViewBag.RejectedCount ?? 0;
}

<div class="container mt-5" dir="rtl">
    <h2 class="text-center text-success mb-4">👥 الطلبة المشاركون في "@courseName"</h2>

    <div class="row justify-content-center mb-4">
        <div class="col-md-10">
            <div class="d-flex justify-content-around text-center bg-light border rounded p-3 shadow-sm">
                <div>
                    <span class="fw-bold text-dark">العدد الكلي</span><br />
                    <span class="badge bg-dark px-3 py-2">@total</span>
                </div>
                <div>
                    <span class="fw-bold text-warning">قيد المراجعة</span><br />
                    <span class="badge bg-warning text-dark px-3 py-2">@pending</span>
                </div>
                <div>
                    <span class="fw-bold text-success">مقبول</span><br />
                    <span class="badge bg-success px-3 py-2">@approved</span>
                </div>
                <div>
                    <span class="fw-bold text-danger">مرفوض</span><br />
                    <span class="badge bg-danger px-3 py-2">@rejected</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 🔍 نموذج البحث -->
    <div class="row justify-content-center mb-4">
        <div class="col-md-6">
            <form asp-action="Participants" method="get" class="input-group shadow-sm">
                <input type="hidden" name="id" value="@ViewBag.CourseId" />
                <input type="text" name="search" class="form-control" placeholder="🔍 ابحث باسم الطالب..." />
                <button type="submit" class="btn btn-outline-primary">بحث</button>
            </form>
        </div>
    </div>

    @if (ViewBag.NotFound == true)
    {
        <div class="alert alert-danger text-center mb-4" dir="rtl">
            ⚠️ لا يوجد متدرب بهذا الاسم في هذه الدورة.
        </div>
    }
    @if (Model.Any())
    {
        <table class="table table-bordered text-center shadow-sm">
            <thead class="table-light">
                <tr>
                    <th>اسم الطالب</th>
                    <th>تاريخ التسجيل</th>
                    <th>الحالة</th>
                    <th>عرض الإجابات</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var p in Model)
                {
                    <tr>
                        <td>@p.FullName</td>
                        <td>@p.RegistrationDate.ToString("yyyy/MM/dd")</td>
                        <td>
                            @switch (p.Status)
                            {
                                case RegistrationStatus.Pending:
                                    <span class="badge bg-warning text-dark">قيد المراجعة</span>
                                    break;
                                case RegistrationStatus.Approved:
                                    <span class="badge bg-success">مقبول</span>
                                    break;
                                case RegistrationStatus.Rejected:
                                    <span class="badge bg-danger">مرفوض</span>
                                    break;
                            }
                        </td>
                        <td>
                            <a asp-controller="TrainerCourses" asp-action="ViewAnswers" asp-route-courseId="@p.CourseId" asp-route-traineeId="@p.TraineeId" class="btn btn-outline-secondary btn-sm">
                                📄 عرض الإجابات
                            </a>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    }
   
</div>

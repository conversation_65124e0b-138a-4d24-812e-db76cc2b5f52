﻿//using System.Net.Mail;
//using System.Net;
//using Microsoft.AspNetCore.Identity.UI.Services;

//namespace Roya.Services
//{
//    public class EmailSender : IEmailSender
//    {
//        private readonly IConfiguration _config;

//        public EmailSender(IConfiguration config)
//        {
//            _config = config;
//        }

//        public async Task SendEmailAsync(string email, string subject, string htmlMessage)
//        {
//            var smtpClient = new SmtpClient("smtp.gmail.com")
//            {
//                Port = 587,
//                Credentials = new NetworkCredential(
//                    _config["EmailSettings:Email"],
//                    _config["EmailSettings:AppPassword"]
//                ),
//                EnableSsl = true
//            };

//            var mailMessage = new MailMessage
//            {
//                From = new MailAddress(_config["EmailSettings:Email"], "Roya Platform"),
//                Subject = subject,
//                Body = htmlMessage,
//                IsBodyHtml = true
//            };

//            mailMessage.To.Add(email);

//            await smtpClient.SendMailAsync(mailMessage);
//        }
//    }
//}

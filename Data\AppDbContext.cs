﻿using Roya.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;

namespace Roya.Data
{
    public class AppDbContext : IdentityDbContext<IdentityUser, IdentityRole, string>
    { 
        public AppDbContext(DbContextOptions<AppDbContext> options):base(options)
        { }

        public DbSet<Category> Categories { get; set; }
        public DbSet<Course> Courses { get; set; }
        public DbSet<Question> Questions { get; set; }
        public DbSet<Trainee> Trainees { get; set; }
        public DbSet<Register> Registers { get; set; }
        public DbSet<Answer> Answers { get; set; }
        public DbSet<Trainer> Trainers { get; set; }
        public DbSet<TrainerStatusLog> TrainerStatusLogs { get; set; }
        public DbSet<ChoiceOption> ChoiceOptions { get; set; }
        public DbSet<CourseStatusLog> CourseStatusLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<Course>()
                .HasOne(c => c.Category)
                .WithMany(cat => cat.Courses)
                .HasForeignKey(c => c.CategoryId)
                .OnDelete(DeleteBehavior.Cascade);

            // العلاقة: Course -> Questions (One-to-Many)
            modelBuilder.Entity<Question>()
                .HasOne(q => q.Course)
                .WithMany(c => c.Questions)
                .HasForeignKey(q => q.CourseId)
                .OnDelete(DeleteBehavior.Cascade);

            // العلاقة: Course <-> Trainee عبر Register (Many-to-Many)
            modelBuilder.Entity<Register>()
                .HasOne(r => r.Course)
                .WithMany(c => c.Registers)
                .HasForeignKey(r => r.CourseId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Register>()
                .HasOne(r => r.Trainee)
                .WithMany(t => t.Registers)
                .HasForeignKey(r => r.userId)
                .OnDelete(DeleteBehavior.Cascade);

            // العلاقة: Course -> Answers (One-to-Many)
            modelBuilder.Entity<Answer>()
                .HasOne(a => a.Course)
                .WithMany(c => c.Answers)
                .HasForeignKey(a => a.CourseId)
                .OnDelete(DeleteBehavior.NoAction);

            // العلاقة: Trainee -> Answers (One-to-Many)
            modelBuilder.Entity<Answer>()
                .HasOne(a => a.Trainee)
                .WithMany(t => t.Answers)
                .HasForeignKey(a => a.UserId)
                .OnDelete(DeleteBehavior.NoAction);

            // العلاقة: Question -> Answers (One-to-Many)
            modelBuilder.Entity<Answer>()
                .HasOne(a => a.Question)
                .WithMany(q => q.Answers)
                .HasForeignKey(a => a.QuestionId)
                .OnDelete(DeleteBehavior.Cascade); 
            
            
            // العلاقة: Trainer -> Course (One-to-Many)
            modelBuilder.Entity<Course>()
                .HasOne(c=> c.Trainer)
                .WithMany(t => t.Courses)
                .HasForeignKey(c => c.UserId)
                .OnDelete(DeleteBehavior.NoAction);

        }
        }
}

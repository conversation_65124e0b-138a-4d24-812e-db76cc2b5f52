﻿@model Roya.ViewModels.CourseWithQuestionsVM
@using Roya.Models

@{
    ViewData["Title"] = "تعديل الكورس";
    var categories = ViewBag.Categories as List<Category>;
}

<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<div class="container my-5" style="max-width: 900px;">
    <h2 class="text-center mb-4">✏️ تعديل الكورس</h2>

    <div class="card shadow-sm">
        <div class="card-body">

            <form asp-action="Edit" method="post" enctype="multipart/form-data">
                <input type="hidden" asp-for="Course.CourseId" />

                <!-- ✅ صورة الكورس -->
                <div class="text-center mb-4">
                    @if (!string.IsNullOrEmpty(Model.Course.Image))
                    {
                        <img src="@Model.Course.Image" class="rounded-circle shadow" style="width: 150px; height: 150px; object-fit: cover;" />
                    }
                    else
                    {
                        <div class="rounded-circle bg-secondary text-white d-flex justify-content-center align-items-center" style="width: 150px; height: 150px;">
                            بدون صورة
                        </div>
                    }
                    <div class="mt-3">
                        <label class="btn btn-outline-primary">
                            🖼 تغيير الصورة
                            <input asp-for="Course.PictureFile" type="file" hidden />
                        </label>
                        <span asp-validation-for="Course.PictureFile" class="text-danger d-block"></span>
                    </div>
                </div>

                <!-- ✅ معلومات الكورس -->
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">اسم الكورس</label>
                        <input asp-for="Course.CourseName" class="form-control" />
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">الموقع</label>
                        <input asp-for="Course.Location" class="form-control" />
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">تاريخ البدء</label>
                        <input asp-for="Course.StartDate" type="date" class="form-control" />
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">تاريخ الانتهاء</label>
                        <input asp-for="Course.EndDate" type="date" class="form-control" />
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">المدة</label>
                        <input asp-for="Course.Duration" class="form-control" />
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">الوقت</label>
                        <input asp-for="Course.Time" class="form-control" />
                    </div>

                    <div class="col-md-12">
                        <label class="form-label">الفئة المستهدفة</label>
                        <input asp-for="Course.Targetpeople" class="form-control" />
                    </div>

                    <div class="col-md-12">
                        <label class="form-label">تفاصيل الكورس</label>
                        <textarea asp-for="Course.Description" class="form-control"></textarea>
                    </div>

                    <div class="col-md-12">
                        <label class="form-label">مواضيع الكورس</label>
                        <textarea asp-for="Course.CourseTopics" class="form-control"></textarea>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">التصنيف</label>
                        <select asp-for="Course.CategoryId" class="form-select">
                            <option value="">-- اختر تصنيف --</option>
                            @foreach (var c in categories)
                            {
                                <option value="@c.Id">@c.CategoryName</option>
                            }
                        </select>
                    </div>
                </div>

                <hr class="my-4" />

                <!-- ✅ الأسئلة -->
                <h5 class="mb-3 fw-bold">📝 تعديل الأسئلة</h5>
                <div id="questions-container" class="mb-4">
                    @for (int i = 0; i < Model.Questions.Count; i++)
                    {
                        <div class="question-block border rounded p-3 mb-3 shadow-sm bg-light">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">السؤال</label>
                                <button type="button" class="btn btn-sm btn-danger" onclick="removeQuestion(this)">🗑 حذف</button>
                            </div>
                            <input name="Questions[@i].QuestionText" class="form-control mb-2" value="@Model.Questions[i].QuestionText" required />
                            <input type="hidden" name="Questions[@i].QuestionId" value="@Model.Questions[i].QuestionId" />
                            <select name="Questions[@i].QuestionType" class="form-select" onchange="toggleOptions(this)">
                                <option value="Text" selected="@(Model.Questions[i].QuestionType == QuestionType.Text)">سؤال نصي</option>
                                <option value="MultipleChoice" selected="@(Model.Questions[i].QuestionType == QuestionType.MultipleChoice)">سؤال اختياري</option>
                            </select>


                            <div class="options-container mt-2 @(Model.Questions[i].QuestionType == QuestionType.MultipleChoice ? "" : "d-none")">
                                <label class="form-label mt-2">الخيارات (افصل بينها بفاصلة)</label>
                                <input name="Questions[@i].OptionsText" class="form-control" value="@Model.Questions[i].OptionsText" />
                            </div>
                        </div>
                    }
                </div>

                <button type="button" class="btn btn-outline-secondary mb-3" onclick="addQuestion()">➕ إضافة سؤال</button>

                <div class="text-end mt-3">
                    <button type="submit" class="btn btn-success px-4">💾 حفظ التعديلات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- ✅ قالب سؤال جديد -->
<div id="question-template" class="d-none">
    <div class="question-block border rounded p-3 mb-3 shadow-sm bg-light">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <label class="form-label mb-0">سؤال جديد</label>
            <button type="button" class="btn btn-sm btn-danger" onclick="removeQuestion(this)">🗑 حذف</button>
        </div>
        <input name="Questions[__index__].QuestionText" class="form-control mb-2" placeholder="اكتب نص السؤال" required />
        <select name="Questions[__index__].QuestionType" class="form-select" onchange="toggleOptions(this)">
            <option value="Text">سؤال نصي</option>
            <option value="MultipleChoice">سؤال اختياري</option>
        </select>
        <div class="options-container mt-2 d-none">
            <label class="form-label mt-2">الخيارات (افصل بينها بفاصلة)</label>
            <input name="Questions[__index__].OptionsText" class="form-control" placeholder="مثال: نعم, لا, ربما" />
        </div>
    </div>
</div>

<!-- ✅ سكربت للأسئلة -->
<script>
    let questionIndex = @Model.Questions.Count;

    function addQuestion() {
        const template = document.getElementById("question-template").innerHTML;
        const html = template.replace(/__index__/g, questionIndex);
        document.getElementById("questions-container").insertAdjacentHTML("beforeend", html);
        questionIndex++;
    }

    function removeQuestion(button) {
        button.closest(".question-block").remove();
    }

    function toggleOptions(select) {
        const optionsContainer = select.closest(".question-block").querySelector(".options-container");
        optionsContainer.classList.toggle("d-none", select.value !== "MultipleChoice");
    }
</script>

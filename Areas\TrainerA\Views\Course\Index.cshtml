﻿@model Roya.Models.Course
@using Roya.Models
@{
    ViewData["Title"] = "إضافة كورس جديد";
    var categories = ViewBag.Categories as List<Category>;
}

<!-- ✅ تصميم احترافي لإضافة كورس -->
<div class="container mt-5" style="max-width: 900px;">
    <h2 class="text-center mb-4 fw-bold">➕ إضافة كورس جديد</h2>

    <form asp-action="Create" method="post">
        <div class="row">
            <div class="col-md-6 mb-3">
                <label class="form-label">اسم الكورس</label>
                <input asp-for="CourseName" class="form-control" />
            </div>
            <div class="col-md-6 mb-3">
                <label class="form-label">المكان</label>
                <input asp-for="Location" class="form-control" />
            </div>

            <div class="col-md-6 mb-3">
                <label class="form-label">تاريخ البدء</label>
                <input asp-for="StartDate" type="date" class="form-control" />
            </div>
            <div class="col-md-6 mb-3">
                <label class="form-label">تاريخ الانتهاء</label>
                <input asp-for="EndDate" type="date" class="form-control" />
            </div>

            <div class="col-md-6 mb-3">
                <label class="form-label">المدة</label>
                <input asp-for="Duration" class="form-control" />
            </div>
            <div class="col-md-6 mb-3">
                <label class="form-label">الوقت</label>
                <input asp-for="Time" class="form-control" />
            </div>

            <div class="col-md-12 mb-3">
                <label class="form-label">الفئة المستهدفة</label>
                <input asp-for="Targetpeople" class="form-control" />
            </div>
            <div class="col-md-12 mb-3">
                <label class="form-label">تفاصيل الكورس</label>
                <textarea asp-for="Description" class="form-control" rows="3"></textarea>
            </div>

            <div class="col-md-6 mb-4">
                <label class="form-label">التصنيف</label>
                <select asp-for="CategoryId" class="form-select">
                    <option value="">اختر تصنيف</option>
                    @foreach (var c in categories)
                    {
                        <option value="@c.Id">@c.CategoryName</option>
                    }
                </select>
            </div>
        </div>

        <!-- ✅ الأسئلة الديناميكية -->
        <div class="card border shadow-sm mb-4">
            <div class="card-header fw-bold">أسئلة الكورس</div>
            <div class="card-body" id="questions-container">
                <!-- الأسئلة تُضاف هنا تلقائيًا -->
            </div>
            <div class="card-footer text-end">
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addQuestion()">➕ إضافة سؤال</button>
            </div>
        </div>

        <!-- ✅ زر الحفظ -->
        <div class="text-end">
            <button type="submit" class="btn btn-success px-4">💾 حفظ الكورس</button>
        </div>
    </form>
</div>

<!-- ✅ Script للأسئلة -->
<script>
    let questionCount = 0;

    function addQuestion() {
        questionCount++;
        const container = document.getElementById("questions-container");
        const questionHtml = `
        <div class="mb-3 p-3 border rounded position-relative bg-light">
            <label class="form-label">السؤال رقم ${questionCount}</label>
            <input type="text" name="questions[\${questionCount - 1}].QuestionText" class="form-control mb-2" placeholder="أدخل نص السؤال" required />
            <button type="button" class="btn btn-sm btn-outline-danger position-absolute top-0 end-0 m-2" onclick="this.parentElement.remove()">🗑</button>
        </div>`;
        container.insertAdjacentHTML("beforeend", questionHtml);
    }
</script>

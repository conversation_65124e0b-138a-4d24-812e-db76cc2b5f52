﻿@model IEnumerable<Roya.Models.Course>
@using Roya.Models
@{
    ViewData["Title"] = "الدورات التدريبية";
    var categories = ViewBag.Categories as List<Category>;
}

<section class="special_cource padding_top">
    <div class="container">

        <div class="row justify-content-center mb-4">
            <div class="col-lg-8 text-center">
                <h2 class="fw-bold">📚 الدورات التدريبية</h2>
                <p class="text-muted">اكتشف الدورات المتاحة واشترك في ما يناسبك</p>
            </div>
        </div>

        <!-- ✅ شريط البحث والفلترة -->
        <div class="row justify-content-center mb-5" dir="rtl">
            <div class="col-lg-10">
                <form asp-action="Index" method="get" class="card shadow-sm p-4 bg-light rounded">
                    <div class="row gy-3 gx-4 align-items-center">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">🔍 البحث باسم الدورة:</label>
                            <input type="text" name="search" class="form-control" placeholder="اكتب اسم الدورة..." />
                        </div>

                        <div class="col-md-4">
                            <label class="form-label fw-bold">📂 التصنيفات:</label>
                            <select name="categoryId" class="form-select">
                                <option value="">كل التصنيفات</option>
                                @foreach (var category in categories!)
                                {
                                    <option value="@category.Id">@category.CategoryName</option>
                                }
                            </select>
                        </div>

                        <div class="col-md-2">
                            <label class="form-label invisible">زر</label>
                            <button type="submit" class="btn btn-dark w-100">تطبيق الفلترة</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 🧾 عرض الكورسات -->
        <div class="row">
            @foreach (var course in Model)
            {
                <div class="col-sm-6 col-lg-4 mb-4">
                    <div class="single_special_cource shadow rounded h-100">
                        <div class="special_img_wrapper rounded-top overflow-hidden" style="height: 200px;">
                            <img src="~/Pictures/@course.Image" alt="Course Image" class="w-100 h-100 object-fit-cover">
                        </div>
                        <div class="special_cource_text p-3 d-flex flex-column justify-content-between">
                            <div>
                                <a class="btn_4 btn-sm mb-2">@course.Category?.CategoryName</a>
                                <h5 class="text-primary">@course.Duration</h5>
                                <h4 class="mb-2">@course.CourseName</h4>
                                <p class="text-muted">
                                    @(course.Description?.Length > 100 ? course.Description.Substring(0, 100) + "..." : course.Description)
                                </p>
                                <div class="author_info d-flex align-items-center mt-3">
                                    <div class="author_img me-2">
                                        <img src="~/img/author/author_1.png" alt="" class="rounded-circle" width="40">
                                    </div>
                                    <div class="author_info_text">
                                        <p class="mb-0">من إعداد:</p>
                                        <h6 class="mb-0">@course.Trainer?.FullName</h6>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-end text-secondary mt-3">📅 تبدأ: @course.StartDate.ToString("yyyy/MM/dd")</p>
                                <div class="d-flex justify-content-between mt-2">
                                    <a asp-action="Details" asp-route-id="@course.CourseId" class="btn btn-outline-primary btn-sm">📋 عرض التفاصيل</a>
                                    @if (User.Identity.IsAuthenticated && User.IsInRole("Trainee"))
                                    {
                                        <a asp-action="Join" asp-route-id="@course.CourseId" class="btn btn-success btn-sm">📝 اشتراك</a>
                                    }
                                    else if (!User.Identity.IsAuthenticated)
                                    {
                                        <span class="text-muted small">🔒 لتتمكن من الاشتراك، <a asp-controller="Account" asp-action="Login">سجّل الدخول</a> أو <a asp-controller="Account" asp-action="Register">أنشئ حسابًا</a>.</span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</section>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        @if (ViewBag.JoinSuccess != null)
        {
            <text>
                    Swal.fire({
                        icon: 'success',
                        title: 'تم الاشتراك!',
                        text: '@Html.Raw(ViewBag.JoinSuccess)',
                        confirmButtonText: 'حسنًا',
                        confirmButtonColor: '#198754'
                    });
            </text>
        }
        else if (ViewBag.AlreadyRegistered != null)
        {
            <text>
                    Swal.fire({
                        icon: 'warning',
                        title: 'تنبيه!',
                        text: '@Html.Raw(ViewBag.AlreadyRegistered)',
                        confirmButtonText: 'موافق',
                        confirmButtonColor: '#d33'
                    });
            </text>
        }
    </script>
}

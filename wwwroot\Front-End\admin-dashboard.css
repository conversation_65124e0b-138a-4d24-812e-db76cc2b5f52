/* متغيرات CSS */
:root {
    --primary-color: #ff5722;
    --primary-dark: #e64a19;
    --secondary-color: #2c3e50;
    --text-dark: #2c3e50;
    --text-muted: #6c757d;
    --bg-light: #f8f9fa;
    --light-color: #ffffff;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* تصميم لوحة التحكم */
.admin-layout {
    min-height: 100vh;
    background-color: #f8f9fa;
}

/* حالة تصغير الشريط الجانبي */
.admin-layout.sidebar-collapsed .sidebar {
    width: 80px;
}

.main-content {
    margin-right: 280px;
    transition: margin-right 0.3s ease;
}

.admin-layout.sidebar-collapsed .main-content {
    margin-right: 80px;
}

/* إخفاء النصوص في الحالة المصغرة */
.sidebar-header h3,
.nav-link span {
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.admin-layout.sidebar-collapsed .sidebar-header h3,
.admin-layout.sidebar-collapsed .nav-link span {
    opacity: 0;
    visibility: hidden;
}

/* تصغير الشريط الجانبي */
.sidebar-header {
    transition: padding 0.3s ease;
}

.admin-layout.sidebar-collapsed .sidebar-header {
    padding: 0.8rem 0.3rem;
    justify-content: center;
    flex-direction: column;
    gap: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 0.5rem;
}

/* إخفاء اللوجو في الحالة المصغرة */
.admin-layout.sidebar-collapsed .logo {
    display: none;
}

/* تحسين عرض الزر في الحالة المصغرة */
.admin-layout.sidebar-collapsed .sidebar-toggle-btn {
    margin: 0 auto;
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
}

.nav-link {
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    margin-bottom: 0.3rem;
    border-radius: 8px;
}

.admin-layout.sidebar-collapsed .nav-link {
    justify-content: center;
    padding: 0.8rem 0;
    margin: 0.2rem auto;
    gap: 0;
    min-height: 45px;
    display: flex;
    align-items: center;
    width: 50px;
}

.admin-layout.sidebar-collapsed .nav-link i {
    margin: 0 auto;
    font-size: 1.4rem;
    width: 100%;
    text-align: center;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

/* تأثيرات جميلة للأيقونات */
.admin-layout.sidebar-collapsed .nav-link:hover i {
    color: #fff;
    transform: scale(1.1);
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.6);
}

/* إخفاء القوائم الفرعية في الحالة المصغرة */
.admin-layout.sidebar-collapsed .submenu {
    display: none !important;
}

/* إخفاء أسهم القوائم الفرعية */
.admin-layout.sidebar-collapsed .nav-link .fa-chevron-down {
    display: none;
}

/* تحسين عرض الأيقونات في الحالة المصغرة */
.admin-layout.sidebar-collapsed .nav-item {
    margin-bottom: 0.1rem;
    width: 100%;
    display: flex;
    justify-content: center;
}

.admin-layout.sidebar-collapsed .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* تحسين القائمة في الحالة المصغرة */
.admin-layout.sidebar-collapsed .sidebar-nav {
    padding: 0.5rem 0;
}

.admin-layout.sidebar-collapsed .sidebar-nav ul {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.1rem;
}

/* تحسين الحالة النشطة في الأيقونات */
.admin-layout.sidebar-collapsed .nav-item.active .nav-link {
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.admin-layout.sidebar-collapsed .nav-item.active .nav-link i {
    color: #fff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* تحسين التمرير في الحالة المصغرة */
.admin-layout.sidebar-collapsed .sidebar {
    overflow-x: hidden;
    overflow-y: auto;
}

.admin-layout.sidebar-collapsed .sidebar::-webkit-scrollbar {
    width: 3px;
}

.admin-layout.sidebar-collapsed .sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.admin-layout.sidebar-collapsed .sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}



/* تدوير زر التبديل */
.sidebar-toggle-btn i {
    transition: transform 0.3s ease;
}

.admin-layout.sidebar-collapsed .sidebar-toggle-btn i {
    transform: rotate(180deg);
}

/* إخفاء overflow في الحالة المصغرة */
.admin-layout.sidebar-collapsed .sidebar {
    overflow: hidden;
}

.admin-layout.sidebar-collapsed .sidebar:hover {
    overflow-y: auto;
}

/* الشريط الجانبي */
.sidebar {
    width: 280px;
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    color: white;
    position: fixed;
    right: 0;
    top: 0;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    transition: width 0.3s ease;
    transform-origin: right center;
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header .logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-header .logo img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

/* التنقل */
.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.nav-item.active .nav-link {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border-left: 4px solid white;
}

.nav-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.submenu-arrow {
    margin-right: auto;
    transition: transform 0.3s ease;
}

.has-submenu.open .submenu-arrow {
    transform: rotate(180deg);
}

/* القائمة الفرعية */
.submenu {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background-color: rgba(0, 0, 0, 0.1);
}

.has-submenu.open .submenu {
    max-height: 200px;
}

.submenu li a {
    display: block;
    padding: 0.8rem 1.5rem 0.8rem 3.5rem;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
}

.submenu li a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: 280px;
    background-color: #f8f9fa;
}

/* الهيدر العلوي */
.main-header {
    background: white;
    padding: 1.5rem 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-bottom: 1px solid #e9ecef;
    position: relative;
    z-index: 100;
    transition: margin-right 0.3s ease;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* زر تصغير/توسيع الشريط الجانبي */
.sidebar-toggle-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    backdrop-filter: blur(10px);
}

.sidebar-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.sidebar-toggle-btn i {
    transition: transform 0.3s ease;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* زر إخفاء/إظهار الشريط الجانبي */
.sidebar-toggle-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(255, 87, 34, 0.3);
}

.sidebar-toggle-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 87, 34, 0.4);
}

.sidebar-toggle-btn:active {
    transform: translateY(0);
}

/* تأثير دوران الأيقونة */
.sidebar-toggle-btn i {
    transition: transform 0.3s ease;
}

.sidebar-toggle-btn.active i {
    transform: rotate(90deg);
}

.header-content h1 {
    margin: 0;
    color: var(--secondary-color);
    font-size: 1.8rem;
    font-weight: 600;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar img {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    color: var(--secondary-color);
}

.user-role {
    font-size: 0.9rem;
    color: #666;
}

/* محتوى الداشبورد */
.dashboard-content {
    padding: 2rem;
}

/* شبكة الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 30px rgba(255, 87, 34, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-info h3 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 700;
    color: var(--secondary-color);
}

.stat-info p {
    margin: 0;
    color: #666;
    font-size: 0.95rem;
}

/* الأنشطة الحديثة */
.recent-activities {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.section-header h2 {
    margin: 0;
    color: var(--secondary-color);
    font-size: 1.5rem;
    font-weight: 600;
}

.view-all {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.view-all:hover {
    color: #e64a19;
}

.activities-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background-color: rgba(255, 87, 34, 0.05);
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, rgba(255, 87, 34, 0.1), rgba(255, 107, 53, 0.1));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
}

.activity-content p {
    margin: 0 0 0.3rem 0;
    color: var(--secondary-color);
}

.activity-time {
    font-size: 0.85rem;
    color: #666;
}

/* الإحصائيات السريعة */
.quick-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
    text-align: center;
    min-width: 150px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(255, 87, 34, 0.15);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

/* قسم الطلبات */
.requests-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* شبكة الطلبات */
.requests-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

/* كارد الطلب */
.request-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
}

.request-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 30px rgba(255, 87, 34, 0.15);
    border-color: var(--primary-color);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.trainer-avatar img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid var(--primary-color);
}

.trainer-info h3 {
    margin: 0 0 0.3rem 0;
    color: var(--secondary-color);
    font-size: 1.1rem;
    font-weight: 600;
}

.request-date {
    font-size: 0.85rem;
    color: #666;
}

.request-status {
    margin-right: auto;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.request-status.pending {
    background: rgba(255, 193, 7, 0.1);
    color: #ff8f00;
}

.request-status.approved {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.request-status.rejected {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.card-content {
    padding: 1.5rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.info-item i {
    color: var(--primary-color);
    width: 16px;
    text-align: center;
}

.card-actions {
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    display: flex;
    gap: 0.8rem;
    flex-wrap: wrap;
}

/* الأزرار */
.btn {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #e64a19;
}

.btn-success {
    background: #4caf50;
    color: white;
}

.btn-success:hover {
    background: #45a049;
}

.btn-danger {
    background: #f44336;
    color: white;
}

.btn-danger:hover {
    background: #da190b;
}

.btn-info {
    background: #2196f3;
    color: white;
}

.btn-info:hover {
    background: #1976d2;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1rem;
}

/* صفحة تفاصيل المدرب */
.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.trainer-details-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.trainer-profile {
    display: flex;
    gap: 2rem;
    padding: 3rem;
    background: linear-gradient(135deg, rgba(255, 87, 34, 0.05), rgba(255, 107, 53, 0.05));
    border-bottom: 1px solid #e9ecef;
}

.profile-image {
    position: relative;
    text-align: center;
}

.profile-image img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 5px solid white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.status-badge {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.profile-info h2 {
    margin: 0 0 0.5rem 0;
    color: var(--secondary-color);
    font-size: 2rem;
    font-weight: 700;
}

.profile-title {
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 2rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.8rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.contact-item i {
    color: var(--primary-color);
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
}

.profile-details {
    padding: 3rem;
}

.details-section h3 {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--secondary-color);
    font-size: 1.5rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(255, 87, 34, 0.1);
}

.details-section h3 i {
    color: var(--primary-color);
}

.bio-content {
    line-height: 1.8;
    color: var(--secondary-color);
}

.bio-content p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.bio-content h4 {
    color: var(--primary-color);
    margin: 2rem 0 1rem 0;
    font-size: 1.2rem;
}

.skills-list, .certificates-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.8rem;
    margin-bottom: 2rem;
}

.skills-list li, .certificates-list li {
    background: rgba(255, 87, 34, 0.1);
    padding: 0.8rem;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
    font-weight: 500;
}

.experience-item {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    border-left: 4px solid var(--primary-color);
}

.experience-item strong {
    color: var(--secondary-color);
    display: block;
    margin-bottom: 0.5rem;
}

.action-buttons {
    padding: 2rem 3rem;
    background: #f8f9fa;
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* قسم الجدول */
.table-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

/* أدوات البحث والفلترة */
.search-filters {
    display: flex;
    gap: 2rem;
    margin: 2rem 0;
    flex-wrap: wrap;
    align-items: center;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.search-box input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
}

.filter-group {
    display: flex;
    gap: 1rem;
}

.filter-group select {
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
}

/* الجدول - تحسينات شاملة */
.table-container {
    overflow-x: auto;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    -webkit-overflow-scrolling: touch;
    margin-bottom: 2rem;
}

/* جدول دوراتي */
.courses-table-container {
    overflow-x: auto;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    -webkit-overflow-scrolling: touch;
    margin-bottom: 2rem;
}

.courses-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    table-layout: fixed;
    min-width: 800px;
}

.courses-table th {
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    color: white;
    padding: 1.2rem;
    text-align: right;
    font-weight: 600;
    font-size: 0.95rem;
    white-space: nowrap;
}

.courses-table td {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
    text-align: right;
    word-wrap: break-word;
}

.courses-table tr:hover {
    background-color: rgba(255, 87, 34, 0.05);
}

/* تحديد عروض أعمدة جدول الدورات */
.courses-table th:nth-child(1),
.courses-table td:nth-child(1) {
    width: 200px; /* اسم الدورة */
}

.courses-table th:nth-child(2),
.courses-table td:nth-child(2) {
    width: 150px; /* المدرب */
}

.courses-table th:nth-child(3),
.courses-table td:nth-child(3) {
    width: 130px; /* تاريخ التقديم */
    text-align: center;
}

.courses-table th:nth-child(4),
.courses-table td:nth-child(4) {
    width: 130px; /* تاريخ البدء */
    text-align: center;
}

.courses-table th:nth-child(5),
.courses-table td:nth-child(5) {
    width: 120px; /* الحالة */
    text-align: center;
}

.courses-table th:nth-child(6),
.courses-table td:nth-child(6) {
    width: 150px; /* الإجراءات */
    text-align: center;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    table-layout: auto;
    min-width: 1000px;
}

.data-table th {
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    color: white;
    padding: 1rem 0.5rem;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    white-space: nowrap;
    border: 1px solid #dee2e6;
    position: relative;
}

.data-table td {
    padding: 0.8rem 0.5rem;
    border: 1px solid #e9ecef;
    vertical-align: middle;
    text-align: center;
    word-wrap: break-word;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
}

/* تحديد عروض الأعمدة بدقة عالية */
.data-table th:nth-child(1),
.data-table td:nth-child(1) {
    width: 80px !important; /* عمود الصورة */
    max-width: 80px !important;
    min-width: 80px !important;
    text-align: center !important;
}

.data-table th:nth-child(2),
.data-table td:nth-child(2) {
    width: 180px !important; /* عمود الاسم */
    max-width: 180px !important;
    min-width: 180px !important;
    text-align: right !important;
}

.data-table th:nth-child(3),
.data-table td:nth-child(3) {
    width: 220px !important; /* عمود البريد الإلكتروني */
    max-width: 220px !important;
    min-width: 220px !important;
    text-align: right !important;
}

.data-table th:nth-child(4),
.data-table td:nth-child(4) {
    width: 120px !important; /* عمود سنوات الخبرة */
    max-width: 120px !important;
    min-width: 120px !important;
    text-align: center !important;
}

.data-table th:nth-child(5),
.data-table td:nth-child(5) {
    width: 130px !important; /* عمود الحالة */
    max-width: 130px !important;
    min-width: 130px !important;
    text-align: center !important;
}

.data-table th:nth-child(6),
.data-table td:nth-child(6) {
    width: 140px !important; /* عمود تاريخ التسجيل */
    max-width: 140px !important;
    min-width: 140px !important;
    text-align: center !important;
}

.data-table th:nth-child(7),
.data-table td:nth-child(7) {
    width: 170px !important; /* عمود الإجراءات */
    max-width: 170px !important;
    min-width: 170px !important;
    text-align: center !important;
}

.data-table tr:hover {
    background-color: rgba(255, 87, 34, 0.05);
}

/* إعادة تعيين كامل للجدول */
.data-table {
    border-collapse: collapse !important;
    table-layout: fixed !important;
}

.data-table th,
.data-table td {
    border: 1px solid #dee2e6 !important;
    padding: 0.8rem 0.4rem !important;
    vertical-align: middle !important;
    text-align: center !important;
    box-sizing: border-box !important;
    position: relative !important;
}

.data-table th:nth-child(2),
.data-table td:nth-child(2),
.data-table th:nth-child(3),
.data-table td:nth-child(3) {
    text-align: right !important;
}

/* إصلاح مشكلة تداخل المحتوى */
.data-table td {
    overflow: hidden;
    text-overflow: ellipsis;
}

.data-table td:nth-child(5) {
    /* عمود الحالة - تأكيد عدم التداخل */
    padding: 0.8rem 0.5rem;
    vertical-align: middle;
}

.data-table td:nth-child(5) .status-badge {
    display: block;
    margin: 0 auto;
    width: fit-content;
    max-width: 100%;
}

/* تأكيد عدم تداخل البريد الإلكتروني */
.data-table td:nth-child(3) {
    font-size: 0.85rem;
    word-break: break-word;
    overflow-wrap: break-word;
}

/* إصلاح شامل لجميع الجداول */
.data-table {
    border-spacing: 0;
    border-collapse: separate;
}

.data-table th,
.data-table td {
    border: 1px solid #e9ecef;
    box-sizing: border-box;
}

.data-table th {
    border-bottom: 2px solid #dee2e6;
}

/* إصلاح محدد لعمود الحالة */
.data-table td:nth-child(4),
.data-table td:nth-child(5) {
    position: relative;
    overflow: visible;
}

.data-table .status-badge,
.data-table .category-badge {
    position: relative;
    z-index: 1;
    display: inline-block;
    margin: 0;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    white-space: nowrap;
    text-align: center;
    min-width: 80px;
    max-width: 100%;
    box-sizing: border-box;
}

/* حل قوي لمشكلة التداخل */
.data-table tbody tr {
    height: 60px;
}

.data-table tbody td {
    height: 60px;
    vertical-align: middle;
    padding: 0.8rem 0.5rem;
    border-right: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
    position: relative;
    overflow: hidden;
}

.data-table tbody td:first-child {
    border-right: none;
}

.data-table tbody td:last-child {
    border-left: 1px solid #e9ecef;
}

/* إصلاح نهائي لمشكلة التداخل */
.data-table td {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.data-table td:nth-child(2),
.data-table td:nth-child(3) {
    white-space: normal;
    word-break: break-word;
}

/* تأكيد عدم تداخل الحالة */
.data-table td:nth-child(5) .status-badge {
    display: inline-block;
    width: auto;
    max-width: 100%;
    margin: 0;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    white-space: nowrap;
    text-align: center;
}

/* تأكيد عدم تداخل التصنيف */
.data-table td:nth-child(3) .category-badge,
.data-table td:nth-child(4) .category-badge {
    display: inline-block;
    width: auto;
    max-width: 100%;
    margin: 0;
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    white-space: nowrap;
    text-align: center;
}

/* إصلاح خاص للأزرار */
.data-table td:nth-child(7) .actions {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.3rem;
    flex-wrap: nowrap;
}

/* حل جذري لمشكلة التداخل */
.table-container {
    overflow-x: auto;
    border: 1px solid #e9ecef;
    border-radius: 10px;
}

.data-table {
    width: 1040px !important; /* عرض ثابت */
    min-width: 1040px !important;
    max-width: 1040px !important;
    border-collapse: collapse !important;
    table-layout: fixed !important;
    margin: 0 !important;
}

/* إعادة تعريف كامل للأعمدة */
.data-table colgroup {
    display: table-column-group;
}

.data-table col:nth-child(1) { width: 80px !important; }   /* الصورة */
.data-table col:nth-child(2) { width: 180px !important; }  /* الاسم */
.data-table col:nth-child(3) { width: 220px !important; }  /* البريد */
.data-table col:nth-child(4) { width: 120px !important; }  /* الخبرة */
.data-table col:nth-child(5) { width: 130px !important; }  /* الحالة */
.data-table col:nth-child(6) { width: 140px !important; }  /* التاريخ */
.data-table col:nth-child(7) { width: 170px !important; }  /* الإجراءات */

/* CSS خاص لجدول الدورات (بدون عمود الصورة) */
.data-table:not(:has(.trainer-thumb)) {
    width: 1120px !important;
    min-width: 1120px !important;
    max-width: 1120px !important;
}

.data-table:not(:has(.trainer-thumb)) col:nth-child(1) { width: 250px !important; }  /* اسم الدورة */
.data-table:not(:has(.trainer-thumb)) col:nth-child(2) { width: 180px !important; }  /* اسم المدرب */
.data-table:not(:has(.trainer-thumb)) col:nth-child(3) { width: 150px !important; }  /* التصنيف */
.data-table:not(:has(.trainer-thumb)) col:nth-child(4) { width: 130px !important; }  /* الحالة */
.data-table:not(:has(.trainer-thumb)) col:nth-child(5) { width: 140px !important; }  /* تاريخ الإضافة */
.data-table:not(:has(.trainer-thumb)) col:nth-child(6) { width: 100px !important; }  /* المدة */
.data-table:not(:has(.trainer-thumb)) col:nth-child(7) { width: 170px !important; }  /* الإجراءات */

/* تحسينات الداشبورد للشاشات المتوسطة */
@media (max-width: 1024px) {
    .admin-layout {
        grid-template-columns: 250px 1fr;
    }

    .sidebar {
        width: 250px;
    }

    .admin-layout.sidebar-collapsed .sidebar {
        width: 70px;
    }

    .admin-layout.sidebar-collapsed .main-content {
        margin-right: 70px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .dashboard-content {
        padding: 1.5rem;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 1200px) {
    .data-table th:nth-child(3),
    .data-table td:nth-child(3) {
        width: 160px; /* تقليل عرض البريد الإلكتروني */
        font-size: 0.85rem;
    }

    .data-table th:nth-child(2),
    .data-table td:nth-child(2) {
        width: 150px; /* تقليل عرض الاسم */
    }
}

@media (max-width: 992px) {
    .table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .data-table {
        min-width: 800px; /* عرض أدنى للجدول */
    }

    .data-table th,
    .data-table td {
        padding: 0.8rem 0.5rem;
        font-size: 0.85rem;
    }

    .data-table th:nth-child(3),
    .data-table td:nth-child(3) {
        width: 140px;
        font-size: 0.8rem;
    }
}

/* تحسينات للهواتف والأجهزة الصغيرة */
@media (max-width: 768px) {
    .admin-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }

    /* إخفاء الزر في الشاشات الصغيرة لتجنب التكرار */
    .sidebar-toggle-btn {
        display: none;
    }

    .sidebar {
        position: fixed;
        top: 0;
        right: -280px;
        width: 280px;
        height: 100vh;
        z-index: 1000;
        transition: right 0.3s ease;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    }

    .sidebar.active {
        right: 0;
    }

    .main-content {
        margin-right: 0;
        width: 100%;
    }

    .main-header {
        padding: 1rem;
        position: relative;
    }

    .main-header::before {
        content: '';
        position: absolute;
        top: 50%;
        right: 1rem;
        transform: translateY(-50%);
        width: 30px;
        height: 20px;
        background: linear-gradient(
            to bottom,
            var(--primary-color) 0%,
            var(--primary-color) 20%,
            transparent 20%,
            transparent 40%,
            var(--primary-color) 40%,
            var(--primary-color) 60%,
            transparent 60%,
            transparent 80%,
            var(--primary-color) 80%,
            var(--primary-color) 100%
        );
        cursor: pointer;
        z-index: 1001;
    }

    .dashboard-content {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .data-table {
        min-width: 700px;
    }

    .data-table th,
    .data-table td {
        padding: 0.6rem 0.4rem;
        font-size: 0.8rem;
    }

    .data-table th:nth-child(1),
    .data-table td:nth-child(1) {
        width: 60px;
    }

    .data-table th:nth-child(2),
    .data-table td:nth-child(2) {
        width: 120px;
    }

    .data-table th:nth-child(3),
    .data-table td:nth-child(3) {
        width: 120px;
        font-size: 0.75rem;
    }

    .data-table th:nth-child(4),
    .data-table td:nth-child(4) {
        width: 80px;
    }

    .data-table th:nth-child(5),
    .data-table td:nth-child(5) {
        width: 90px;
    }

    .data-table th:nth-child(6),
    .data-table td:nth-child(6) {
        width: 100px;
    }

    .data-table th:nth-child(7),
    .data-table td:nth-child(7) {
        width: 120px;
    }

    /* جدول الدورات للهواتف */
    .courses-table {
        min-width: 650px;
    }

    .courses-table th,
    .courses-table td {
        padding: 0.6rem 0.4rem;
        font-size: 0.8rem;
    }

    .courses-table th:nth-child(1),
    .courses-table td:nth-child(1) {
        width: 150px;
    }

    .courses-table th:nth-child(2),
    .courses-table td:nth-child(2) {
        width: 120px;
    }

    .courses-table th:nth-child(3),
    .courses-table td:nth-child(3) {
        width: 100px;
    }

    .courses-table th:nth-child(4),
    .courses-table td:nth-child(4) {
        width: 100px;
    }

    .courses-table th:nth-child(5),
    .courses-table td:nth-child(5) {
        width: 90px;
    }

    .courses-table th:nth-child(6),
    .courses-table td:nth-child(6) {
        width: 120px;
    }
}

/* تحسينات للهواتف الصغيرة جداً */
@media (max-width: 480px) {
    .sidebar {
        width: 100%;
        right: -100%;
    }

    .main-header h1 {
        font-size: 1.5rem;
        margin-right: 3rem;
    }

    .dashboard-content {
        padding: 0.5rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-card h3 {
        font-size: 1.5rem;
    }

    .search-filters {
        flex-direction: column;
        gap: 1rem;
    }

    .search-box {
        width: 100%;
    }

    .filter-group {
        width: 100%;
        justify-content: stretch;
    }

    .filter-group select {
        width: 100%;
    }

    .btn-icon {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }

    .status-badge {
        padding: 0.3rem 0.6rem;
        font-size: 0.7rem;
        min-width: 70px;
    }

    .actions {
        gap: 0.3rem;
    }

    /* تحسينات إضافية للجداول */
    .data-table,
    .courses-table {
        font-size: 0.75rem;
    }

    .data-table th,
    .data-table td,
    .courses-table th,
    .courses-table td {
        padding: 0.5rem 0.3rem;
    }
}

.trainer-thumb {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    display: inline-block;
    text-align: center;
    white-space: nowrap;
    min-width: 90px;
    max-width: 100%;
    box-sizing: border-box;
    line-height: 1.2;
    position: relative;
}

.status-badge.approved {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.status-badge.pending {
    background: rgba(255, 193, 7, 0.1);
    color: #ff8f00;
}

.status-badge.rejected {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

/* تحسينات إضافية للـ status badges */
.status-badge.status-accepted,
.status-badge.status-approved {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.status-badge.status-pending {
    background: rgba(255, 193, 7, 0.1);
    color: #ff8f00;
}

.status-badge.status-rejected {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

/* تحسينات للكروت */
.category-badge {
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    white-space: nowrap;
}

.category-badge.tech {
    background: rgba(33, 150, 243, 0.1);
    color: #2196f3;
}

.category-badge.business {
    background: rgba(156, 39, 176, 0.1);
    color: #9c27b0;
}

.category-badge.design {
    background: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.students-count {
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    color: #666;
    font-size: 0.9rem;
}

/* أزرار الإجراءات */
.actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
}

.btn-icon {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-icon:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-icon.btn-info {
    background: #2196f3;
    color: white;
}

.btn-icon.btn-success {
    background: #4caf50;
    color: white;
}

.btn-icon.btn-danger {
    background: #f44336;
    color: white;
}

/* الترقيم */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e9ecef;
}

.page-info {
    color: #666;
    font-weight: 500;
}

.pagination-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.8rem 1.2rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.pagination-btn:hover:not(.disabled) {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 87, 34, 0.3);
}

.pagination-btn.disabled {
    background: #ccc;
    cursor: not-allowed;
    opacity: 0.6;
}

.pagination-btn.disabled:hover {
    transform: none;
    box-shadow: none;
}

/* كروت طلبات الدورات */
.course-request-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
}

.course-request-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 30px rgba(255, 87, 34, 0.15);
    border-color: var(--primary-color);
}

.course-request-card .card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.course-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.course-request-card .course-info h3 {
    margin: 0 0 0.3rem 0;
    color: var(--secondary-color);
    font-size: 1.2rem;
    font-weight: 600;
}

.btn-warning {
    background: #ff9800;
    color: white;
}

.btn-warning:hover {
    background: #f57c00;
}

/* شارات التصنيفات */
.category-badge {
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
}

.category-badge.tech {
    background: rgba(33, 150, 243, 0.1);
    color: #2196f3;
}

.category-badge.marketing {
    background: rgba(156, 39, 176, 0.1);
    color: #9c27b0;
}

.category-badge.design {
    background: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.category-badge.business {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

/* تحسينات إضافية للفلاتر */
.filter-group input[type="date"] {
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-group input[type="date"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
}

/* عداد الطلاب */
.students-count {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(139, 195, 74, 0.1));
    border-radius: 20px;
    color: #4caf50;
    font-weight: 600;
    font-size: 0.9rem;
}

.students-count.no-students {
    background: linear-gradient(135deg, rgba(158, 158, 158, 0.1), rgba(189, 189, 189, 0.1));
    color: #9e9e9e;
}

.students-count i {
    font-size: 1rem;
}

/* بانر معلومات الدورة */
.course-info-banner {
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(255, 87, 34, 0.2);
}

.course-info-banner h2 {
    margin: 0 0 1rem 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.course-meta {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.course-meta span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
}

.course-meta i {
    font-size: 1.1rem;
}

/* شبكة كروت الطلاب */
.students-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.student-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
}

.student-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 30px rgba(255, 87, 34, 0.15);
    border-color: var(--primary-color);
}

.student-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.student-avatar img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid var(--primary-color);
}

.student-info h3 {
    margin: 0 0 0.3rem 0;
    color: var(--secondary-color);
    font-size: 1.1rem;
    font-weight: 600;
}

.student-id {
    font-size: 0.85rem;
    color: #666;
    font-weight: 500;
}

.student-status {
    margin-right: auto;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.student-content {
    padding: 1.5rem;
}

.student-actions {
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    display: flex;
    gap: 0.8rem;
    flex-wrap: wrap;
}

.student-actions .btn {
    flex: 1;
    min-width: 100px;
    justify-content: center;
}

/* صفحة إجابات الطالب */
.student-profile-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.student-profile-header {
    display: flex;
    gap: 2rem;
    padding: 3rem;
    background: linear-gradient(135deg, rgba(255, 87, 34, 0.05), rgba(255, 107, 53, 0.05));
    border-bottom: 1px solid #e9ecef;
}

.student-profile-card .profile-image {
    position: relative;
    text-align: center;
}

.student-profile-card .profile-image img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.student-profile-card .profile-info h2 {
    margin: 0 0 0.5rem 0;
    color: var(--secondary-color);
    font-size: 1.8rem;
    font-weight: 700;
}

.student-id {
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 2rem;
}

/* قسم الإجابات */
.answers-section {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.submission-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-weight: 500;
}

.answers-container {
    margin: 2rem 0;
}

.answer-item {
    margin-bottom: 2.5rem;
    padding: 2rem;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.answer-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 5px 20px rgba(255, 87, 34, 0.1);
}

.answer-item .question h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(255, 87, 34, 0.1);
}

.answer-item .question i {
    font-size: 1.3rem;
}

.answer-item .answer {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border-right: 4px solid var(--primary-color);
}

.answer-item .answer p {
    margin: 0;
    line-height: 1.8;
    color: var(--secondary-color);
    font-size: 1.05rem;
}

/* أزرار الإجراءات النهائية */
.final-actions {
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 15px;
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.final-actions .btn-large {
    min-width: 200px;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-content {
        padding: 1rem;
    }

    .requests-grid {
        grid-template-columns: 1fr;
    }

    .quick-stats {
        justify-content: center;
    }

    .card-actions {
        flex-direction: column;
    }

    .btn {
        justify-content: center;
    }

    /* تحسينات responsive للصفحات الجديدة */
    .course-info-banner {
        padding: 1.5rem;
    }

    .course-meta {
        flex-direction: column;
        gap: 1rem;
    }

    .students-grid {
        grid-template-columns: 1fr;
    }

    .student-profile-header {
        flex-direction: column;
        text-align: center;
        padding: 2rem;
    }

    .student-profile-card .profile-image img {
        width: 100px;
        height: 100px;
    }

    .answer-item {
        padding: 1.5rem;
    }

    .final-actions {
        flex-direction: column;
    }

    .final-actions .btn-large {
        min-width: auto;
    }

    .course-request-card .card-actions {
        flex-direction: column;
    }

    .course-request-card .card-actions .btn {
        justify-content: center;
    }
}

/* تحسينات لصفحات سجل التعديلات */
.trainer-info, .course-info {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.trainer-thumb {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.trainer-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.trainer-details {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
}

.trainer-details strong, .course-info strong {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.9rem;
}

.trainer-details small, .course-info small {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.course-info {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
}

/* فلاتر البحث المحسنة */
.filters-section {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.filters-row {
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.filter-group label {
    font-weight: 500;
    color: var(--text-dark);
    font-size: 0.9rem;
}

.filter-input, .filter-select {
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-family: 'Tajawal', sans-serif;
    transition: border-color 0.3s ease;
}

.filter-input:focus, .filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
}

.filter-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Tajawal', sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    height: fit-content;
}

.filter-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 87, 34, 0.3);
}

/* حالات ملونة لسجل التعديلات */
.status-badge {
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    display: inline-block;
    min-width: 80px;
    transition: all 0.3s ease;
}

.status-badge.approved {
    background: rgba(76, 175, 80, 0.1) !important;
    color: #4caf50 !important;
}

.status-badge.pending {
    background: rgba(255, 193, 7, 0.1) !important;
    color: #ff8f00 !important;
}

.status-badge.rejected {
    background: rgba(244, 67, 54, 0.1) !important;
    color: #f44336 !important;
}

.status-badge.suspended {
    background: rgba(156, 39, 176, 0.1) !important;
    color: #9c27b0 !important;
}

/* تأثيرات hover للحالات */
.status-badge:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* ضمان تطبيق الألوان في جميع الحالات */
table .status-badge.approved,
.data-table .status-badge.approved {
    background: rgba(76, 175, 80, 0.1) !important;
    color: #4caf50 !important;
}

table .status-badge.pending,
.data-table .status-badge.pending {
    background: rgba(255, 193, 7, 0.1) !important;
    color: #ff8f00 !important;
}

table .status-badge.rejected,
.data-table .status-badge.rejected {
    background: rgba(244, 67, 54, 0.1) !important;
    color: #f44336 !important;
}

table .status-badge.suspended,
.data-table .status-badge.suspended {
    background: rgba(156, 39, 176, 0.1) !important;
    color: #9c27b0 !important;
}

/* تحسينات إضافية للجدول */
.data-table td {
    vertical-align: middle;
    padding: 1rem 0.8rem;
}

.data-table tr:hover {
    background: rgba(255, 87, 34, 0.05);
}



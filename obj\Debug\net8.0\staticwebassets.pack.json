{"Files": [{"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Roya.bundle.scp.css", "PackagePath": "staticwebassets\\Roya.3md91uct9v.bundle.scp.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\Pictures\\7215c746-97f0-468e-b9b7-cb5808f8357b.png", "PackagePath": "staticwebassets\\Pictures\\7215c746-97f0-468e-b9b7-cb5808f8357b.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\Pictures\\Roya 2030 Logo.png", "PackagePath": "staticwebassets\\Pictures\\Roya 2030 Logo.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\Pictures\\b87e8a36-4309-453c-95ef-12ce156b2d7c.png", "PackagePath": "staticwebassets\\Pictures\\b87e8a36-4309-453c-95ef-12ce156b2d7c.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\Pictures\\cd836df4-da3e-4097-8e89-3c45187c2788.png", "PackagePath": "staticwebassets\\Pictures\\cd836df4-da3e-4097-8e89-3c45187c2788.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\Pictures\\ea0ac251-1de0-46dc-aead-17a45e7e4cbf.png", "PackagePath": "staticwebassets\\Pictures\\ea0ac251-1de0-46dc-aead-17a45e7e4cbf.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\Pictures\\unnamed-removebg-preview.png", "PackagePath": "staticwebassets\\Pictures\\unnamed-removebg-preview.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\assets\\demo\\chart-area-demo.js", "PackagePath": "staticwebassets\\assets\\demo\\chart-area-demo.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\assets\\demo\\chart-bar-demo.js", "PackagePath": "staticwebassets\\assets\\demo\\chart-bar-demo.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\assets\\demo\\chart-pie-demo.js", "PackagePath": "staticwebassets\\assets\\demo\\chart-pie-demo.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\assets\\demo\\datatables-demo.js", "PackagePath": "staticwebassets\\assets\\demo\\datatables-demo.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\assets\\img\\error-404-monochrome.svg", "PackagePath": "staticwebassets\\assets\\img\\error-404-monochrome.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\css\\styles.css", "PackagePath": "staticwebassets\\css\\styles.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\cssvist\\animate.css", "PackagePath": "staticwebassets\\cssvist\\animate.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\cssvist\\aos.css", "PackagePath": "staticwebassets\\cssvist\\aos.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\cssvist\\bootstrap.min.css", "PackagePath": "staticwebassets\\cssvist\\bootstrap.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\cssvist\\flaticon.css", "PackagePath": "staticwebassets\\cssvist\\flaticon.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\cssvist\\font-awesome.min.css", "PackagePath": "staticwebassets\\cssvist\\font-awesome.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\cssvist\\magnific-popup.css", "PackagePath": "staticwebassets\\cssvist\\magnific-popup.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\cssvist\\nice-select.css", "PackagePath": "staticwebassets\\cssvist\\nice-select.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\cssvist\\owl.carousel.min.css", "PackagePath": "staticwebassets\\cssvist\\owl.carousel.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\cssvist\\slick.css", "PackagePath": "staticwebassets\\cssvist\\slick.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\cssvist\\style.css", "PackagePath": "staticwebassets\\cssvist\\style.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\cssvist\\style.map", "PackagePath": "staticwebassets\\cssvist\\style.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\cssvist\\swiper.min.css", "PackagePath": "staticwebassets\\cssvist\\swiper.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\cssvist\\themify-icons.css", "PackagePath": "staticwebassets\\cssvist\\themify-icons.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\favicon.ico", "PackagePath": "staticwebassets\\favicon.ico"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\fonts\\Flaticon.eot", "PackagePath": "staticwebassets\\fonts\\Flaticon.eot"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\fonts\\Flaticon.ttf", "PackagePath": "staticwebassets\\fonts\\Flaticon.ttf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\fonts\\Flaticon.woff", "PackagePath": "staticwebassets\\fonts\\Flaticon.woff"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\fonts\\Flaticon.woff2", "PackagePath": "staticwebassets\\fonts\\Flaticon.woff2"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\fonts\\FontAwesome.otf", "PackagePath": "staticwebassets\\fonts\\FontAwesome.otf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\fonts\\fontawesome-webfont.eot", "PackagePath": "staticwebassets\\fonts\\fontawesome-webfont.eot"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\fonts\\fontawesome-webfont.svg", "PackagePath": "staticwebassets\\fonts\\fontawesome-webfont.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\fonts\\fontawesome-webfont.ttf", "PackagePath": "staticwebassets\\fonts\\fontawesome-webfont.ttf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\fonts\\fontawesome-webfont.woff", "PackagePath": "staticwebassets\\fonts\\fontawesome-webfont.woff"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\fonts\\fontawesome-webfont.woff2", "PackagePath": "staticwebassets\\fonts\\fontawesome-webfont.woff2"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\fonts\\themify.eot", "PackagePath": "staticwebassets\\fonts\\themify.eot"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\fonts\\themify.svg", "PackagePath": "staticwebassets\\fonts\\themify.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\fonts\\themify.ttf", "PackagePath": "staticwebassets\\fonts\\themify.ttf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\fonts\\themify.woff", "PackagePath": "staticwebassets\\fonts\\themify.woff"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\about_overlay.png", "PackagePath": "staticwebassets\\img\\about_overlay.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\advance_feature_bg.png", "PackagePath": "staticwebassets\\img\\advance_feature_bg.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\advance_feature_img.png", "PackagePath": "staticwebassets\\img\\advance_feature_img.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\animate_icon\\icon_1.png", "PackagePath": "staticwebassets\\img\\animate_icon\\icon_1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\animate_icon\\icon_2.png", "PackagePath": "staticwebassets\\img\\animate_icon\\icon_2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\animate_icon\\icon_3.png", "PackagePath": "staticwebassets\\img\\animate_icon\\icon_3.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\animate_icon\\icon_4.png", "PackagePath": "staticwebassets\\img\\animate_icon\\icon_4.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\animate_icon\\icon_5.png", "PackagePath": "staticwebassets\\img\\animate_icon\\icon_5.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\animate_icon\\icon_7.png", "PackagePath": "staticwebassets\\img\\animate_icon\\icon_7.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\animate_icon\\icon_8.png", "PackagePath": "staticwebassets\\img\\animate_icon\\icon_8.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\animate_icon\\icon_9.png", "PackagePath": "staticwebassets\\img\\animate_icon\\icon_9.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\author\\author_1.png", "PackagePath": "staticwebassets\\img\\author\\author_1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\author\\author_2.png", "PackagePath": "staticwebassets\\img\\author\\author_2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\author\\author_3.png", "PackagePath": "staticwebassets\\img\\author\\author_3.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\banner_bg.png", "PackagePath": "staticwebassets\\img\\banner_bg.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\banner_img.png", "PackagePath": "staticwebassets\\img\\banner_img.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\blog\\author.png", "PackagePath": "staticwebassets\\img\\blog\\author.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\blog\\blog_1.png", "PackagePath": "staticwebassets\\img\\blog\\blog_1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\blog\\blog_2.png", "PackagePath": "staticwebassets\\img\\blog\\blog_2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\blog\\blog_3.png", "PackagePath": "staticwebassets\\img\\blog\\blog_3.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\blog\\blog_4.png", "PackagePath": "staticwebassets\\img\\blog\\blog_4.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\blog\\learn_about_bg.png", "PackagePath": "staticwebassets\\img\\blog\\learn_about_bg.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\blog\\single_blog_1.png", "PackagePath": "staticwebassets\\img\\blog\\single_blog_1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\blog\\single_blog_2.png", "PackagePath": "staticwebassets\\img\\blog\\single_blog_2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\blog\\single_blog_3.png", "PackagePath": "staticwebassets\\img\\blog\\single_blog_3.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\blog\\single_blog_4.png", "PackagePath": "staticwebassets\\img\\blog\\single_blog_4.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\blog\\single_blog_5.png", "PackagePath": "staticwebassets\\img\\blog\\single_blog_5.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\blog\\slide_thumb_1.png", "PackagePath": "staticwebassets\\img\\blog\\slide_thumb_1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\breadcrumb.png", "PackagePath": "staticwebassets\\img\\breadcrumb.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\client\\client_1.png", "PackagePath": "staticwebassets\\img\\client\\client_1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\client\\client_2.png", "PackagePath": "staticwebassets\\img\\client\\client_2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\comment\\comment_1.png", "PackagePath": "staticwebassets\\img\\comment\\comment_1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\comment\\comment_2.png", "PackagePath": "staticwebassets\\img\\comment\\comment_2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\comment\\comment_3.png", "PackagePath": "staticwebassets\\img\\comment\\comment_3.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\cource\\cource_1.png", "PackagePath": "staticwebassets\\img\\cource\\cource_1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\cource\\cource_2.png", "PackagePath": "staticwebassets\\img\\cource\\cource_2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\cource\\cource_3.png", "PackagePath": "staticwebassets\\img\\cource\\cource_3.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\a.jpg", "PackagePath": "staticwebassets\\img\\elements\\a.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\a2.jpg", "PackagePath": "staticwebassets\\img\\elements\\a2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\d.jpg", "PackagePath": "staticwebassets\\img\\elements\\d.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\disabled-check.png", "PackagePath": "staticwebassets\\img\\elements\\disabled-check.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\disabled-radio.png", "PackagePath": "staticwebassets\\img\\elements\\disabled-radio.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\f1.jpg", "PackagePath": "staticwebassets\\img\\elements\\f1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\f2.jpg", "PackagePath": "staticwebassets\\img\\elements\\f2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\f3.jpg", "PackagePath": "staticwebassets\\img\\elements\\f3.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\f4.jpg", "PackagePath": "staticwebassets\\img\\elements\\f4.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\f5.jpg", "PackagePath": "staticwebassets\\img\\elements\\f5.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\f6.jpg", "PackagePath": "staticwebassets\\img\\elements\\f6.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\f7.jpg", "PackagePath": "staticwebassets\\img\\elements\\f7.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\f8.jpg", "PackagePath": "staticwebassets\\img\\elements\\f8.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\g1.jpg", "PackagePath": "staticwebassets\\img\\elements\\g1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\g2.jpg", "PackagePath": "staticwebassets\\img\\elements\\g2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\g3.jpg", "PackagePath": "staticwebassets\\img\\elements\\g3.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\g4.jpg", "PackagePath": "staticwebassets\\img\\elements\\g4.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\g5.jpg", "PackagePath": "staticwebassets\\img\\elements\\g5.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\g6.jpg", "PackagePath": "staticwebassets\\img\\elements\\g6.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\g7.jpg", "PackagePath": "staticwebassets\\img\\elements\\g7.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\g8.jpg", "PackagePath": "staticwebassets\\img\\elements\\g8.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\primary-check.png", "PackagePath": "staticwebassets\\img\\elements\\primary-check.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\primary-radio.png", "PackagePath": "staticwebassets\\img\\elements\\primary-radio.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\success-check.png", "PackagePath": "staticwebassets\\img\\elements\\success-check.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\elements\\success-radio.png", "PackagePath": "staticwebassets\\img\\elements\\success-radio.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\favicon.png", "PackagePath": "staticwebassets\\img\\favicon.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\gallery\\gallery_item.png", "PackagePath": "staticwebassets\\img\\gallery\\gallery_item.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\gallery\\gallery_item_1.png", "PackagePath": "staticwebassets\\img\\gallery\\gallery_item_1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\gallery\\gallery_item_2.png", "PackagePath": "staticwebassets\\img\\gallery\\gallery_item_2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\gallery\\gallery_item_3.png", "PackagePath": "staticwebassets\\img\\gallery\\gallery_item_3.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\gallery\\gallery_item_4.png", "PackagePath": "staticwebassets\\img\\gallery\\gallery_item_4.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\gallery\\gallery_item_5.png", "PackagePath": "staticwebassets\\img\\gallery\\gallery_item_5.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\gallery\\gallery_item_6.png", "PackagePath": "staticwebassets\\img\\gallery\\gallery_item_6.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\gallery\\gallery_item_7.png", "PackagePath": "staticwebassets\\img\\gallery\\gallery_item_7.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\gallery\\gallery_item_71.png", "PackagePath": "staticwebassets\\img\\gallery\\gallery_item_71.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\gallery\\gallery_item_8.png", "PackagePath": "staticwebassets\\img\\gallery\\gallery_item_8.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\gallery\\service_bg_2.png", "PackagePath": "staticwebassets\\img\\gallery\\service_bg_2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\icon\\color_star.svg", "PackagePath": "staticwebassets\\img\\icon\\color_star.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\icon\\left.svg", "PackagePath": "staticwebassets\\img\\icon\\left.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\icon\\play.svg", "PackagePath": "staticwebassets\\img\\icon\\play.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\icon\\quate.svg", "PackagePath": "staticwebassets\\img\\icon\\quate.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\icon\\right.svg", "PackagePath": "staticwebassets\\img\\icon\\right.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\icon\\star.svg", "PackagePath": "staticwebassets\\img\\icon\\star.svg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\insta\\instagram_1.png", "PackagePath": "staticwebassets\\img\\insta\\instagram_1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\insta\\instagram_2.png", "PackagePath": "staticwebassets\\img\\insta\\instagram_2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\insta\\instagram_3.png", "PackagePath": "staticwebassets\\img\\insta\\instagram_3.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\insta\\instagram_4.png", "PackagePath": "staticwebassets\\img\\insta\\instagram_4.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\insta\\instagram_5.png", "PackagePath": "staticwebassets\\img\\insta\\instagram_5.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\insta\\instagram_6.png", "PackagePath": "staticwebassets\\img\\insta\\instagram_6.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\learning_img.png", "PackagePath": "staticwebassets\\img\\learning_img.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\learning_img_bg.png", "PackagePath": "staticwebassets\\img\\learning_img_bg.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\logo.png", "PackagePath": "staticwebassets\\img\\logo.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\post\\next.png", "PackagePath": "staticwebassets\\img\\post\\next.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\post\\post_1.png", "PackagePath": "staticwebassets\\img\\post\\post_1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\post\\post_10.png", "PackagePath": "staticwebassets\\img\\post\\post_10.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\post\\post_2.png", "PackagePath": "staticwebassets\\img\\post\\post_2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\post\\post_3.png", "PackagePath": "staticwebassets\\img\\post\\post_3.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\post\\post_4.png", "PackagePath": "staticwebassets\\img\\post\\post_4.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\post\\post_5.png", "PackagePath": "staticwebassets\\img\\post\\post_5.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\post\\post_6.png", "PackagePath": "staticwebassets\\img\\post\\post_6.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\post\\post_7.png", "PackagePath": "staticwebassets\\img\\post\\post_7.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\post\\post_8.png", "PackagePath": "staticwebassets\\img\\post\\post_8.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\post\\post_9.png", "PackagePath": "staticwebassets\\img\\post\\post_9.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\post\\preview.png", "PackagePath": "staticwebassets\\img\\post\\preview.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\quote.png", "PackagePath": "staticwebassets\\img\\quote.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\service_bg_2.png", "PackagePath": "staticwebassets\\img\\service_bg_2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\single_cource.png", "PackagePath": "staticwebassets\\img\\single_cource.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\single_page_logo.png", "PackagePath": "staticwebassets\\img\\single_page_logo.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\single_project.png", "PackagePath": "staticwebassets\\img\\single_project.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\special_cource_1.png", "PackagePath": "staticwebassets\\img\\special_cource_1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\special_cource_2.png", "PackagePath": "staticwebassets\\img\\special_cource_2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\special_cource_3.png", "PackagePath": "staticwebassets\\img\\special_cource_3.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\team\\team_1.png", "PackagePath": "staticwebassets\\img\\team\\team_1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\team\\team_2.png", "PackagePath": "staticwebassets\\img\\team\\team_2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\team\\team_3.png", "PackagePath": "staticwebassets\\img\\team\\team_3.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\team\\team_4.png", "PackagePath": "staticwebassets\\img\\team\\team_4.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\testimonial_img_1.png", "PackagePath": "staticwebassets\\img\\testimonial_img_1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\testimonial_img_2.png", "PackagePath": "staticwebassets\\img\\testimonial_img_2.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\img\\testimonial_img_3.png", "PackagePath": "staticwebassets\\img\\testimonial_img_3.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\js\\datatables-simple-demo.js", "PackagePath": "staticwebassets\\js\\datatables-simple-demo.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\js\\scripts.js", "PackagePath": "staticwebassets\\js\\scripts.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\aos.js", "PackagePath": "staticwebassets\\jsvist\\aos.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\bootstrap.min.js", "PackagePath": "staticwebassets\\jsvist\\bootstrap.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\contact.js", "PackagePath": "staticwebassets\\jsvist\\contact.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\custom.js", "PackagePath": "staticwebassets\\jsvist\\custom.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\gmaps.min.js", "PackagePath": "staticwebassets\\jsvist\\gmaps.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\jquery-1.12.1.min.js", "PackagePath": "staticwebassets\\jsvist\\jquery-1.12.1.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\jquery-3.3.1.slim.min.js", "PackagePath": "staticwebassets\\jsvist\\jquery-3.3.1.slim.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\jquery.ajaxchimp.min.js", "PackagePath": "staticwebassets\\jsvist\\jquery.ajaxchimp.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\jquery.counterup.min.js", "PackagePath": "staticwebassets\\jsvist\\jquery.counterup.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\jquery.easing.min.js", "PackagePath": "staticwebassets\\jsvist\\jquery.easing.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\jquery.form.js", "PackagePath": "staticwebassets\\jsvist\\jquery.form.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\jquery.magnific-popup.js", "PackagePath": "staticwebassets\\jsvist\\jquery.magnific-popup.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\jquery.nice-select.min.js", "PackagePath": "staticwebassets\\jsvist\\jquery.nice-select.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\jquery.validate.min.js", "PackagePath": "staticwebassets\\jsvist\\jquery.validate.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\mail-script.js", "PackagePath": "staticwebassets\\jsvist\\mail-script.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\masonry.pkgd.js", "PackagePath": "staticwebassets\\jsvist\\masonry.pkgd.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\masonry.pkgd.min.js", "PackagePath": "staticwebassets\\jsvist\\masonry.pkgd.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\owl.carousel.min.js", "PackagePath": "staticwebassets\\jsvist\\owl.carousel.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\particles.min.js", "PackagePath": "staticwebassets\\jsvist\\particles.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\popper.min.js", "PackagePath": "staticwebassets\\jsvist\\popper.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\slick.min.js", "PackagePath": "staticwebassets\\jsvist\\slick.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\swiper.min.js", "PackagePath": "staticwebassets\\jsvist\\swiper.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\swiper_custom.js", "PackagePath": "staticwebassets\\jsvist\\swiper_custom.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\jsvist\\waypoints.min.js", "PackagePath": "staticwebassets\\jsvist\\waypoints.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\LICENSE", "PackagePath": "staticwebassets\\lib\\bootstrap"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "PackagePath": "staticwebassets\\lib\\jquery-validation\\LICENSE.md"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\jquery\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery\\LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\jquery\\dist\\jquery.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.js"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.map"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\CVs\\1d50fa0d-232a-4253-9001-30f9b3ad2164.pdf", "PackagePath": "staticwebassets\\uploads\\CVs\\1d50fa0d-232a-4253-9001-30f9b3ad2164.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\CVs\\1fd0d641-d673-4d92-b407-c8596c3b83b4.mp4", "PackagePath": "staticwebassets\\uploads\\CVs\\1fd0d641-d673-4d92-b407-c8596c3b83b4.mp4"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\CVs\\42840a77-53f8-4fc7-87a2-2f773e44cd1c.pdf", "PackagePath": "staticwebassets\\uploads\\CVs\\42840a77-53f8-4fc7-87a2-2f773e44cd1c.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\CVs\\4ac41654-a3c8-4167-a2aa-dcf2d11ede42.pdf", "PackagePath": "staticwebassets\\uploads\\CVs\\4ac41654-a3c8-4167-a2aa-dcf2d11ede42.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\CVs\\5e1b589e-12a4-4f58-b123-d61c7dc65273.pdf", "PackagePath": "staticwebassets\\uploads\\CVs\\5e1b589e-12a4-4f58-b123-d61c7dc65273.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\CVs\\70868615-c704-4416-8571-ff43e8d6cb6c.pdf", "PackagePath": "staticwebassets\\uploads\\CVs\\70868615-c704-4416-8571-ff43e8d6cb6c.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\CVs\\8f117439-7861-4346-ada6-3f1a3b9ed8df.pdf", "PackagePath": "staticwebassets\\uploads\\CVs\\8f117439-7861-4346-ada6-3f1a3b9ed8df.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\CVs\\b0c30c3c-958d-45d2-a84b-974c20d96f8b.pdf", "PackagePath": "staticwebassets\\uploads\\CVs\\b0c30c3c-958d-45d2-a84b-974c20d96f8b.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\CourseImages\\d0d66a84-2c43-40f0-80e5-57ba244db2f5.png", "PackagePath": "staticwebassets\\uploads\\CourseImages\\d0d66a84-2c43-40f0-80e5-57ba244db2f5.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\CourseImeges\\19a018e3-6c14-4cd9-9ccc-e71cebd66bd0.png", "PackagePath": "staticwebassets\\uploads\\CourseImeges\\19a018e3-6c14-4cd9-9ccc-e71cebd66bd0.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\CourseImeges\\45f13724-cd75-46d5-b0d8-20e8e242517d.png", "PackagePath": "staticwebassets\\uploads\\CourseImeges\\45f13724-cd75-46d5-b0d8-20e8e242517d.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\CourseImeges\\8a82cba0-f3c9-4f13-b835-189a9ea21bb1.png", "PackagePath": "staticwebassets\\uploads\\CourseImeges\\8a82cba0-f3c9-4f13-b835-189a9ea21bb1.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\CourseImeges\\9e34a172-5dba-46be-9730-45f121795c18.png", "PackagePath": "staticwebassets\\uploads\\CourseImeges\\9e34a172-5dba-46be-9730-45f121795c18.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\CourseImeges\\c98b356d-ed69-4fd2-926f-a29bdee13b56.png", "PackagePath": "staticwebassets\\uploads\\CourseImeges\\c98b356d-ed69-4fd2-926f-a29bdee13b56.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\CourseImeges\\d3f95191-29f9-486e-9c4d-9553d0d06de8.png", "PackagePath": "staticwebassets\\uploads\\CourseImeges\\d3f95191-29f9-486e-9c4d-9553d0d06de8.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\TrainerImages\\03e309d4-a1cd-46bf-9372-bd3494d5d996.png", "PackagePath": "staticwebassets\\uploads\\TrainerImages\\03e309d4-a1cd-46bf-9372-bd3494d5d996.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\TrainerImages\\7ab819ae-ff6c-462d-bda1-e18dde9ca4aa.jpg", "PackagePath": "staticwebassets\\uploads\\TrainerImages\\7ab819ae-ff6c-462d-bda1-e18dde9ca4aa.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\TrainerImages\\a65f1f6f-ed92-4c09-83d5-92fe282a69b5.png", "PackagePath": "staticwebassets\\uploads\\TrainerImages\\a65f1f6f-ed92-4c09-83d5-92fe282a69b5.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\TrainerImages\\b36e0ff0-5ecc-426e-80ee-3387fa3dfb11.png", "PackagePath": "staticwebassets\\uploads\\TrainerImages\\b36e0ff0-5ecc-426e-80ee-3387fa3dfb11.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\TrainerImages\\dc554658-e089-4221-8ee2-fb1d5323ab1d.jpg", "PackagePath": "staticwebassets\\uploads\\TrainerImages\\dc554658-e089-4221-8ee2-fb1d5323ab1d.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\TrainerImages\\e77db8e9-8dc5-4ac2-a9cf-f75c40647058.png", "PackagePath": "staticwebassets\\uploads\\TrainerImages\\e77db8e9-8dc5-4ac2-a9cf-f75c40647058.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\Roya\\Roya\\wwwroot\\uploads\\TrainerImages\\f8a6a4f4-5207-4351-b574-616b17e00f42.png", "PackagePath": "staticwebassets\\uploads\\TrainerImages\\f8a6a4f4-5207-4351-b574-616b17e00f42.png"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.Roya.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.Roya.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.build.Roya.props", "PackagePath": "build\\Roya.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildMultiTargeting.Roya.props", "PackagePath": "buildMultiTargeting\\Roya.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildTransitive.Roya.props", "PackagePath": "buildTransitive\\Roya.props"}], "ElementsToRemove": []}
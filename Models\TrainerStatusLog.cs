﻿using System.ComponentModel.DataAnnotations;

namespace Roya.Models
{
    public class TrainerStatusLog
    {
        public int Id { get; set; }

        [Required]
        public string TrainerId { get; set; } 
       

        public Trainer Trainer { get; set; }


        [Required]
        public TrainerStatus OldStatus { get; set; }

        [Required]
        public TrainerStatus NewStatus { get; set; }

        public DateTime ChangeTime { get; set; } = DateTime.Now;

        public string ChangedBy { get; set; } = "Admin"; //  أدمن واحد
    }



}

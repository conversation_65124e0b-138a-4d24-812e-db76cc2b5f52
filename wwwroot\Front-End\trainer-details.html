<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل المدرب - رؤية 2030</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="admin-dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- الشريط الجانبي -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="logo.png" alt="رؤية 2030">
                    <h3>لوحة التحكم</h3>
                </div>
                <button class="sidebar-toggle-btn" id="sidebarToggle" title="تصغير/توسيع القائمة">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item">
                        <a href="admin-dashboard.html" class="nav-link">
                            <i class="fas fa-home"></i>
                            <span>الصفحة الرئيسية</span>
                        </a>
                    </li>
                    
                    <li class="nav-item has-submenu open">
                        <a href="#" class="nav-link">
                            <i class="fas fa-chalkboard-teacher"></i>
                            <span>إدارة المدربين</span>
                            <i class="fas fa-chevron-down submenu-arrow"></i>
                        </a>
                        <ul class="submenu">
                            <li><a href="trainer-requests.html">طلبات المدربين</a></li>
                            <li><a href="all-trainers.html">جميع المدربين</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item has-submenu">
                        <a href="#" class="nav-link">
                            <i class="fas fa-book"></i>
                            <span>إدارة الدورات</span>
                            <i class="fas fa-chevron-down submenu-arrow"></i>
                        </a>
                        <ul class="submenu">
                            <li><a href="course-requests.html">طلبات الدورات</a></li>
                            <li><a href="all-courses.html">جميع الدورات</a></li>
                            <li><a href="approved-courses.html">الدورات المقبولة</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item">
                        <a href="settings.html" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>تسجيل الخروج</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- الهيدر العلوي -->
            <header class="main-header">
                <div class="header-content">
                    <div class="header-left">
                        <button class="btn btn-secondary" onclick="goBack()">
                            <i class="fas fa-arrow-right"></i>
                            رجوع
                        </button>
                        <h1>تفاصيل المدرب</h1>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="المدير">
                        </div>
                        <div class="user-details">
                            <span class="user-name">أحمد محمد</span>
                            <span class="user-role">مدير النظام</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- المحتوى -->
            <div class="dashboard-content">
                <!-- كارد تفاصيل المدرب -->
                <div class="trainer-details-card">
                    <div class="trainer-profile">
                        <div class="profile-image">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face" alt="أحمد علي محمد">
                            <div class="status-badge pending">
                                <i class="fas fa-clock"></i>
                                قيد المراجعة
                            </div>
                        </div>
                        
                        <div class="profile-info">
                            <h2>أحمد علي محمد</h2>
                            <p class="profile-title">مطور ويب متخصص</p>
                            
                            <div class="contact-info">
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <span><EMAIL></span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <span>+218 91 234 5678</span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>بنغازي، ليبيا</span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-birthday-cake"></i>
                                    <span>32 سنة</span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-briefcase"></i>
                                    <span>5 سنوات خبرة</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="profile-details">
                        <div class="details-section">
                            <h3>
                                <i class="fas fa-user"></i>
                                السيرة الذاتية
                            </h3>
                            <div class="bio-content">
                                <p>مطور ويب محترف مع خبرة واسعة في تطوير التطبيقات والمواقع الإلكترونية باستخدام أحدث التقنيات. حاصل على شهادة البكالوريوس في علوم الحاسوب من جامعة بنغازي.</p>
                                
                                <h4>المهارات التقنية:</h4>
                                <ul class="skills-list">
                                    <li>HTML5 & CSS3</li>
                                    <li>JavaScript (ES6+)</li>
                                    <li>React.js & Vue.js</li>
                                    <li>Node.js & Express</li>
                                    <li>MongoDB & MySQL</li>
                                    <li>Git & GitHub</li>
                                </ul>
                                
                                <h4>الخبرة العملية:</h4>
                                <div class="experience-item">
                                    <strong>مطور ويب أول - شركة التقنيات المتقدمة (2020-2024)</strong>
                                    <p>تطوير وصيانة مواقع الويب والتطبيقات للعملاء، قيادة فريق من 3 مطورين.</p>
                                </div>
                                
                                <div class="experience-item">
                                    <strong>مطور ويب - شركة الحلول الرقمية (2018-2020)</strong>
                                    <p>تطوير واجهات المستخدم وتحسين أداء المواقع الإلكترونية.</p>
                                </div>
                                
                                <h4>الشهادات:</h4>
                                <ul class="certificates-list">
                                    <li>شهادة AWS Certified Developer</li>
                                    <li>شهادة Google Analytics</li>
                                    <li>شهادة React Developer من Meta</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <button class="btn btn-success btn-large" onclick="approveTrainer()">
                            <i class="fas fa-check"></i>
                            قبول المدرب
                        </button>
                        <button class="btn btn-danger btn-large" onclick="rejectTrainer()">
                            <i class="fas fa-times"></i>
                            رفض المدرب
                        </button>
                        <button class="btn btn-info btn-large" onclick="requestMoreInfo()">
                            <i class="fas fa-question-circle"></i>
                            طلب معلومات إضافية
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="admin-dashboard.js"></script>
    <script>
        function goBack() {
            window.history.back();
        }
        
        function approveTrainer() {
            if(confirm('هل أنت متأكد من قبول هذا المدرب؟')) {
                alert('تم قبول المدرب بنجاح!');
                // هنا يمكن إضافة كود لإرسال الطلب للخادم
            }
        }
        
        function rejectTrainer() {
            if(confirm('هل أنت متأكد من رفض هذا المدرب؟')) {
                alert('تم رفض المدرب!');
                // هنا يمكن إضافة كود لإرسال الطلب للخادم
            }
        }
        
        function requestMoreInfo() {
            alert('تم إرسال طلب للحصول على معلومات إضافية');
            // هنا يمكن إضافة كود لإرسال الطلب للخادم
        }
    </script>
</body>
</html>

﻿@model IEnumerable<Category>
@{
    ViewData["Title"] = "📂 التصنيفات";
}

<!-- ✅ ستايل الزر الرمادي المتدرج -->
<style>
    .btn-gradient-gray {
        background: linear-gradient(135deg, #adb5bd, #6c757d);
        color: white;
        transition: all 0.3s ease;
        border: none;
    }

        .btn-gradient-gray:hover {
            background: linear-gradient(135deg, #868e96, #495057);
            transform: scale(1.05);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            color: white;
        }
</style>

<div class="container mt-5" style="max-width: 800px;">
    <h2 class="text-center mb-4" style="color: #333; font-weight: bold;">
        📂 قائمة التصنيفات
    </h2>

    @if (TempData["Message"] != null)
    {
        <div class="alert alert-success text-center shadow-sm">@TempData["Message"]</div>
    }

    <!-- ✅ شريط البحث -->
    <form asp-action="Index" method="get"
          class="mb-4 d-flex justify-content-between align-items-center gap-2 flex-wrap shadow-sm p-3 rounded"
          style="background-color: #fdfdfd; border: 1px solid #eee;">
        <input type="text" name="searchString"
               class="form-control w-75"
               placeholder="🔍 ابحث باسم التصنيف"
               value="@Context.Request.Query["searchString"]" />

        <button type="submit" class="btn text-white fw-bold"
                style="background: linear-gradient(135deg, #ffa94d, #ff6b00); border: none;">
            🔍
        </button>

        <a asp-action="Index" class="btn btn-outline-secondary">🔄</a>
    </form>

    <!-- ✅ زر الإضافة -->
    <div class="text-end mb-3">
        <a asp-action="Create" class="btn btn-gradient-gray rounded-pill px-4 shadow-sm fw-bold">
            ➕
        </a>
    </div>

    @if (Model.Any())
    {
        <!-- ✅ جدول التصنيفات -->
        <div class="table-responsive shadow rounded">
            <table class="table table-bordered text-center align-middle mb-0"
                   style="background-color: #fff;">
                <thead style="background-color: #f8f9fa; color: #333;">
                    <tr style="font-weight: bold;">
                        <th>الاسم</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var category in Model)
                    {
                        <tr>
                            <td>@category.CategoryName</td>
                            <td class="d-flex justify-content-center gap-2 flex-wrap">
                                <a asp-action="Edit" asp-route-id="@category.Id"
                                   class="btn btn-outline-primary btn-sm rounded-pill shadow-sm px-3">
                                    ✏
                                </a>

                                <form asp-action="DeleteConfirmed" asp-route-id="@category.Id"
                                      method="post" class="d-inline delete-form">
                                    <button type="submit"
                                            class="btn btn-outline-danger btn-sm rounded-pill shadow-sm px-3"
                                            data-name="@category.CategoryName">
                                        🗑
                                    </button>
                                </form>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
    else
    {
        <div class="alert alert-warning text-center mt-3 shadow-sm">
            ⚠️ لا توجد نتائج مطابقة لبحثك.
        </div>
    }
</div>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const deleteForms = document.querySelectorAll(".delete-form");

        deleteForms.forEach(form => {
            form.addEventListener("submit", function (e) {
                e.preventDefault();

                const categoryName = form.querySelector("button").dataset.name;

                Swal.fire({
                    title: 'تأكيد الحذف',
                    text: `هل أنت متأكد من حذف التصنيف "${categoryName}"؟`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#dc3545',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'نعم، احذف',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        form.submit();
                    }
                });
            });
        });
    });
</script>

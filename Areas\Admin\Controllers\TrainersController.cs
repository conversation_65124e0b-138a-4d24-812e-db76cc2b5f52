﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Roya.Data;
using Roya.Models;

namespace Roya.Areas.Admin.Controllers
{  
        [Area("Admin")]
        [Authorize(Roles = "Admin")]
       
    public class TrainersController : Controller
    {
       
            private readonly AppDbContext _context;

            public TrainersController(AppDbContext context)
            {
                _context = context;
            }

        public async Task<IActionResult> Pending()
        {
            var pendingTrainers = await _context.Trainers
                .Where(t => t.Status == TrainerStatus.Pending)
                .ToListAsync();

            return View(pendingTrainers);
        }


        [HttpPost]
        public async Task<IActionResult> Approve(string id)
        {
            var trainer = await _context.Trainers.FindAsync(id);
            if (trainer == null)
                return NotFound();

            trainer.Status = TrainerStatus.Approved;
            _context.Update(trainer);
            await _context.SaveChangesAsync();

            //TempData["Message"] = "تمت الموافقة على المدرب.";

            return Redirect("https://wa.me/"+ trainer.PhoneNum+ "?text=تم%20قبولك%20يمكن%20الان%20تسجيل%20دخولك%20للمنصة");
        }

        [HttpPost]
        public async Task<IActionResult> Reject(string id)
        {
            var trainer = await _context.Trainers.FindAsync(id);
            if (trainer == null)
                return NotFound();

            trainer.Status = TrainerStatus.Rejected;
            _context.Update(trainer);
            await _context.SaveChangesAsync();

            TempData["Message"] = "تم رفض المدرب.";

            return RedirectToAction("AllTrainersWithStatus");
        }

        public async Task<IActionResult> AllTrainersWithStatus(string searchString, TrainerStatus? statusFilter)
        {
            var query = _context.Trainers.AsQueryable();

            if (!string.IsNullOrEmpty(searchString))
            {
                query = query.Where(t =>
                    t.FullName.Contains(searchString) ||
                    t.Email.Contains(searchString));
            }

            if (statusFilter.HasValue)
            {
                query = query.Where(t => t.Status == statusFilter.Value);
            }

            var filteredTrainers = await query.ToListAsync();
            return View(filteredTrainers);
        }


        public async Task<IActionResult> RejectedTrainers()
        {
            var rejected = await _context.Trainers
                .Where(t => t.Status == TrainerStatus.Rejected)
                .ToListAsync();

            return View(AllTrainersWithStatus);
        }


     
        [HttpPost]
        public async Task<IActionResult> UpdateStatus(string id, TrainerStatus newStatus)
        {
            var trainer = await _context.Trainers.FindAsync(id);
            if (trainer == null)
                return NotFound();

            var oldStatus = trainer.Status;
            if (oldStatus == newStatus)
            {
                TempData["Message"] = "الحالة لم تتغير.";
                return RedirectToAction("AllTrainersWithStatus");
            }

            // تغيير الحالة
            trainer.Status = newStatus;
            _context.Update(trainer);



            // إضافة سجل جديد في Log
            var log = new TrainerStatusLog
            {
                TrainerId = id,
                OldStatus = oldStatus,
                NewStatus = newStatus,
                ChangeTime = DateTime.Now,
                ChangedBy = "Admin" // لاحقًا ممكن تأخذه من User.Identity.Name لو عندك أكثر من مشرف
            };
            _context.TrainerStatusLogs.Add(log);

            await _context.SaveChangesAsync();

            //TempData["Message"] = "تم تحديث حالة المدرب.";
            string phone = trainer.PhoneNum; // تأكد أنه بدون "+" أو "0"
            string statusText = newStatus.ToString();
            string message;
            if (statusText == "Approved")
            {
                 message = Uri.EscapeDataString("تم قبولك، يمكنك الآن تسجيل دخولك للمنصة");
            }
          else if (statusText == "Rejected")
            {
                message = Uri.EscapeDataString("تم رفضلك، راجع المؤسسة لمعرفة الاسباب");
            }
            else
            {
                message = Uri.EscapeDataString("بيانات قيد المراجعة، انتظر قبولك من مدير المنصة");
            }
                string url = $"https://wa.me/{phone}?text={message}";
            return Redirect(url);

          
        }






        public async Task<IActionResult> StatusHistory()
        {
            var logs = await _context.TrainerStatusLogs
                .Include(l => l.Trainer)
                .OrderByDescending(l => l.ChangeTime)
                .ToListAsync();

            return View(logs);
        }


        // ✅ عرض تفاصيل مدرب
        public async Task<IActionResult> Details(string id)
        {
            if (string.IsNullOrEmpty(id))
                return BadRequest();

            var trainer = await _context.Trainers
                .Include(t => t.StatusLogs)
                .FirstOrDefaultAsync(t => t.Id == id);

            if (trainer == null)
                return NotFound();

            return View(trainer);
        }


    }
}


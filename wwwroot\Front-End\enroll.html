<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاشتراك في الدورة - رؤية 2030</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .enrollment-section {
            padding: 4rem 0;
            background-color: var(--bg-light);
            min-height: 80vh;
        }
        
        .enrollment-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: var(--light-color);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }
        
        .enrollment-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: var(--light-color);
            padding: 2rem;
            text-align: center;
        }
        
        .enrollment-header h1 {
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
        }
        
        .enrollment-header p {
            opacity: 0.9;
        }
        
        .enrollment-form {
            padding: 3rem;
        }
        
        .form-section {
            margin-bottom: 3rem;
        }
        
        .form-section h3 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            font-size: 1.4rem;
            border-bottom: 2px solid var(--bg-light);
            padding-bottom: 0.5rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--secondary-color);
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid var(--bg-light);
            border-radius: var(--border-radius);
            font-family: 'Tajawal', sans-serif;
            font-size: 1rem;
            transition: var(--transition);
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .radio-group {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }
        
        .radio-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .radio-item input[type="radio"] {
            width: auto;
        }
        
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
        
        .checkbox-item:hover {
            background-color: var(--bg-light);
        }
        
        .checkbox-item input[type="checkbox"] {
            width: auto;
        }
        
        .submit-section {
            text-align: center;
            padding-top: 2rem;
            border-top: 2px solid var(--bg-light);
        }
        
        .submit-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: var(--light-color);
            padding: 1.2rem 3rem;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            font-family: 'Tajawal', sans-serif;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 87, 34, 0.3);
        }

        .info-note {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            border: 1px solid rgba(255, 87, 34, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .info-note i {
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .info-note p {
            margin: 0;
            color: var(--secondary-color);
            font-weight: 500;
        }
        
        .required {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- ناف بار -->
    <nav class="navbar">
        <div class="logo">
            <img src="logo.png" alt="رؤية 2030">
        </div>
        <div class="nav-links">
            <a href="index.html">الصفحة الرئيسية</a>
            <a href="courses.html">الدورات</a>
            <a href="my-courses.html">دوراتي</a>
            <a href="about.html">نبذة عنا</a>
        </div>
        <div class="auth-buttons">
            <a href="login.html" class="login-btn">تسجيل دخول</a>
        </div>
    </nav>

    <!-- قسم الاشتراك -->
    <section class="enrollment-section">
        <div class="container">
            <div class="enrollment-container">
                <div class="enrollment-header">
                    <h1>الاشتراك في دورة تطوير مواقع الويب</h1>
                    <p>يرجى ملء النموذج التالي للاشتراك في الدورة</p>
                </div>
                
                <form class="enrollment-form">
                    <!-- ملاحظة: البيانات الشخصية محفوظة في حسابك -->
                    <div class="form-section">
                        <div class="info-note">
                            <i class="fas fa-info-circle"></i>
                            <p>سيتم استخدام البيانات الشخصية المحفوظة في حسابك. يمكنك تعديلها من صفحة الإعدادات.</p>
                        </div>
                    </div>
                    
                    <!-- المعلومات التعليمية والمهنية -->
                    <div class="form-section">
                        <h3><i class="fas fa-graduation-cap"></i> المعلومات التعليمية والمهنية</h3>
                        
                        <div class="form-group">
                            <label for="education">المستوى التعليمي <span class="required">*</span></label>
                            <select id="education" name="education" required>
                                <option value="">اختر المستوى التعليمي</option>
                                <option value="high_school">ثانوية عامة</option>
                                <option value="diploma">دبلوم</option>
                                <option value="bachelor">بكالوريوس</option>
                                <option value="master">ماجستير</option>
                                <option value="phd">دكتوراه</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="profession">المهنة/التخصص</label>
                            <input type="text" id="profession" name="profession">
                        </div>
                        
                        <div class="form-group">
                            <label>الحالة المهنية <span class="required">*</span></label>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" id="employed" name="employment_status" value="employed" required>
                                    <label for="employed">موظف</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="unemployed" name="employment_status" value="unemployed" required>
                                    <label for="unemployed">عاطل عن العمل</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="student" name="employment_status" value="student" required>
                                    <label for="student">طالب</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="freelancer" name="employment_status" value="freelancer" required>
                                    <label for="freelancer">عمل حر</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الخبرة التقنية -->
                    <div class="form-section">
                        <h3><i class="fas fa-code"></i> الخبرة التقنية</h3>
                        
                        <div class="form-group">
                            <label>مستوى خبرتك في البرمجة <span class="required">*</span></label>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" id="beginner" name="programming_level" value="beginner" required>
                                    <label for="beginner">مبتدئ (لا توجد خبرة)</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="intermediate" name="programming_level" value="intermediate" required>
                                    <label for="intermediate">متوسط</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="advanced" name="programming_level" value="advanced" required>
                                    <label for="advanced">متقدم</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>اللغات البرمجية التي تعرفها (اختياري)</label>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="html" name="languages[]" value="html">
                                    <label for="html">HTML</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="css" name="languages[]" value="css">
                                    <label for="css">CSS</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="javascript" name="languages[]" value="javascript">
                                    <label for="javascript">JavaScript</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="python" name="languages[]" value="python">
                                    <label for="python">Python</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="java" name="languages[]" value="java">
                                    <label for="java">Java</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="none" name="languages[]" value="none">
                                    <label for="none">لا أعرف أي لغة</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الدافع والأهداف -->
                    <div class="form-section">
                        <h3><i class="fas fa-target"></i> الدافع والأهداف</h3>
                        
                        <div class="form-group">
                            <label for="motivation">لماذا تريد الانضمام لهذه الدورة؟ <span class="required">*</span></label>
                            <textarea id="motivation" name="motivation" placeholder="اكتب دافعك للانضمام للدورة..." required></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="goals">ما هي أهدافك من هذه الدورة؟</label>
                            <textarea id="goals" name="goals" placeholder="اكتب أهدافك من الدورة..."></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label>كيف سمعت عن هذه الدورة؟</label>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" id="social_media" name="heard_about" value="social_media">
                                    <label for="social_media">وسائل التواصل الاجتماعي</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="friends" name="heard_about" value="friends">
                                    <label for="friends">الأصدقاء</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="website" name="heard_about" value="website">
                                    <label for="website">الموقع الإلكتروني</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="other_source" name="heard_about" value="other">
                                    <label for="other_source">أخرى</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إرسال النموذج -->
                    <div class="submit-section">
                        <button type="submit" class="submit-btn">
                            <i class="fas fa-paper-plane"></i>
                            إرسال طلب الاشتراك
                        </button>
                        <p style="margin-top: 1rem; color: var(--secondary-color); font-size: 0.9rem;">
                            سيتم مراجعة طلبك والرد عليك خلال 48 ساعة
                        </p>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- الفوتر -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="logo.png" alt="رؤية 2030">
                </div>
                <div class="footer-links">
                    <h3>روابط سريعة</h3>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="courses.html">الدورات</a></li>
                        <li><a href="my-courses.html">دوراتي</a></li>
                        <li><a href="edit-profile.html">تعديل البيانات</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>تواصل معنا</h3>
                    <p><i class="fas fa-map-marker-alt"></i> بنغازي، ليبيا</p>
                    <p><i class="fas fa-phone"></i> +218 91-234-5678</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
                <div class="footer-social">
                    <h3>تابعنا</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 رؤية بنغازي 2030. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>
</body>
</html>

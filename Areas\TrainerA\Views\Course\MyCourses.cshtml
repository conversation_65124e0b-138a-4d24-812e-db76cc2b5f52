﻿@model IEnumerable<Roya.Models.Course>

@{
    ViewData["Title"] = "كورساتي";
}

<div class="container my-5" style="max-width: 900px;">
    <h2 class="text-center mb-4">📚 كورساتي</h2>

    <!-- 🔍 شريط البحث -->
    <form method="get" asp-action="MyCourses" class="d-flex justify-content-center mb-4">
        <input type="text" name="search" class="form-control w-50 me-2" placeholder="🔍 ابحث باسم الكورس" value="@Context.Request.Query["search"]" />
        <button type="submit" class="btn btn-outline-primary">بحث</button>
    </form>

    <!-- ➕ زر إضافة كورس -->
    <div class="text-end mb-3">
        <a asp-action="Create" class="btn btn-dark">➕ إضافة كورس جديد</a>
    </div>

    <!-- 🧾 جدول الكورسات -->
    <div class="table-responsive">
        <table class="table table-bordered table-hover table-striped shadow-sm text-center">
            <thead class="table-light">
                <tr>
                    <th>📘 اسم الكورس</th>
                    <th>🕒 تاريخ الإضافة</th>
                    <th>📌 الحالة</th>
                    <th>🔍</th>
                </tr>
            </thead>
            <tbody>
                @if (!Model.Any())
                {
                    <tr>
                        <td colspan="4" class="text-center text-muted py-4">
                            ❌ لا توجد نتائج مطابقة للبحث.
                        </td>
                    </tr>
                }
                else
                {
                    foreach (var course in Model)
                    {
                        <tr>
                            <td class="fw-bold">@course.CourseName</td>
                            <td>@course.CreatedDate.ToString("yyyy/MM/dd HH:mm")</td>
                            <td>
                                @switch (course.Status)
                                {
                                    case Roya.Models.CourseStatus.Approved:
                                        <span class="badge bg-success">مقبول</span>
                                        break;
                                    case Roya.Models.CourseStatus.Pending:
                                        <span class="badge bg-warning text-dark">قيد المراجعة</span>
                                        break;
                                    case Roya.Models.CourseStatus.Rejected:
                                        <span class="badge bg-danger">مرفوض</span>
                                        break;
                                }
                            </td>
                            <td>
                                <a asp-action="Details" asp-route-id="@course.CourseId" class="btn btn-sm btn-outline-primary">عرض</a>
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
    </div>
</div>

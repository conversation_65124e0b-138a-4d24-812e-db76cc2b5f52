﻿using System.ComponentModel.DataAnnotations;

namespace Roya.ViewModels
{
    public class TrainerRegisterViewModel
    {
        [Required(ErrorMessage = "الرجاء إدخال الاسم الكامل.")]
        public string FullName { get; set; }


        [Required(ErrorMessage = "الرجاء إدخال البريد الإلكتروني.")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صالح.")]
        public string Email { get; set; }
        [Required(ErrorMessage = "الرجاء إدخال رقم الهاتف.")]
        [RegularExpression(@"^\d{8}$", ErrorMessage = "أدخل فقط آخر 8 أرقام من رقم الهاتف.")]
        public string PhoneNum { get; set; }


        [Required(ErrorMessage = "الرجاء إدخال المدينة.")]
        public string City { get; set; }

        [Required(ErrorMessage = "الرجاء إدخال العمر.")]
        [Range(10, 100, ErrorMessage = "العمر يجب أن يكون بين 10 و 100.")]
        public int Age { get; set; }

        [Required(ErrorMessage = "الرجاء  ادخال ملف السيره الذاتيه.")]
        public IFormFile CVFile { get; set; }


        [Required(ErrorMessage = "الرجاء إدخال المهنه / التخصص.")]
        public string ExperienceYears { get; set; }

        public IFormFile ImageFile { get; set; }

        [Required(ErrorMessage = "الرجاء إدخال كلمة المرور.")]
        [DataType(DataType.Password)]
        [StringLength(100, ErrorMessage = "كلمة المرور يجب أن تكون على الأقل {2} حروف.", MinimumLength = 6)]
        public string Password { get; set; }

        [Compare("Password", ErrorMessage = "كلمة المرور وتأكيدها غير متطابقين.")]
        [DataType(DataType.Password)]
        public string ConfirmPassword { get; set; }

    }
}

﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Roya.Data;
using Roya.Models;
using Roya.ViewModels;

namespace Roya.Controllers
{
    public class BrowseCoursesController : Controller
    {
        private readonly AppDbContext _context;
        private readonly UserManager<IdentityUser> _userManager;

        public BrowseCoursesController(AppDbContext context, UserManager<IdentityUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public async Task<IActionResult> Index(string search, int? categoryId)
        {
            var courses = _context.Courses
                .Include(c => c.Category)
                .Include(c => c.Trainer)
                .Where(c => c.Status == CourseStatus.Approved);

            if (!string.IsNullOrEmpty(search))
                courses = courses.Where(c => c.CourseName.Contains(search));

            if (categoryId.HasValue)
                courses = courses.Where(c => c.CategoryId == categoryId.Value);

            ViewBag.Categories = await _context.Categories.ToListAsync();

            // ✅ استخدم alert من Query String فقط
            var alert = Request.Query["alert"].ToString();
            if (alert == "success")
                ViewBag.JoinSuccess = "✅ تم تقديم اشتراكك بنجاح، سيتم مراجعته من قبل الإدارة.";
            else if (alert == "duplicate")
                ViewBag.AlreadyRegistered = "⚠️ لقد سبق وأن قمت بالاشتراك أو الإجابة على هذه الدورة.";

            return View(await courses.ToListAsync());
        }

        public async Task<IActionResult> Details(int id)
        {
            var course = await _context.Courses
                .Include(c => c.Category)
                .Include(c => c.Trainer)
                .FirstOrDefaultAsync(c => c.CourseId == id && c.Status == CourseStatus.Approved);

            if (course == null)
                return NotFound();

            return View(course);
        }

        public async Task<IActionResult> Join(int id)
        {
            var user = await _userManager.GetUserAsync(User);
            var trainee = await _context.Trainees.FirstOrDefaultAsync(t => t.Id == user.Id);
            if (trainee == null) return Unauthorized();

            bool alreadyRegistered = await _context.Registers
                .AnyAsync(r => r.CourseId == id && r.userId == user.Id);

            if (alreadyRegistered)
            {
                return RedirectToAction("Index", new { alert = "duplicate" });
            }

            var course = await _context.Courses
                .Include(c => c.Questions)
                    .ThenInclude(q => q.Options)
                .FirstOrDefaultAsync(c => c.CourseId == id && c.Status == CourseStatus.Approved);

            if (course == null) return NotFound();

            var vm = new CourseJoinVM
            {
                Course = course,
                Questions = course.Questions
            };

            return View(vm);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Join(int id, CourseJoinVM vm)
        {
            var user = await _userManager.GetUserAsync(User);
            var trainee = await _context.Trainees.FirstOrDefaultAsync(t => t.Id == user.Id);
            if (trainee == null) return Unauthorized();

            var course = await _context.Courses
                .Include(c => c.Questions)
                .FirstOrDefaultAsync(c => c.CourseId == id && c.Status == CourseStatus.Approved);
            if (course == null) return NotFound();

            if (vm.Answers == null || !vm.Answers.Any())
            {
                ModelState.AddModelError("", "يرجى الإجابة على الأسئلة.");
                vm.Questions = course.Questions;
                vm.Course = course;
                return View(vm);
            }

            foreach (var entry in vm.Answers)
            {
                var answer = new Answer
                {
                    CourseId = id,
                    QuestionId = entry.Key,
                    UserId = user.Id,
                    AnswerText = entry.Value
                };
                _context.Answers.Add(answer);
            }

            var register = new Register
            {
                userId = user.Id,
                CourseId = id,
                RegistrationDate = DateTime.Now,
                RegistrationStatus = RegistrationStatus.Pending
            };

            _context.Registers.Add(register);
            await _context.SaveChangesAsync();

            return RedirectToAction("Index", new { alert = "success" });
        }
    

        public async Task<IActionResult> MyCourses(string status)
        {
            var user = await _userManager.GetUserAsync(User);

            var query = _context.Registers
                .Include(r => r.Course)
                .ThenInclude(c => c.Trainer)
                .Where(r => r.userId == user.Id)
                .AsQueryable();

            if (!string.IsNullOrEmpty(status) && Enum.TryParse(status, out RegistrationStatus parsedStatus))
            {
                query = query.Where(r => r.RegistrationStatus == parsedStatus);
            }

            var courses = await query
                .Select(r => new MyCoursesVM
                {
                    CourseId = r.CourseId,
                    CourseName = r.Course.CourseName,
                    TrainerName = r.Course.Trainer.FullName,
                    StartDate = r.Course.StartDate,
                    Status = r.RegistrationStatus
                }).ToListAsync();

            return View(courses);
        }
    }
}
        
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دوراتي - رؤية 2030</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .my-courses-section {
            padding: 4rem 0;
            background-color: var(--bg-light);
            min-height: 80vh;
        }
        
        .page-title {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .page-title h1 {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .page-title p {
            color: var(--secondary-color);
            font-size: 1.1rem;
        }
        
        .search-filter-section {
            background-color: var(--light-color);
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 2rem;
        }
        
        .search-filter {
            display: flex;
            gap: 2rem;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-box {
            flex: 1;
            min-width: 300px;
            display: flex;
            background-color: var(--bg-light);
            border-radius: var(--border-radius);
            overflow: hidden;
        }
        
        .search-box input {
            flex: 1;
            padding: 1rem;
            border: none;
            font-family: 'Tajawal', sans-serif;
            background: transparent;
        }
        
        .search-box input:focus {
            outline: none;
        }
        
        .search-box button {
            padding: 1rem 1.5rem;
            background-color: var(--primary-color);
            color: var(--light-color);
            border: none;
            cursor: pointer;
        }
        
        .status-filter {
            display: flex;
            gap: 1rem;
        }
        
        .filter-btn {
            padding: 0.8rem 1.5rem;
            background-color: var(--bg-light);
            color: var(--secondary-color);
            border: 2px solid transparent;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            font-family: 'Tajawal', sans-serif;
        }
        
        .filter-btn:hover,
        .filter-btn.active {
            background-color: var(--primary-color);
            color: var(--light-color);
            border-color: var(--primary-color);
        }
        
        .courses-table-container {
            background-color: var(--light-color);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }
        
        .courses-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .courses-table th,
        .courses-table td {
            padding: 1.5rem 1rem;
            text-align: right;
            border-bottom: 1px solid var(--bg-light);
        }
        
        .courses-table th {
            background-color: var(--bg-orange);
            color: var(--secondary-color);
            font-weight: 600;
            font-size: 1rem;
        }
        
        .courses-table td {
            color: var(--dark-color);
        }
        
        .courses-table tr:hover {
            background-color: var(--bg-light);
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-align: center;
            display: inline-block;
            min-width: 80px;
        }
        
        .status-accepted {
            background-color: #e8f5e8;
            color: #2e7d32;
        }
        
        .status-pending {
            background-color: #fff3e0;
            color: #f57c00;
        }
        
        .status-rejected {
            background-color: #ffebee;
            color: #d32f2f;
        }
        
        .action-btn {
            padding: 0.5rem 1rem;
            background-color: var(--primary-color);
            color: var(--light-color);
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            font-family: 'Tajawal', sans-serif;
            text-decoration: none;
            display: inline-block;
            font-size: 0.9rem;
        }
        
        .action-btn:hover {
            background-color: var(--primary-dark);
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--secondary-color);
        }
        
        .empty-state i {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .empty-state p {
            margin-bottom: 2rem;
        }
        
        .empty-state .cta-btn {
            display: inline-block;
            padding: 1rem 2rem;
            background-color: var(--primary-color);
            color: var(--light-color);
            text-decoration: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            transition: var(--transition);
        }
        
        .empty-state .cta-btn:hover {
            background-color: var(--primary-dark);
        }
        
        @media (max-width: 768px) {
            .search-filter {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                min-width: auto;
            }
            
            .status-filter {
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .courses-table-container {
                overflow-x: auto;
            }
            
            .courses-table {
                min-width: 600px;
            }
        }
    </style>
</head>
<body>
    <!-- ناف بار -->
    <nav class="navbar">
        <div class="logo">
            <img src="logo.png" alt="رؤية 2030">
        </div>
        <div class="nav-links">
            <a href="index.html">الصفحة الرئيسية</a>
            <a href="courses.html">الدورات</a>
            <a href="my-courses.html" class="active">دوراتي</a>
            <a href="about.html">نبذة عنا</a>
        </div>
        <div class="auth-buttons">
            <a href="login.html" class="login-btn">تسجيل دخول</a>
        </div>
    </nav>

    <!-- قسم دوراتي -->
    <section class="my-courses-section">
        <div class="container">
            <div class="page-title">
                <h1>دوراتي</h1>
                <p>تابع حالة الدورات التي اشتركت فيها</p>
            </div>
            
            <!-- البحث والفلترة -->
            <div class="search-filter-section">
                <div class="search-filter">
                    <div class="search-box">
                        <input type="text" placeholder="ابحث في دوراتك...">
                        <button><i class="fas fa-search"></i></button>
                    </div>
                    <div class="status-filter">
                        <button class="filter-btn active" data-status="all">الكل</button>
                        <button class="filter-btn" data-status="accepted">مقبول</button>
                        <button class="filter-btn" data-status="pending">قيد المراجعة</button>
                        <button class="filter-btn" data-status="rejected">مرفوض</button>
                    </div>
                </div>
            </div>
            
            <!-- جدول الدورات -->
            <div class="courses-table-container">
                <table class="courses-table">
                    <thead>
                        <tr>
                            <th>اسم الدورة</th>
                            <th>المدرب</th>
                            <th>تاريخ التقديم</th>
                            <th>تاريخ البدء</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr data-status="accepted">
                            <td>تطوير مواقع الويب</td>
                            <td>أحمد محمد</td>
                            <td>2023/10/01</td>
                            <td>2023/10/15</td>
                            <td><span class="status-badge status-accepted">مقبول</span></td>
                            <td><a href="course-details.html?id=1" class="action-btn">عرض التفاصيل</a></td>
                        </tr>
                        <tr data-status="pending">
                            <td>التسويق الرقمي</td>
                            <td>فاطمة علي</td>
                            <td>2023/10/05</td>
                            <td>2023/10/20</td>
                            <td><span class="status-badge status-pending">قيد المراجعة</span></td>
                            <td><a href="course-details.html?id=2" class="action-btn">عرض التفاصيل</a></td>
                        </tr>
                        <tr data-status="accepted">
                            <td>البرمجة بـ Python</td>
                            <td>خالد محمود</td>
                            <td>2023/10/03</td>
                            <td>2023/11/05</td>
                            <td><span class="status-badge status-accepted">مقبول</span></td>
                            <td><a href="course-details.html?id=5" class="action-btn">عرض التفاصيل</a></td>
                        </tr>
                        <tr data-status="rejected">
                            <td>إدارة الأعمال</td>
                            <td>محمد سالم</td>
                            <td>2023/09/28</td>
                            <td>2023/10/25</td>
                            <td><span class="status-badge status-rejected">مرفوض</span></td>
                            <td><a href="course-details.html?id=3" class="action-btn">عرض التفاصيل</a></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- حالة فارغة (مخفية افتراضياً) -->
            <div class="empty-state" style="display: none;">
                <i class="fas fa-book-open"></i>
                <h3>لم تشترك في أي دورة بعد</h3>
                <p>ابدأ رحلتك التعليمية واشترك في إحدى دوراتنا المتميزة</p>
                <a href="courses.html" class="cta-btn">تصفح الدورات</a>
            </div>
        </div>
    </section>

    <!-- الفوتر -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="logo.png" alt="رؤية 2030">
                </div>
                <div class="footer-links">
                    <h3>روابط سريعة</h3>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="courses.html">الدورات</a></li>
                        <li><a href="my-courses.html">دوراتي</a></li>
                        <li><a href="edit-profile.html">تعديل البيانات</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>تواصل معنا</h3>
                    <p><i class="fas fa-map-marker-alt"></i> بنغازي، ليبيا</p>
                    <p><i class="fas fa-phone"></i> +218 91-234-5678</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
                <div class="footer-social">
                    <h3>تابعنا</h3>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 رؤية بنغازي 2030. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>
</body>
</html>

﻿using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Roya.Models;
using System.ComponentModel.DataAnnotations;


namespace Roya.Models
{
    public class Answer
    {
        [Key]
        public int Id { get; set; }

        public string AnswerText { get; set; }



        public int CourseId { get; set; }
        [ValidateNever]
        public Course Course { get; set; }


        public string UserId { get; set; }
        [ValidateNever]

        public Trainee Trainee { get; set; }


        public int QuestionId { get; set; }
        [ValidateNever]
        public Question Question { get; set; }




    }
}

/* تنسيقات صفحة تفاصيل الدورة */

.course-details-section {
    padding: 3rem 10%;
    background: linear-gradient(135deg, var(--bg-light) 0%, #fafafa 100%);
}

/* الأقسام الجديدة */
.course-section {
    background: white;
    border-radius: 15px;
    padding: 2.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 87, 34, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
}

.course-section:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(255, 87, 34, 0.15);
    border-color: var(--primary-color);
}

.course-section:hover h2 {
    color: #e64a19;
    transform: translateY(-2px);
}

.course-section:hover h2 i {
    transform: scale(1.1) rotate(5deg);
}

.course-section h2 {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-bottom: 2px solid rgba(255, 87, 34, 0.1);
    padding-bottom: 1rem;
}

.course-section h2 i {
    font-size: 1.5rem;
}

/* محاور الدورة */
.course-modules {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.module {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
}

.module:hover {
    transform: translateY(-5px) scale(1.03);
    box-shadow: 0 10px 25px rgba(255, 87, 34, 0.15);
    border-color: var(--primary-color);
}

.module:hover .module-header {
    background: linear-gradient(135deg, #e64a19, var(--primary-color));
}

.module:hover .module-duration {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.module:hover .module-topics li::before {
    color: #e64a19;
    transform: scale(1.2);
}

.module-header {
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.module-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.module-duration {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

.module-topics {
    padding: 1.5rem;
    margin: 0;
    list-style: none;
}

.module-topics li {
    padding: 0.8rem 0;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
    padding-right: 2rem;
}

.module-topics li:last-child {
    border-bottom: none;
}

.module-topics li::before {
    content: '✓';
    position: absolute;
    right: 0;
    top: 0.8rem;
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.1rem;
}

/* وصف الدورة */
.course-description p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
}

.description-highlights {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem;
    background: rgba(255, 87, 34, 0.05);
    border-radius: 10px;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
    cursor: pointer;
}

.highlight-item:hover {
    transform: translateX(5px) scale(1.02);
    background: rgba(255, 87, 34, 0.1);
    border-left-width: 6px;
    box-shadow: 0 5px 15px rgba(255, 87, 34, 0.1);
}

.highlight-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.highlight-item span {
    font-weight: 500;
    color: var(--secondary-color);
}

/* المستهدفين من الدورة */
.target-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1rem;
}

.target-list li {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.2rem;
    background: linear-gradient(135deg, rgba(255, 87, 34, 0.05), rgba(255, 107, 53, 0.05));
    border-radius: 12px;
    border-left: 4px solid var(--primary-color);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    font-size: 1rem;
    line-height: 1.5;
    color: var(--secondary-color);
    cursor: pointer;
}

.target-list li:hover {
    transform: translateX(8px) scale(1.02);
    background: linear-gradient(135deg, rgba(255, 87, 34, 0.12), rgba(255, 107, 53, 0.12));
    box-shadow: 0 8px 20px rgba(255, 87, 34, 0.2);
    border-left-width: 6px;
}

.target-list li:hover i {
    color: #e64a19;
    transform: scale(1.2) rotate(5deg);
}

.target-list li i {
    color: var(--primary-color);
    font-size: 1.3rem;
    min-width: 20px;
    text-align: center;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .course-details-section {
        padding: 2rem 5%;
    }

    .course-header {
        flex-direction: column;
        gap: 0;
    }

    .course-image {
        max-width: 100%;
    }

    .course-info {
        padding: 2rem;
    }

    .course-info h1 {
        font-size: 2rem;
    }

    .course-section {
        padding: 2rem;
        margin-bottom: 1.5rem;
    }

    .course-section:hover {
        transform: translateY(-5px) scale(1.01);
    }

    .module:hover {
        transform: translateY(-3px) scale(1.02);
    }

    .target-list li:hover {
        transform: translateX(5px) scale(1.01);
    }

    .course-section h2 {
        font-size: 1.5rem;
    }

    .module-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .description-highlights {
        grid-template-columns: 1fr;
    }

    .target-list {
        grid-template-columns: 1fr;
        gap: 0.8rem;
    }

    .target-list li {
        padding: 1rem;
        font-size: 0.95rem;
    }

    .target-list li i {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .course-details-section {
        padding: 1rem 3%;
    }

    .course-section {
        padding: 1.5rem;
    }

    .module-header h3 {
        font-size: 1.1rem;
    }

    .module-topics li {
        font-size: 0.9rem;
    }

    .target-list li {
        font-size: 0.9rem;
        padding: 0.8rem;
    }
}

/* رأس الدورة */
.course-header {
    display: flex;
    gap: 3rem;
    margin-bottom: 3rem;
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
}

.course-header:hover {
    transform: translateY(-5px) scale(1.01);
    box-shadow: 0 15px 35px rgba(255, 87, 34, 0.15);
}

.course-image {
    flex: 1;
    max-width: 40%;
}

.course-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.course-info {
    flex: 1;
    padding: 2.5rem;
    display: flex;
    flex-direction: column;
}

.course-info h1 {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.8rem;
}

.course-info h1::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
}

.course-meta {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.2rem;
    margin-bottom: 2rem;
    background: rgba(255, 87, 34, 0.05);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 87, 34, 0.1);
}

.course-price {
    margin-bottom: 1.5rem;
}

.course-price .price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.course-meta p {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-size: 1.1rem;
}

.course-meta p i {
    color: var(--primary-color);
    font-size: 1.2rem;
    min-width: 20px;
}

/* تنسيق خاص للمكان */
.course-meta p i.fa-map-marker-alt {
    color: #e74c3c;
}

/* تنسيق خاص لتاريخ الانتهاء */
.course-meta p i.fa-calendar-check {
    color: #27ae60;
}

/* تحسين عرض النص للمكان */
.course-meta p:has(i.fa-map-marker-alt) {
    grid-column: 1 / -1; /* يأخذ العرض الكامل */
    background: rgba(231, 76, 60, 0.1);
    padding: 0.8rem;
    border-radius: 8px;
    border-left: 4px solid #e74c3c;
}

.course-info .enroll-btn {
    margin-top: auto;
    display: inline-block;
    padding: 1rem 2rem;
    background-color: var(--primary-color);
    color: var(--light-color);
    border-radius: var(--border-radius);
    font-weight: 600;
    text-align: center;
    transition: var(--transition);
    box-shadow: 0 4px 10px rgba(255, 87, 34, 0.3);
}

.course-info .enroll-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(255, 87, 34, 0.4);
}

/* محتوى الدورة */
.course-content {
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

/* تبويبات الدورة */
.course-tabs {
    display: flex;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #f5f5f5;
}

.tab-btn {
    padding: 1.2rem 2rem;
    background: none;
    border: none;
    font-family: 'Tajawal', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--secondary-color);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.tab-btn:hover {
    color: var(--primary-color);
}

.tab-btn.active {
    color: var(--primary-color);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary-color);
}

/* محتوى التبويبات */
.tab-content {
    padding: 2.5rem;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* وصف الدورة */
.tab-pane h2 {
    font-size: 2rem;
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-block;
    padding-bottom: 0.5rem;
}

.tab-pane h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background-color: var(--primary-color);
}

.tab-pane p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--secondary-color);
}

.tab-pane h3 {
    font-size: 1.4rem;
    color: var(--secondary-color);
    margin: 2rem 0 1rem;
}

.tab-pane ul {
    padding-right: 1.5rem;
    margin-bottom: 1.5rem;
}

.tab-pane ul li {
    margin-bottom: 0.8rem;
    position: relative;
    padding-right: 1.5rem;
    font-size: 1.05rem;
}

.tab-pane ul li::before {
    content: '\f054';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 0;
    top: 5px;
    color: var(--primary-color);
    font-size: 0.8rem;
}

/* منهج الدورة */
.curriculum-weeks {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.week {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
}

.week:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.week-header {
    padding: 1.2rem 1.5rem;
    background-color: #f9f9f9;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: var(--transition);
}

.week-header:hover {
    background-color: rgba(255, 87, 34, 0.05);
}

.week-header h3 {
    font-size: 1.2rem;
    margin: 0;
}

.toggle-icon {
    color: var(--primary-color);
    transition: var(--transition);
}

.week.active .toggle-icon {
    transform: rotate(180deg);
}

.week-content {
    padding: 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background-color: var(--light-color);
}

/* المدرب */
.instructor-profile {
    display: flex;
    gap: 2.5rem;
    align-items: flex-start;
}

.instructor-image {
    flex: 0 0 200px;
}

.instructor-image img {
    width: 100%;
    border-radius: 50%;
    box-shadow: var(--box-shadow);
    border: 5px solid rgba(255, 87, 34, 0.1);
}

.instructor-info {
    flex: 1;
}

.instructor-info h3 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    color: var(--secondary-color);
}

.instructor-title {
    color: var(--primary-color);
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.instructor-bio {
    margin-bottom: 1.5rem;
    line-height: 1.8;
    color: var(--secondary-color);
}

.instructor-social {
    display: flex;
    gap: 1rem;
}

.instructor-social a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #f5f5f5;
    border-radius: 50%;
    color: var(--secondary-color);
    transition: var(--transition);
}

.instructor-social a:hover {
    background-color: var(--primary-color);
    color: var(--light-color);
    transform: translateY(-3px);
}

/* إحصائيات المدرب */
.instructor-stats {
    display: flex;
    gap: 2rem;
    margin: 2rem 0;
    padding: 1.5rem;
    background-color: var(--bg-light);
    border-radius: var(--border-radius);
}

.instructor-stats .stat {
    text-align: center;
    flex: 1;
}

.instructor-stats .number {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.instructor-stats .label {
    font-size: 0.9rem;
    color: var(--secondary-color);
}

/* التقييمات */
.reviews-summary {
    display: flex;
    justify-content: center;
    margin-bottom: 2.5rem;
    background-color: rgba(255, 87, 34, 0.05);
    padding: 2rem;
    border-radius: var(--border-radius);
}

.rating {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.rating-number {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stars {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.rating-count {
    color: var(--secondary-color);
    font-size: 1rem;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .course-header {
        flex-direction: column;
    }

    .course-image {
        max-width: 100%;
    }

    .course-meta {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }

    .course-meta p:has(i.fa-map-marker-alt) {
        grid-column: 1;
        margin-top: 0.5rem;
    }

    .course-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        min-width: 120px;
    }

    .instructor-profile {
        flex-direction: column;
        text-align: center;
    }

    .instructor-image {
        flex: none;
        align-self: center;
    }
}

/* المحتوى المبسط */
.simple-content {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    padding: 2.5rem;
    border-radius: 15px;
    border-right: 4px solid var(--primary-color);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.simple-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,87,34,0.05)"/><circle cx="80" cy="40" r="0.5" fill="rgba(255,87,34,0.05)"/><circle cx="40" cy="80" r="0.8" fill="rgba(255,87,34,0.05)"/></svg>') repeat;
    opacity: 0.3;
    pointer-events: none;
}

.simple-content > * {
    position: relative;
    z-index: 2;
}

.simple-content p {
    margin-bottom: 1.8rem;
    line-height: 1.8;
    color: var(--secondary-color);
    font-size: 1.1rem;
    text-align: justify;
}

.simple-content p:last-child {
    margin-bottom: 0;
}

.simple-content strong {
    color: var(--primary-color);
    font-weight: 600;
}

.simple-content p:first-child {
    font-size: 1.15rem;
    color: #2c3e50;
    font-weight: 500;
}

/* تحسينات responsive للمحتوى المبسط */
@media (max-width: 768px) {
    .simple-content {
        padding: 2rem;
        margin: 1rem 0;
    }

    .simple-content p {
        font-size: 1rem;
        line-height: 1.7;
    }

    .simple-content p:first-child {
        font-size: 1.05rem;
    }
}

@media (max-width: 480px) {
    .simple-content {
        padding: 1.5rem;
        border-right-width: 3px;
    }

    .simple-content p {
        font-size: 0.95rem;
        margin-bottom: 1.5rem;
    }
}
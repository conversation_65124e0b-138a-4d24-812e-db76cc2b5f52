﻿@model Roya.ViewModels.TraineeAnswersVM

@{
    ViewData["Title"] = "إجابات المتدرب";
}

<div class="container mt-5" dir="rtl">
    <h3 class="text-primary text-center mb-4">📋 إجابات المتدرب في الدورة</h3>

    <div class="card mb-4 shadow">
        <div class="card-header bg-info text-white fw-bold">بيانات المتدرب</div>
        <div class="card-body">
            <p><strong>الاسم الكامل:</strong> @Model.FullName</p>
            <p><strong>رقم الهاتف:</strong> @Model.PhoneNum</p>
            <p><strong>المدينة:</strong> @Model.City</p>
            <p><strong>العمر:</strong> @Model.Age</p>
            <p><strong>التخصص/المهنة:</strong> @Model.MajorOrProfession</p>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header bg-secondary text-white fw-bold">الإجابات</div>
        <div class="card-body">
            @if (Model.Answers.Any())
            {
                <ul class="list-group list-group-flush">
                    @foreach (var answer in Model.Answers)
                    {
                        <li class="list-group-item">
                            <strong>السؤال:</strong> @answer.QuestionText <br />
                            <strong>الإجابة:</strong> @answer.AnswerText
                        </li>
                    }
                </ul>
            }
            else
            {
                <div class="alert alert-warning text-center">لم يتم العثور على إجابات.</div>
            }
        </div>
    </div>
    <div class="text-center">
        <button class="btn btn-success mx-2" onclick="confirmAction(true)">✔️ قبول المتدرب</button>
        <button class="btn btn-danger mx-2" onclick="confirmAction(false)">❌ رفض المتدرب</button>
    </div>

</div>
@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
                function confirmAction(isApprove) {
            Swal.fire({
                title: isApprove ? 'تأكيد القبول' : 'تأكيد الرفض',
                text: isApprove ? 'هل أنت متأكد من قبول المتدرب؟' : 'هل أنت متأكد من رفض المتدرب؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: isApprove ? '#28a745' : '#d33',
                cancelButtonColor: '#6c757d',
                confirmButtonText: isApprove ? 'نعم، قبول' : 'نعم، رفض',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = isApprove
                        ? '/TrainerA/TrainerCourses/ApproveParticipant'
                        : '/TrainerA/TrainerCourses/RejectParticipant';

                    const traineeId = document.createElement('input');
                    traineeId.type = 'hidden';
                    traineeId.name = 'traineeId';
                    traineeId.value = '@Model.TraineeId';
                    form.appendChild(traineeId);

                    const courseId = document.createElement('input');
                    courseId.type = 'hidden';
                    courseId.name = 'courseId';
                    courseId.value = '@Model.CourseId';
                    form.appendChild(courseId);

                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }

    </script>
}

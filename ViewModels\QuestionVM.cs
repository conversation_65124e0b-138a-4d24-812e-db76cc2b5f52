﻿using Roya.Models;
using System.ComponentModel.DataAnnotations;

namespace Roya.ViewModels
{
    public class QuestionVM
    {
        public int QuestionId { get; set; }

        [Required]
        public string QuestionText { get; set; }

        public QuestionType QuestionType { get; set; } = QuestionType.Text;
        public string? OptionsText { get; set; } // مفصول بفاصلة (مثال: نعم,لا,ربما)
    }
}

﻿@model List<Trainer>

@{
    ViewData["Title"] = "طلبات المدربين قيد المراجعة";
}

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<style>
    .trainer-card {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border-radius: 16px;
        padding: 20px;
        background-color: #fff;
        transition: 0.3s ease;
        margin-bottom: 20px;
    }

        .trainer-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

    .btn-orange-gradient {
        background: linear-gradient(to right, #f47c3c, #f05a24);
        color: white;
        font-weight: bold;
        border: none;
        border-radius: 50px;
        padding: 7px 18px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }

        .btn-orange-gradient:hover {
            background: linear-gradient(to right, #e24a14, #d54310);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            transform: scale(1.05);
            color: white;
        }

    .icon-black {
        color: #000;
        margin-left: 6px;
    }

    .card-header-title {
        font-size: 1.25rem;
        font-weight: bold;
        color: #F05A24;
    }

    .btn-outline-success-custom {
        color: #28a745;
        border: 2px solid #28a745;
        background-color: transparent;
        font-weight: bold;
        border-radius: 25px;
        padding: 5px 18px;
        transition: 0.3s ease;
    }

        .btn-outline-success-custom:hover {
            background-color: #28a745;
            color: #fff;
        }

    .btn-outline-danger-custom {
        color: #dc3545;
        border: 2px solid #dc3545;
        background-color: transparent;
        font-weight: bold;
        border-radius: 25px;
        padding: 5px 18px;
        transition: 0.3s ease;
    }

        .btn-outline-danger-custom:hover {
            background-color: #dc3545;
            color: #fff;
        }
</style>

<h2 class="text-center mb-5 mt-3">⏳ طلبات المدربين قيد المراجعة</h2>

@if (!Model.Any())
{
    <div class="alert alert-info text-center">لا يوجد مدربون قيد المراجعة حالياً.</div>
}
else
{
    <div class="row justify-content-center">
        @foreach (var trainer in Model)
        {
            <div class="col-md-5 m-3">
                <div class="trainer-card">
                    <h5 class="card-header-title">
                        <i class="bi bi-person-fill icon-black"></i>
                        @trainer.FullName
                    </h5>
                    <p>
                        <i class="bi bi-envelope icon-black"></i>
                        <strong>البريد:</strong> @trainer.Email
                    </p>
                    <p>
                        <i class="bi bi-telephone icon-black"></i>
                        <strong>الهاتف:</strong> @trainer.PhoneNum
                    </p>

                    <div class="d-flex justify-content-between mt-4 flex-wrap gap-2">
                        <form asp-action="UpdateStatus" method="post" class="confirm-form" data-current-status="@trainer.Status" data-trainer-name="@trainer.FullName">
                            <input type="hidden" name="id" value="@trainer.Id" />
                            <input type="hidden" name="newStatus" value="Approved" />
                            <button class="btn btn-outline-success-custom btn-sm" type="submit">✔ موافقة</button>
                        </form>

                        <form asp-action="UpdateStatus" method="post" class="confirm-form" data-current-status="@trainer.Status" data-trainer-name="@trainer.FullName">
                            <input type="hidden" name="id" value="@trainer.Id" />
                            <input type="hidden" name="newStatus" value="Rejected" />
                            <button class="btn btn-outline-danger-custom btn-sm" type="submit">✖ رفض</button>
                        </form>

                        <a asp-action="Details" asp-route-id="@trainer.Id" class="btn-orange-gradient">
                            📄 عرض التفاصيل
                        </a>
                    </div>
                </div>
            </div>
        }
    </div>
}

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const forms = document.querySelectorAll(".confirm-form");

        forms.forEach(form => {
            form.addEventListener("submit", function (e) {
                e.preventDefault();

                const currentStatus = form.dataset.currentStatus;
                const newStatus = form.querySelector("input[name='newStatus']").value;
                const trainerName = form.dataset.trainerName || "هذا المدرب";

                if (currentStatus === newStatus) {
                    Swal.fire({
                        icon: 'info',
                        title: 'لا حاجة للإجراء',
                        text: 'الحالة بالفعل ' + getArabicStatusName(newStatus),
                        confirmButtonColor: '#28a745'
                    });
                    return;
                }

                let message = "";
                if (newStatus === "Approved") {
                    message = `؟${trainerName}هل أنت متأكد أنك تريد قبول المدرب `;
                } else if (newStatus === "Rejected") {
                    message = `؟${trainerName}هل أنت متأكد أنك تريد رفض المدرب `;
                }

                Swal.fire({
                    title: 'تأكيد الإجراء',
                    text: message,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#28a745',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'نعم، تأكيد',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        form.submit();
                    }
                });
            });
        });

        function getArabicStatusName(status) {
            switch (status) {
                case "Approved": return "مقبول";
                case "Rejected": return "مرفوض";
                case "Pending": return "قيد المراجعة";
                default: return "";
            }
        }
    });
</script>

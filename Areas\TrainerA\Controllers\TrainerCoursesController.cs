﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Roya.Data;
using Roya.Models;
using Roya.ViewModels;
namespace Roya.Areas.TrainerA.Controllers
{
    [Authorize(Roles = "Trainer")]
    [Area("TrainerA")]
    public class TrainerCoursesController : Controller
    {
        
            private readonly AppDbContext _context;
            private readonly UserManager<IdentityUser> _userManager;

            public TrainerCoursesController(AppDbContext context, UserManager<IdentityUser> userManager)
            {
                _context = context;
                _userManager = userManager;
            }

            // ✅ صفحة رئيسية تعرض دورات المدرب التي تحتوي على مشتركين فقط
            public async Task<IActionResult> CoursesWithParticipants()
            {
                var user = await _userManager.GetUserAsync(User);
                var trainer = await _context.Trainers.FirstOrDefaultAsync(t => t.Id == user.Id);
                if (trainer == null) return Unauthorized();

                var coursesWithParticipants = await _context.Courses
                    .Where(c => c.UserId == trainer.Id)
                    .Where(c => c.Registers.Any()) // ⬅️ فيها مشتركين فعلاً
                    .Select(c => new TrainerCourseVM
                    {
                        CourseId = c.CourseId,
                        CourseName = c.CourseName,
                        ParticipantCount = c.Registers.Count
                    })
                    .ToListAsync();

                return View(coursesWithParticipants);
            }

        // ✅ عرض الطلبة المشاركين في دورة معينة
        public async Task<IActionResult> Participants(int id, string search)
        {
            var course = await _context.Courses
                .Include(c => c.Registers).ThenInclude(r => r.Trainee)
                .FirstOrDefaultAsync(c => c.CourseId == id);

            if (course == null) return NotFound();

            var participantsQuery = course.Registers.AsQueryable();

            if (!string.IsNullOrWhiteSpace(search))
            {
                participantsQuery = participantsQuery
                    .Where(r => r.Trainee.FullName.Contains(search));
            }

            var filteredParticipants = participantsQuery.Select(r => new CourseParticipantVM
            {
                TraineeId = r.Trainee.Id,
                FullName = r.Trainee.FullName,
                RegistrationDate = r.RegistrationDate,
                Status = r.RegistrationStatus,
                CourseId = course.CourseId
            }).ToList();

            ViewBag.CourseName = course.CourseName;
            ViewBag.CourseId = id;

            // الإحصائيات العامة
            ViewBag.TotalCount = course.Registers.Count;
            ViewBag.PendingCount = course.Registers.Count(p => p.RegistrationStatus == RegistrationStatus.Pending);
            ViewBag.ApprovedCount = course.Registers.Count(p => p.RegistrationStatus == RegistrationStatus.Approved);
            ViewBag.RejectedCount = course.Registers.Count(p => p.RegistrationStatus == RegistrationStatus.Rejected);

            ViewBag.NotFound = !string.IsNullOrWhiteSpace(search) && !filteredParticipants.Any();

            return View(filteredParticipants);
        }


        // ✅ عرض إجابات متدرب معين في دورة معينة مع بياناته وزر قبول/رفض
        public async Task<IActionResult> ViewAnswers(string traineeId, int courseId)
        {
            var trainee = await _context.Trainees.FirstOrDefaultAsync(t => t.Id == traineeId);
            if (trainee == null) return NotFound();

            var answers = await _context.Answers
                .Include(a => a.Question)
                .Where(a => a.CourseId == courseId && a.UserId == traineeId)
                .ToListAsync();

            var vm = new TraineeAnswersVM
            {
                TraineeId = traineeId,
                CourseId = courseId,
                FullName = trainee.FullName,
                PhoneNum = trainee.PhoneNum,
                City = trainee.City,
                Age = trainee.Age,
                MajorOrProfession = trainee.MajorOrProfession,
                Answers = answers.Select(a => new AnswerDetailVM
                {
                    QuestionText = a.Question.QuestionText,
                    AnswerText = a.AnswerText
                }).ToList()
            };

            return View(vm);
        }
        [HttpPost]
            public async Task<IActionResult> ApproveParticipant(string traineeId, int courseId)
            {
                var register = await _context.Registers
                    .Include(r => r.Trainee) // ⬅️ ضروري لتحميل بيانات المتدرب
                    .Include(r => r.Course)  // ⬅️ ضروري لتحميل بيانات الدورة
                    .FirstOrDefaultAsync(r => r.userId == traineeId && r.CourseId == courseId);

                if (register == null || register.Trainee == null || register.Course == null)
                    return NotFound();

                register.RegistrationStatus = RegistrationStatus.Approved;
                await _context.SaveChangesAsync();

                string phone = register.Trainee.PhoneNum;
              

                string courseName = register.Course.CourseName;
                string message = Uri.EscapeDataString($"✔️ تم قبولك في الدورة ({courseName})، يمكنك الآن متابعتها عبر المنصة.");
                string url = $"https://wa.me/{phone}?text={message}";
                return Redirect(url);
            }

            [HttpPost]
            public async Task<IActionResult> RejectParticipant(string traineeId, int courseId)
            {
                var register = await _context.Registers
                    .Include(r => r.Trainee)
                    .Include(r => r.Course)
                    .FirstOrDefaultAsync(r => r.userId == traineeId && r.CourseId == courseId);

                if (register == null || register.Trainee == null || register.Course == null)
                    return NotFound();

                register.RegistrationStatus = RegistrationStatus.Rejected;
                await _context.SaveChangesAsync();

                string phone = register.Trainee.PhoneNum;
                

                string courseName = register.Course.CourseName;
                string message = Uri.EscapeDataString($"❌ نأسف! لم يتم قبولك في الدورة ({courseName})، يمكنك المحاولة مرة أخرى لاحقًا.");
                string url = $"https://wa.me/{phone}?text={message}";
                return Redirect(url);
            }

        }
    }








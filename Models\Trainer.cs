﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity;

namespace Roya.Models
{
    public enum TrainerStatus
    {
        Pending,    // 0 = قيد المراجعة
        Approved,   // 1 = مقبول
        Rejected    // 2 = مرفوض
    }

    public class Trainer : IdentityUser
    {

        [Required]
        public string? FullName { get; set; }

        [Required]
        public string? PhoneNum{ get; set; }
        [Required]
        public string? City { get; set; }
        [Required]
        public int Age { get; set; }

        [Required]
        public string? CV { get; set; }

        [Required]
        public string? ExperienceYears { get; set; }

        public string? Image { get; set; }


        public TrainerStatus Status { get; set; } = TrainerStatus.Pending;


        public List<Course> Courses { get; set; } = new List<Course>();

        // ✅ Navigation Property for Status Logs
        public List<TrainerStatusLog> StatusLogs { get; set; } = new List<TrainerStatusLog>();
    }

}


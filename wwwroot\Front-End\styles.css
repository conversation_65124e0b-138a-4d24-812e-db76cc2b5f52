/* الخطوط والمتغيرات */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

:root {
    --primary-color: #FF5722; /* اللون البرتقالي الأساسي */
    --primary-dark: #E64A19; /* برتقالي داكن */
    --primary-light: #FF8A65; /* برتقالي فاتح */
    --secondary-color: #5A5A5A; /* اللون الرمادي من الشعار */
    --light-color: #FFFFFF;
    --dark-color: #333333;
    --accent-color: #FFB74D; /* برتقالي ذهبي */
    --bg-light: #FFF8F5; /* خلفية برتقالية فاتحة جداً */
    --bg-orange: #FFF3E0; /* خلفية برتقالية فاتحة */
    --border-radius: 12px;
    --box-shadow: 0 4px 15px rgba(255, 87, 34, 0.15);
    --transition: all 0.3s ease;

    /* دعم safe-area للهواتف الحديثة */
    --safe-area-inset-top: env(safe-area-inset-top);
    --safe-area-inset-right: env(safe-area-inset-right);
    --safe-area-inset-bottom: env(safe-area-inset-bottom);
    --safe-area-inset-left: env(safe-area-inset-left);
}

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--bg-light);
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* ناف بار */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 5%;
    background-color: var(--light-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.logo img {
    height: 50px;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-links a {
    font-weight: 500;
    padding: 0.5rem 0;
    position: relative;
}

.nav-links a.active,
.nav-links a:hover {
    color: var(--primary-color);
}

.nav-links a.active::after,
.nav-links a:hover::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
}

.auth-buttons {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.login-btn {
    padding: 0.5rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
    text-decoration: none;
    transition: var(--transition);
}

.login-btn:hover {
    background: var(--primary-color);
    color: white;
}

/* قسم الهيرو المحسن */
.hero {
    min-height: 100vh;
    min-height: calc(100vh - var(--safe-area-inset-top) - var(--safe-area-inset-bottom));
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 25%, #3a4a5c 50%, #2c3e50 75%, #1a252f 100%);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding-top: var(--safe-area-inset-top);
    padding-bottom: var(--safe-area-inset-bottom);
    padding-left: var(--safe-area-inset-left);
    padding-right: var(--safe-area-inset-right);
}

/* إضافة نمط الخلفية المشابه للصورة */
.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(ellipse at top left, rgba(255, 87, 34, 0.15) 0%, transparent 50%),
        radial-gradient(ellipse at bottom right, rgba(255, 87, 34, 0.1) 0%, transparent 50%),
        radial-gradient(ellipse at center, rgba(44, 62, 80, 0.3) 0%, transparent 70%);
    z-index: 1;
}

.hero::after {
    content: '';
    position: absolute;
    top: -20%;
    left: -20%;
    width: 140%;
    height: 140%;
    background:
        radial-gradient(circle at 30% 30%, rgba(255, 87, 34, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 70% 70%, rgba(255, 87, 34, 0.06) 0%, transparent 40%),
        linear-gradient(135deg, transparent 20%, rgba(255, 87, 34, 0.03) 50%, transparent 80%);
    animation: float 25s ease-in-out infinite;
    z-index: 1;
}

/* تحسين الحاوي */
.container {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 30%;
    right: 30%;
    animation-delay: 1s;
}

.shape-5 {
    width: 40px;
    height: 40px;
    top: 10%;
    right: 50%;
    animation-delay: 3s;
}

/* تحسينات الأشكال للشاشات الصغيرة */
@media (max-width: 768px) {
    .shape-1 {
        width: 40px;
        height: 40px;
        top: 15%;
        left: 5%;
    }

    .shape-2 {
        width: 60px;
        height: 60px;
        top: 70%;
        right: 10%;
    }

    .shape-3 {
        width: 30px;
        height: 30px;
        top: 85%;
        left: 15%;
    }

    .shape-4 {
        width: 50px;
        height: 50px;
        top: 25%;
        right: 20%;
    }

    .shape-5 {
        width: 20px;
        height: 20px;
        top: 5%;
        right: 40%;
    }
}

@media (max-width: 480px) {
    .floating-shapes {
        opacity: 0.5;
    }

    .shape-1 {
        width: 25px;
        height: 25px;
        top: 10%;
        left: 5%;
    }

    .shape-2 {
        width: 35px;
        height: 35px;
        top: 75%;
        right: 8%;
    }

    .shape-3 {
        width: 20px;
        height: 20px;
        top: 90%;
        left: 10%;
    }

    .shape-4 {
        width: 30px;
        height: 30px;
        top: 20%;
        right: 15%;
    }

    .shape-5 {
        width: 15px;
        height: 15px;
        top: 5%;
        right: 35%;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="80" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="40" cy="80" r="0.8" fill="rgba(255,255,255,0.05)"/></svg>') repeat;
    opacity: 0.3;
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 3;
    display: flex;
    align-items: center;
    gap: 4rem;
    min-height: 80vh;
}

.hero-logo-section {
    flex: 0 0 300px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.animated-logo {
    position: relative;
    animation: logoFloat 4s ease-in-out infinite;
}

@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0px) scale(1);
    }
    50% {
        transform: translateY(-10px) scale(1.05);
    }
}

.logo-square {
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 20px 40px rgba(255, 87, 34, 0.3);
    position: relative;
    overflow: hidden;
}

.logo-square::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shine 3s linear infinite;
}

@keyframes shine {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.logo-circle {
    width: 120px;
    height: 120px;
    background: white;
    border-radius: 50%;
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-circle::before {
    content: '';
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

.hero-text {
    flex: 1;
    color: white;
}

.hero-title {
    margin-bottom: 1.5rem;
}

.title-main {
    display: block;
    font-size: 4rem;
    font-weight: 800;
    background: linear-gradient(45deg, #ffffff, #ffd700, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow:
        0 2px 10px rgba(0, 0, 0, 0.5),
        0 0 30px rgba(255, 215, 0, 0.3);
    margin-bottom: 0.5rem;
    position: relative;
}

.title-sub {
    display: block;
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--primary-color);
    text-shadow:
        0 2px 8px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(255, 87, 34, 0.4);
    position: relative;
}

.hero-description {
    font-size: 1.4rem;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    font-weight: 500;
}

.hero-quote {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    font-style: italic;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    position: relative;
    padding: 1rem 2rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.hero-quote i {
    color: #ffd700;
    font-size: 1rem;
}

.hero-buttons {
    display: flex;
    gap: 2rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.cta-button {
    display: inline-block;
    padding: 1.5rem 3rem;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.4s ease;
    text-decoration: none;
    border: 3px solid transparent;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
    z-index: 3;
}

.cta-button.primary {
    background: linear-gradient(45deg, #fff, #f8f8f8);
    color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    border-color: transparent;
}

.cta-button.primary:hover {
    background: linear-gradient(45deg, #f8f8f8, #fff);
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
}

.cta-button.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--light-color);
    border-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.cta-button.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--light-color);
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(255, 255, 255, 0.2);
}

/* قسم نبذة عنا */
.about-section {
    padding: 6rem 0;
    background-color: var(--light-color);
}

.about-content {
    display: flex;
    gap: 4rem;
    align-items: center;
}

.about-text {
    flex: 1;
}

.about-text h2 {
    font-size: 2.8rem;
    color: var(--primary-color);
    margin-bottom: 2rem;
    position: relative;
    padding-bottom: 1rem;
}

.about-text h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
}

.about-text p {
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
    line-height: 1.8;
    color: var(--secondary-color);
}

.about-images {
    flex: 1;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.image-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.image-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(255, 87, 34, 0.2);
}

.image-item img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    transition: var(--transition);
}

.image-item:hover img {
    transform: scale(1.05);
}

/* قسم المستهدفون */
.target-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, var(--bg-orange) 0%, var(--bg-light) 100%);
    text-align: center;
}

.target-section h2 {
    font-size: 2.8rem;
    color: var(--primary-color);
    margin-bottom: 4rem;
    position: relative;
    display: inline-block;
    padding-bottom: 1rem;
}

.target-section h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
}

.target-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 4rem;
}

.target-card {
    background-color: var(--light-color);
    padding: 3rem 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.target-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.target-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(255, 87, 34, 0.2);
}

.target-card .icon {
    font-size: 3.5rem;
    color: var(--primary-color);
    margin-bottom: 2rem;
    transition: var(--transition);
}

.target-card:hover .icon {
    transform: scale(1.1);
    color: var(--primary-dark);
}

.target-card h3 {
    font-size: 1.3rem;
    color: var(--secondary-color);
    line-height: 1.5;
    background: transparent;
    padding: 0;
    margin: 0;
}

.target-card .subtitle {
    font-size: 0.9rem;
    color: var(--primary-color);
    font-weight: 500;
    background: transparent;
    padding: 0;
    margin-top: 0.5rem;
}

.target-quote {
    max-width: 900px;
    margin: 2rem auto;
    font-size: 1.3rem;
    line-height: 1.9;
    color: var(--secondary-color);
    background: transparent;
    padding: 2rem 1rem;
    text-align: center;
    position: relative;
}

.target-quote::before {
    content: '"';
    position: absolute;
    top: -10px;
    right: 20px;
    font-size: 4rem;
    color: var(--primary-color);
    font-family: serif;
}

/* قسم أحدث الدورات */
.latest-courses {
    padding: 8rem 0;
    background: linear-gradient(135deg, var(--light-color) 0%, #fafafa 100%);
    position: relative;
}

.latest-courses::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="10" cy="10" r="1" fill="rgba(255,87,34,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,87,34,0.05)"/><circle cx="30" cy="90" r="0.8" fill="rgba(255,87,34,0.05)"/></svg>') repeat;
    opacity: 0.5;
}

.latest-courses h2 {
    font-size: 3.2rem;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 5rem;
    position: relative;
    display: inline-block;
    width: 100%;
    padding-bottom: 1.5rem;
    font-weight: 800;
    z-index: 2;
}

.latest-courses h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 5px;
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    border-radius: 3px;
    box-shadow: 0 2px 10px rgba(255, 87, 34, 0.3);
}

.courses-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-bottom: 5rem;
    position: relative;
    z-index: 2;
}

.course-card {
    background-color: var(--light-color);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid rgba(255, 87, 34, 0.05);
    position: relative;
    cursor: pointer;
}

.course-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 25px 50px rgba(255, 87, 34, 0.2);
    border-color: var(--primary-color);
}

.course-image {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.course-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.course-card:hover .course-image img {
    transform: scale(1.1);
}

.course-card:hover .course-category {
    background: linear-gradient(135deg, #e64a19, var(--primary-color));
    transform: scale(1.1);
}

.course-card:hover .course-date {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.05);
}

.course-card:hover .course-details h3 {
    color: var(--primary-color);
    transform: translateY(-2px);
}

.course-card:hover .course-start-date {
    background: linear-gradient(135deg, rgba(255, 87, 34, 0.15), rgba(255, 107, 53, 0.15));
    border-left-color: #e64a19;
    transform: translateX(5px);
}

.course-card:hover .course-meta-info {
    background-color: rgba(255, 87, 34, 0.05);
    transform: translateY(-1px);
}

.course-details {
    padding: 1.8rem;
    position: relative;
}

.course-details h3 {
    font-size: 1.4rem;
    margin-bottom: 1.2rem;
    color: var(--secondary-color);
    font-weight: 700;
    line-height: 1.3;
    text-align: center;
}

.course-meta-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.8rem;
    background-color: var(--bg-light);
    border-radius: 12px;
}

.course-instructor {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--secondary-color);
}

.course-instructor i {
    color: var(--primary-color);
}

.course-duration {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.85rem;
    color: var(--primary-color);
    background-color: rgba(255, 87, 34, 0.1);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-weight: 600;
}

.course-duration i {
    font-size: 0.8rem;
}

.description {
    margin: 1.2rem 0;
    font-size: 0.95rem;
    line-height: 1.5;
    color: var(--dark-color);
    text-align: center;
    height: 3rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.course-start-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 1rem 0;
    padding: 0.8rem;
    background: linear-gradient(135deg, rgba(255, 87, 34, 0.1), rgba(255, 107, 53, 0.1));
    border-radius: 10px;
    border-left: 4px solid var(--primary-color);
}

.course-start-date i {
    color: var(--primary-color);
    font-size: 1rem;
}

.course-start-date span {
    font-size: 0.9rem;
    color: var(--secondary-color);
    font-weight: 600;
}

.course-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 1.2rem;
    margin-top: auto;
}

.course-category {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 2;
}

.course-date {
    position: absolute;
    bottom: 15px;
    left: 15px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 500;
    z-index: 2;
}

.enroll-btn {
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    color: white;
    padding: 0.8rem 2rem;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    text-decoration: none;
    display: inline-block;
    font-size: 0.9rem;
    width: 100%;
    text-align: center;
}

.enroll-btn:hover {
    background: linear-gradient(135deg, #e64a19, var(--primary-color));
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 25px rgba(255, 87, 34, 0.4);
}

.course-card:hover .enroll-btn {
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 20px rgba(255, 87, 34, 0.3);
}

.view-all {
    text-align: center;
}

.view-all-btn {
    display: inline-block;
    padding: 1.2rem 3rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--light-color);
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 1.1rem;
    transition: var(--transition);
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(255, 87, 34, 0.3);
}

.view-all-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 87, 34, 0.4);
}

/* قسم الإحصائيات المحسن */
.enhanced-stats-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, var(--primary-color) 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.enhanced-stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.stats-header {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
    z-index: 2;
}

.stats-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ffffff, #ffd700);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stats-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.enhanced-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 3rem;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.enhanced-stat-card {
    text-align: center;
    padding: 3rem 2rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.enhanced-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.enhanced-stat-card:hover::before {
    left: 100%;
}

.enhanced-stat-card:hover {
    transform: translateY(-15px) scale(1.05);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.stat-icon-wrapper {
    margin-bottom: 2rem;
}

.enhanced-stat-card .stat-icon {
    font-size: 4rem;
    margin-bottom: 0;
    color: #ffd700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    transition: all 0.3s ease;
}

.enhanced-stat-card:hover .stat-icon {
    transform: scale(1.2) rotate(10deg);
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
}

.animated-number {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    color: white;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.enhanced-stat-card:hover .animated-number {
    transform: scale(1.1);
    color: #ffd700;
}

.enhanced-stat-card .stat-label {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: white;
}

.stat-description {
    font-size: 0.95rem;
    opacity: 0.8;
    color: rgba(255, 255, 255, 0.9);
    font-style: italic;
}

/* الفاصل التصميمي */
.section-divider {
    padding: 3rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.divider-content {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 800px;
    margin: 0 auto;
}

.divider-line {
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    opacity: 0.3;
}

.divider-icon {
    margin: 0 2rem;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 10px 30px rgba(255, 87, 34, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 10px 30px rgba(255, 87, 34, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 15px 40px rgba(255, 87, 34, 0.5);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 10px 30px rgba(255, 87, 34, 0.3);
    }
}

/* شاشة التحميل */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.loading-progress {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin: 0 auto;
    overflow: hidden;
}

.loading-bar {
    height: 100%;
    background: white;
    border-radius: 2px;
    animation: loading 2s ease-in-out;
}

@keyframes loading {
    0% { width: 0%; }
    100% { width: 100%; }
}

/* تأثيرات الظهور */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    animation: slideInLeft 0.8s ease forwards;
}

@keyframes slideInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    animation: slideInRight 0.8s ease forwards;
}

@keyframes slideInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تأثير الكتابة */
.typewriter {
    overflow: hidden;
    border-right: 2px solid white;
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: white; }
}

/* نظام الإشعارات Toast */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    background: white;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-left: 4px solid var(--primary-color);
    display: flex;
    align-items: center;
    gap: 1rem;
    min-width: 300px;
    transform: translateX(400px);
    transition: all 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left-color: #4caf50;
}

.toast.error {
    border-left-color: #f44336;
}

.toast.warning {
    border-left-color: #ff9800;
}

.toast.info {
    border-left-color: #2196f3;
}

.toast-icon {
    font-size: 1.5rem;
}

.toast.success .toast-icon {
    color: #4caf50;
}

.toast.error .toast-icon {
    color: #f44336;
}

.toast.warning .toast-icon {
    color: #ff9800;
}

.toast.info .toast-icon {
    color: #2196f3;
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--secondary-color);
}

.toast-message {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

.toast-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toast-close:hover {
    color: #666;
}

/* تأثيرات إضافية للأزرار */
.btn-glow {
    position: relative;
    overflow: hidden;
}

.btn-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.btn-glow:hover::before {
    left: 100%;
}

/* تحسينات الأداء */
.course-card,
.target-card,
.stat-card {
    will-change: transform;
}

/* تأثير الموجة عند النقر */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::after {
    width: 300px;
    height: 300px;
}

/* صفحة جميع الدورات */
.page-header {
    background: linear-gradient(135deg, var(--light-color) 0%, #f8f9fa 100%);
    color: var(--secondary-color);
    padding: 4rem 0;
    text-align: center;
    border-bottom: 1px solid rgba(255, 87, 34, 0.1);
}

.page-header h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.page-header p {
    font-size: 1.3rem;
    opacity: 0.9;
}

/* فلاتر البحث */
.filters-section {
    padding: 3rem 0;
    background-color: var(--bg-light);
}

.filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.search-box {
    display: flex;
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    min-width: 300px;
}

.search-box input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    font-size: 1rem;
    font-family: 'Tajawal', sans-serif;
}

.search-box input:focus {
    outline: none;
}

.search-box button {
    padding: 1rem 1.5rem;
    background-color: var(--primary-color);
    color: var(--light-color);
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.search-box button:hover {
    background-color: var(--primary-dark);
}

.filter-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.8rem 1.5rem;
    background-color: var(--light-color);
    color: var(--secondary-color);
    border: 2px solid var(--bg-light);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-family: 'Tajawal', sans-serif;
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--primary-color);
    color: var(--light-color);
    border-color: var(--primary-color);
}

/* شبكة الدورات */
.all-courses {
    padding: 4rem 0;
    background-color: var(--light-color);
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
}

/* شارة الدورة */
.course-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: var(--primary-color);
    color: var(--light-color);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.course-badge.popular {
    background-color: var(--accent-color);
}

/* الفوتر */
footer {
    background-color: var(--secondary-color);
    color: var(--light-color);
    padding: 3rem 10% 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo img {
    height: 60px;
    margin-bottom: 1rem;
}

.footer-links h3, .footer-contact h3, .footer-social h3 {
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.footer-links ul li {
    margin-bottom: 0.5rem;
}

.footer-contact p {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-icons a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s;
}

.social-icons a:hover {
    background-color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}



/* تصميم متجاوب */
@media (max-width: 1024px) {
    .target-cards {
        grid-template-columns: repeat(2, 1fr);
    }

    .courses-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        padding: 1rem;
    }

    .nav-links {
        margin: 1rem 0;
    }

    .hero {
        height: 90vh;
        padding: 2rem 0;
    }

    .about-content {
        flex-direction: column;
    }

    .hero-title {
        font-size: 2.8rem;
    }

    .hero-subtitle {
        font-size: 2.2rem;
    }

    .hero-quote {
        font-size: 1.3rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }

    .cta-button {
        width: 100%;
        max-width: 280px;
        padding: 1.2rem 2rem;
    }

    .target-cards {
        grid-template-columns: 1fr;
    }

    .courses-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .course-card:hover {
        transform: translateY(-5px) scale(1.02);
    }

    .target-quote {
        font-size: 1.1rem;
        padding: 1.5rem 0.5rem;
    }

    .latest-courses {
        padding: 5rem 0;
    }

    .latest-courses h2 {
        font-size: 2.5rem;
    }

    .filters {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    .filter-buttons {
        justify-content: center;
    }

    .courses-grid {
        grid-template-columns: 1fr;
    }

    .page-header h1 {
        font-size: 2.2rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2.2rem;
    }

    .hero-subtitle {
        font-size: 1.8rem;
    }

    .hero-quote {
        font-size: 1.1rem;
    }

    .cta-button {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    .course-card {
        margin: 0 0.5rem;
    }

    .latest-courses h2 {
        font-size: 2rem;
    }

    /* تحسينات responsive للإحصائيات المحسنة */
    .enhanced-stats-section {
        padding: 3rem 0;
    }

    .stats-header h2 {
        font-size: 1.8rem;
    }

    .stats-header p {
        font-size: 0.95rem;
        padding: 0 1rem;
    }

    .enhanced-stats-grid {
        gap: 1.5rem;
        padding: 0 1rem;
    }

    .enhanced-stat-card {
        padding: 1.5rem 1rem;
    }

    .enhanced-stat-card .stat-icon {
        font-size: 2.5rem;
    }

    .animated-number {
        font-size: 2rem;
    }

    .enhanced-stat-card .stat-label {
        font-size: 1rem;
    }

    .stat-description {
        font-size: 0.85rem;
    }

    .divider-icon {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
        margin: 0 1rem;
    }
}

/* تحسينات إضافية للشاشات الكبيرة */
@media (min-width: 768px) {
    .enhanced-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .enhanced-stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* إضافات للهيرو الجديد */


/* تحسينات responsive للهيرو الجديد */
@media (max-width: 1024px) {
    .hero {
        min-height: 100vh;
        padding: 2rem 0;
    }

    .hero-content {
        flex-direction: column;
        gap: 3rem;
        text-align: center;
        min-height: auto;
        padding: 2rem 0;
    }

    .hero-logo-section {
        flex: none;
        margin-top: 2rem;
    }

    .logo-square {
        width: 150px;
        height: 150px;
    }

    .logo-circle {
        width: 90px;
        height: 90px;
    }

    .logo-circle::before {
        width: 45px;
        height: 45px;
    }

    .title-main {
        font-size: 3rem;
    }

    .title-sub {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1.2rem;
    }


}

@media (max-width: 768px) {
    .hero {
        min-height: 100vh;
        padding: 4rem 0 2rem;
        display: flex;
        align-items: center;
    }

    .hero-content {
        gap: 2rem;
        padding: 0;
        justify-content: center;
        min-height: auto;
    }

    .hero-logo-section {
        margin-top: 0;
        order: 1;
    }

    .hero-text {
        order: 2;
    }

    .logo-square {
        width: 140px;
        height: 140px;
    }

    .logo-circle {
        width: 85px;
        height: 85px;
    }

    .logo-circle::before {
        width: 42px;
        height: 42px;
    }

    .title-main {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .title-sub {
        font-size: 1.8rem;
        line-height: 1.3;
    }

    .hero-description {
        font-size: 1.1rem;
        padding: 0 1rem;
    }

    .hero-quote {
        font-size: 1rem;
        padding: 0.8rem 1.5rem;
        margin: 0 1rem 2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
        padding: 0 1rem;
    }

    .hero-buttons .cta-button {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }


}

@media (max-width: 480px) {
    .hero {
        min-height: 100vh;
        min-height: calc(var(--vh, 1vh) * 100); /* ارتفاع ديناميكي */
        min-height: 100dvh; /* للمتصفحات الحديثة */
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
    }

    .hero-content {
        gap: 1rem;
        padding: 1rem;
        width: 100%;
        max-width: 100%;
        min-height: auto;
        justify-content: center;
        align-items: center;
    }

    .hero-logo-section {
        margin: 0;
        flex-shrink: 0;
    }

    .logo-square {
        width: 100px;
        height: 100px;
    }

    .logo-circle {
        width: 60px;
        height: 60px;
    }

    .logo-circle::before {
        width: 30px;
        height: 30px;
    }

    .hero-text {
        width: 100%;
        text-align: center;
    }

    .title-main {
        font-size: 1.8rem;
        line-height: 1.1;
        margin-bottom: 0.3rem;
    }

    .title-sub {
        font-size: 1.3rem;
        line-height: 1.2;
    }

    .hero-description {
        font-size: 0.9rem;
        padding: 0;
        margin-bottom: 1rem;
        line-height: 1.5;
    }

    .hero-quote {
        font-size: 0.8rem;
        padding: 0.5rem 1rem;
        margin: 0 0 1rem 0;
        line-height: 1.4;
    }

    .hero-buttons {
        padding: 0;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .hero-buttons .cta-button {
        padding: 0.8rem 1.2rem;
        font-size: 0.9rem;
        min-height: 44px; /* للمس السهل */
    }


}

/* تحسينات إضافية للشاشات الصغيرة جداً */
@media (max-width: 360px) {
    .hero-content {
        padding: 0.5rem;
        gap: 0.8rem;
    }

    .logo-square {
        width: 80px;
        height: 80px;
    }

    .logo-circle {
        width: 48px;
        height: 48px;
    }

    .logo-circle::before {
        width: 24px;
        height: 24px;
    }

    .title-main {
        font-size: 1.5rem;
    }

    .title-sub {
        font-size: 1.1rem;
    }

    .hero-description {
        font-size: 0.8rem;
    }

    .hero-quote {
        font-size: 0.7rem;
        padding: 0.4rem 0.8rem;
    }

    .hero-buttons .cta-button {
        padding: 0.7rem 1rem;
        font-size: 0.8rem;
    }


}

/* تحسينات للشاشات القصيرة (landscape على الهواتف) */
@media (max-height: 600px) and (max-width: 768px) {
    .hero {
        min-height: 100vh;
        padding: 1rem 0;
    }

    .hero-content {
        gap: 0.5rem;
        padding: 0.5rem;
    }

    .hero-logo-section {
        order: 1;
    }

    .hero-text {
        order: 2;
    }

    .logo-square {
        width: 60px;
        height: 60px;
    }

    .logo-circle {
        width: 36px;
        height: 36px;
    }

    .logo-circle::before {
        width: 18px;
        height: 18px;
    }

    .title-main {
        font-size: 1.5rem;
        margin-bottom: 0.2rem;
    }

    .title-sub {
        font-size: 1.2rem;
    }

    .hero-description {
        font-size: 0.8rem;
        margin-bottom: 0.5rem;
    }

    .hero-quote {
        font-size: 0.7rem;
        padding: 0.3rem 0.8rem;
        margin-bottom: 0.5rem;
    }

    .hero-buttons {
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .hero-buttons .cta-button {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }


}

/* زر المنيو للشاشات الصغيرة */
.mobile-menu-btn {
    display: none;
    flex-direction: column;
    justify-content: space-around;
    width: 30px;
    height: 30px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 1001;
    transition: all 0.3s ease;
}

.hamburger-line {
    width: 100%;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
    transition: all 0.3s ease;
    transform-origin: center;
}

.mobile-menu-btn.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-btn.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-btn.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* تحسينات responsive للناف بار */
@media (max-width: 768px) {
    .navbar {
        background: transparent;
        backdrop-filter: none;
        box-shadow: none;
        padding: 0;
        justify-content: flex-end;
        position: fixed;
        top: 1rem;
        right: 1rem;
        left: auto;
        width: auto;
        height: auto;
    }

    .logo {
        display: none;
    }

    .auth-buttons {
        display: none !important;
        visibility: hidden !important;
    }

    .mobile-menu-btn {
        display: flex;
        position: relative;
        top: 0;
        right: 0;
        z-index: 1002;
        background: rgba(255, 87, 34, 0.9);
        border-radius: 8px;
        padding: 0.5rem;
        box-shadow: 0 4px 15px rgba(255, 87, 34, 0.3);
        backdrop-filter: blur(10px);
    }

    .hamburger-line {
        background: white;
    }

    .nav-links {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(15px);
        flex-direction: column;
        padding: 4rem 2rem 4rem;
        gap: 1.5rem;
        box-shadow: none;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        justify-content: center;
        align-items: center;
    }

    .nav-links.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-links a {
        padding: 1rem;
        text-align: center;
        border-radius: 10px;
        background: rgba(255, 87, 34, 0.05);
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .nav-links a:hover,
    .nav-links a.active {
        background: rgba(255, 87, 34, 0.1);
        transform: translateY(-2px);
    }

    .nav-links a.active::after,
    .nav-links a:hover::after {
        display: none;
    }




}

@media (max-width: 480px) {
    .navbar {
        padding: 1rem;
    }

    .logo img {
        height: 40px;
    }

    .mobile-menu-btn {
        width: 25px;
        height: 25px;
    }

    .hamburger-line {
        height: 2px;
    }

    .nav-links {
        top: 60px;
        padding: 1.5rem;
    }


}


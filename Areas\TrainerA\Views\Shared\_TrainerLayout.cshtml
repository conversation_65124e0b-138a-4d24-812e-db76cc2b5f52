﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
  
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>@ViewData["Title"] - Roya</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
     
        <meta name="description" content="" />
        <meta name="author" content="" />
        <title>Dashboard - SB Admin</title>
        <link href="https://cdn.jsdelivr.net/npm/simple-datatables@7.1.2/dist/style.min.css" rel="stylesheet" />
        <link href="~/css/styles.css" rel="stylesheet" />
        <script src="https://use.fontawesome.com/releases/v6.3.0/js/all.js" crossorigin="anonymous"></script>
    
</head>
<body class="sb-nav-fixed">

    <nav class="sb-topnav navbar navbar-expand navbar-dark bg-dark">
        <!-- Navbar Brand-->
        <a class="navbar-brand ps-3" asp-area="TrainerA" asp-controller="Home" asp-action="Index">Roya</a>

        <!-- Sidebar Toggle-->
        <button class="btn btn-link btn-sm order-1 order-lg-0 me-4 me-lg-0" id="sidebarToggle" href="#!"><i class="fas fa-bars"></i></button>
       
        <!-- Navbar Search-->
        <form class="d-none d-md-inline-block form-inline ms-auto me-0 me-md-3 my-2 my-md-0">
            <div class="input-group">
                <input class="form-control" type="text" placeholder="Search for..." aria-label="Search for..." aria-describedby="btnNavbarSearch" />
                <button class="btn btn-primary" id="btnNavbarSearch" type="button"><i class="fas fa-search"></i></button>
            </div>
        </form>

        <!-- Navbar-->
     @*    <ul class="navbar-nav ms-auto ms-md-0 me-3 me-lg-4">
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" id="navbarDropdown" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false"><i class="fas fa-user fa-fw"></i></a>
               
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                    <li><a class="dropdown-item" asp-area="Trainer" asp-controller="Home" asp-action="Index">Settings</a></li>
                    <li><hr class="dropdown-divider" /></li>
                    <li><a class="dropdown-item" asp-area="Trainer" asp-controller="Home" asp-action="Index">Logout</a></li>
                </ul>
            </li>
        </ul> *@
        <p class="navbar-nav ms-auto ms-md-0 me-3 me-lg-4">
            <a class="btn btn-info" asp-area="" asp-controller="Account" asp-action="Logout">Logout</a>
        </p>
    </nav>


    <div id="layoutSidenav">
        <div id="layoutSidenav_nav">
            <nav class="sb-sidenav accordion sb-sidenav-dark" id="sidenavAccordion">
                <div class="sb-sidenav-menu">
                    <div class="nav">
                        <div class="sb-sidenav-menu-heading">Core</div>
                        <a class="nav-link" asp-area="TrainerA" asp-controller="Home" asp-action="Index">
                            <div class="sb-nav-link-icon"><i class="fas fa-home"></i></div>
                            الرئيسية
                        </a>


                        <div class="sb-sidenav-menu-heading">إدارة</div>
                        <!-- إدارة المدربين -->
                        <a class="nav-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#collapseLayouts" aria-expanded="false" aria-controls="collapseLayouts">
                            <div class="sb-nav-link-icon"><i class="fas fa-columns"></i></div>
                            إدارة الدورات
                            <div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                        </a>

                        <div class="collapse" id="collapseLayouts" aria-labelledby="headingOne" data-bs-parent="#sidenavAccordion">
                            <nav class="sb-sidenav-menu-nested nav">
                                <a class="nav-link" asp-area="TrainerA" asp-controller="Course" asp-action="Create">اضافة دورة </a>
                                <a class="nav-link" asp-area="TrainerA" asp-controller="Course" asp-action="MyCourses">دوراتي </a>
                            </nav>
                        </div>

                        <!-- إدارة الدورات -->

                        <a class="nav-link" asp-area="TrainerA" asp-controller="TrainerCourses" asp-action="CoursesWithParticipants">
                            <div class="sb-nav-link-icon"><i class="fas fa-home"></i></div>
                            دورات المقبولة
                        </a>
                      

                        <div class="sb-sidenav-menu-heading">Addons</div>
                        <a class="nav-link" asp-area="TrainerA" asp-controller="Profile" asp-action="Edit">
                            <div class="sb-nav-link-icon"><i class="fas fa-chart-area"></i></div>
                            معلومات الشخصية
                        </a>
                      
                    </div>
                </div>
                <div class="sb-sidenav-footer">
                    <div class="small">Logged in as:</div>
                   Trainer
                </div>
            </nav>
        </div>







    <div id="layoutSidenav_content">
        <main>

            @RenderBody()
        </main>



        <footer class="py-4 bg-light mt-auto">
            <div class="container-fluid px-4">
                <div class="d-flex align-items-center justify-content-between small">
                    <div class="text-muted">Copyright &copy; Your Website 2023</div>
                    <div>
                        <a href="#">Privacy Policy</a>
                        &middot;
                        <a href="#">Terms &amp; Conditions</a>
                    </div>
                </div>
            </div>
        </footer>
    </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
    <script src="~/js/scripts.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.min.js" crossorigin="anonymous"></script>
    <script src="~/assets/demo/chart-area-demo.js"></script>
    <script src="~/assets/demo/chart-bar-demo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/simple-datatables@7.1.2/dist/umd/simple-datatables.min.js" crossorigin="anonymous"></script>
    <script src="~/js/datatables-simple-demo.js"></script>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>

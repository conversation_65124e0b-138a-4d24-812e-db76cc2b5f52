﻿@model List<TrainerStatusLog>

@{
    ViewData["Title"] = "سجل تغييرات حالة المدربين";
}

<div class="container mt-5">
    <h2 class="text-center mb-4">📋 سجل تغييرات حالة المدربين</h2>

    @if (!Model.Any())
    {
        <div class="alert alert-warning text-center">لا يوجد أي تغييرات حالة مسجلة.</div>
    }
    else
    {
        <div class="table-responsive shadow-sm rounded">
            <table class="table table-bordered table-hover text-center align-middle">
                <thead class="table-light">
                    <tr>
                        <th>اسم المدرب</th>
                        <th>الحالة السابقة</th>
                        <th>الحالة الجديدة</th>
                        <th>وقت التغيير</th>
                        <th>من غيّر الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var log in Model)
                    {
                        <tr>
                            <td>@log.Trainer?.FullName</td>
                            <td>@GetStatusArabic(log.OldStatus)</td>
                            <td>@GetStatusArabic(log.NewStatus)</td>
                            <td>@log.ChangeTime.ToString("yyyy-MM-dd HH:mm")</td>
                            <td>@log.ChangedBy</td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
</div>

@functions {
    string GetStatusArabic(TrainerStatus status)
    {
        return status switch
        {
            TrainerStatus.Pending => "قيد المراجعة",
            TrainerStatus.Approved => "مقبول",
            TrainerStatus.Rejected => "مرفوض",
            _ => ""
        };
    }
}

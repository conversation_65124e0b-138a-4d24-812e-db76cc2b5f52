﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Roya.Data;
using Roya.Models;
using Roya.Utilities;
using Roya.ViewModels;

namespace Roya.Controllers
{
    public class AccountController : Controller
    {
        private readonly UserManager<IdentityUser> _userManager;
        private readonly SignInManager<IdentityUser> _signInManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private AppDbContext _context;
        //private readonly IEmailSender _emailSender;
        public AccountController(UserManager<IdentityUser> userManager,
                                 SignInManager<IdentityUser> signInManager,
                                 RoleManager<IdentityRole> roleManager,
                                 IWebHostEnvironment webHostEnvironment,
                                 AppDbContext context)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _roleManager = roleManager;
            _webHostEnvironment = webHostEnvironment;
            _context = context;
            //_emailSender = emailSender;
        }

        public IActionResult RegisterTrainer() => View();


        // POST: Register Trainer
        [HttpPost]
        public async Task<IActionResult> RegisterTrainer(TrainerRegisterViewModel model)
        {
            if (!ModelState.IsValid)
                return View(model);

            // ✅ التحقق من وجود بريد إلكتروني مسجل مسبقاً
            var existingUser = await _userManager.FindByEmailAsync(model.Email);
            if (existingUser != null)
            {
                ModelState.AddModelError("Email", "هذا البريد الإلكتروني مستخدم بالفعل.");
                return View(model);
            }

            // ✅ حفظ الملفات (السيرة الذاتية والصورة)
            string cvPath = await SaveFile(model.CVFile, "CVs");
            string? imgPath = model.ImageFile != null ? await SaveFile(model.ImageFile, "TrainerImages") : null;
            
            model.PhoneNum = "+2189" + model.PhoneNum;
            // ✅ إنشاء كائن المدرب وتحديد حالته كـ "قيد المراجعة"
            var trainer = new Trainer
            {
                UserName = model.Email,
                Email = model.Email,
                FullName = model.FullName,
                PhoneNum = model.PhoneNum,
                City = model.City,
                Age = model.Age,
                CV = cvPath,
                ExperienceYears = model.ExperienceYears,
                Image = imgPath,
                Status = TrainerStatus.Pending // ⏳ قيد المراجعة
            };

            var result = await _userManager.CreateAsync(trainer, model.Password);
            if (result.Succeeded)
            {
                await _userManager.AddToRoleAsync(trainer, RoleEnum.Trainer.ToString());

                return RedirectToAction("Login");
            }

            // ✅ إظهار الأخطاء في حالة فشل التسجيل
            foreach (var error in result.Errors)
                ModelState.AddModelError("", error.Description);

            return View(model);
        }



        // GET: Register Trainee
        public IActionResult RegisterTrainee() => View();

        // POST: Register Trainee
        [HttpPost]
        public async Task<IActionResult> RegisterTrainee(TraineeRegisterViewModel model)
        {
            if (!ModelState.IsValid) return View(model);

            model.PhoneNum = "+2189" + model.PhoneNum;
            var trainee = new Trainee
            {
                UserName = model.Email,
                Email = model.Email,
                FullName = model.FullName,
                PhoneNum = model.PhoneNum,
                City = model.City,
                Age = model.Age,
                MajorOrProfession = model.MajorOrProfession
            };

            var result = await _userManager.CreateAsync(trainee, model.Password);
            if (result.Succeeded)
            {
                await _userManager.AddToRoleAsync(trainee, RoleEnum.Trainee.ToString());
                return RedirectToAction("Login");
            }

            foreach (var error in result.Errors)
                ModelState.AddModelError("", error.Description);

            return View(model);
        }


        private async Task<string> SaveFile(IFormFile file, string folder)
        {
            string uploads = Path.Combine(_webHostEnvironment.WebRootPath, "uploads", folder);
            Directory.CreateDirectory(uploads);
            string fileName = Guid.NewGuid().ToString() + Path.GetExtension(file.FileName);
            string filePath = Path.Combine(uploads, fileName);
            using (var fs = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(fs);
            }
            return $"/uploads/{folder}/{fileName}";
        }
        public IActionResult Login()
        {
            return View();
        }
        [HttpPost]
        public async Task<IActionResult> Login(LoginViewModel model)
        {
            if (!ModelState.IsValid)
                return View(model);

            var user = await _userManager.FindByEmailAsync(model.Email);
            if (user == null)
            {
                ModelState.AddModelError("", "بيانات تسجيل الدخول غير صحيحة.");
                return View(model);
            }

            var result = await _signInManager.PasswordSignInAsync(user, model.Password, false, false);

            if (result.Succeeded)
            {
                var roles = await _userManager.GetRolesAsync(user);

                if (roles.Contains(RoleEnum.Admin.ToString()))
                    return RedirectToAction("Index", new { area = "Admin", controller = "Home" });

                if (roles.Contains(RoleEnum.Trainer.ToString()))
                {
                    var trainer = await _context.Trainers.FindAsync(user.Id);
                    if (trainer == null)
                    {
                        await _signInManager.SignOutAsync();
                        ModelState.AddModelError("", "حدث خطأ. لم يتم العثور على الحساب.");
                        return View(model);
                    }

                    // ✅ التحقق من حالة المدرب
                    if (trainer.Status == TrainerStatus.Pending)
                    {
                        await _signInManager.SignOutAsync();
                        ModelState.AddModelError("", "حسابك قيد المراجعة. يرجى الانتظار حتى يتم الموافقة.");
                        return View(model);
                    }

                    if (trainer.Status == TrainerStatus.Rejected)
                    {
                        await _signInManager.SignOutAsync();
                        ModelState.AddModelError("", "تم رفض طلبك. لا يمكنك تسجيل الدخول.");
                        return View(model);
                    }

                    return RedirectToAction("Index", new { area = "TrainerA", controller = "Home" });
                }

                if (roles.Contains(RoleEnum.Trainee.ToString()))
                    return RedirectToAction("Index", "Home");

                // إذا لم يكن له دور معروف
                return RedirectToAction("Index", "Home");
            }

            ModelState.AddModelError("", "كلمة المرور غير صحيحة.");
            return View(model);
        }


        public IActionResult ChangePassword()
        {
            return View();
        }



        [HttpPost]
        [Authorize]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ChangePassword(ChangePasswordViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return NotFound($"Unable to load user with ID '{_userManager.GetUserId(User)}'.");
            }

            var changePasswordResult = await _userManager.ChangePasswordAsync(user, model.CurrentPassword, model.NewPassword);

            if (changePasswordResult.Succeeded)
            {
                await _signInManager.RefreshSignInAsync(user); // Keep the user signed in after password change
                TempData["SuccessMessage"] = "Your password has been changed successfully.";
                return RedirectToAction(nameof(ChangePasswordConfirmation));
            }

            foreach (var error in changePasswordResult.Errors)
            {
                ModelState.AddModelError(string.Empty, error.Description);
            }

            return View(model);
        }

        [HttpGet]
        [Authorize]
        public IActionResult ChangePasswordConfirmation()
        {
            return View();
        }

        public async Task<IActionResult> Logout()
        {
            await _signInManager.SignOutAsync();
            return RedirectToAction("Index", "Home");
        }


    }
}



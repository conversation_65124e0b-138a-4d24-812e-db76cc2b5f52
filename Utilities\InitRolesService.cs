﻿using Microsoft.AspNetCore.Identity;
using System;
using Microsoft.EntityFrameworkCore.Metadata.Internal;

using Roya.Data;

namespace Roya.Utilities
{
    public class InitRolesService
    {

        
        public async Task<bool> Run(WebApplication app)
        {

            using (var scope = app.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                try
                {
                    var context = services.GetRequiredService<AppDbContext>();

                    var userManager = services.GetRequiredService<UserManager<IdentityUser>>();
                    var roleManager = services.GetRequiredService<RoleManager<IdentityRole>>();

                    // Create roles if they don't exist
                    if (!await roleManager.RoleExistsAsync(RoleEnum.Admin.ToString()))
                    {
                        await roleManager.CreateAsync(new IdentityRole(RoleEnum.Admin.ToString()));
                    }
                    if (!await roleManager.RoleExistsAsync(RoleEnum.Trainer.ToString()))
                    {
                        await roleManager.CreateAsync(new IdentityRole(RoleEnum.Trainer.ToString()));
                    }
                    if (!await roleManager.RoleExistsAsync(RoleEnum.Trainee.ToString()))
                    {
                        await roleManager.CreateAsync(new IdentityRole(RoleEnum.Trainee.ToString()));
                    }

                    // Seed the admin user
                    if (await userManager.FindByEmailAsync("<EMAIL>") == null)
                    {
                        var user = new IdentityUser { UserName = "admin", Email = "<EMAIL>" };
                        var result = await userManager.CreateAsync(user, "P@$$w0rd");  //  Set an initial password
                        if (result.Succeeded)
                        {
                            await userManager.AddToRoleAsync(user, RoleEnum.Admin.ToString()); // Assign the Admin role
                        }
                    }
                }
                catch (Exception ex)
                {
                    var logger = services.GetRequiredService<ILogger<Program>>();
                    logger.LogError(ex, "An error occurred while seeding the database.");
                }
            }
            return true;

        }
    }
}

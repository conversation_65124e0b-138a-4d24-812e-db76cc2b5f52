<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلاب الدورة - رؤية 2030</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="admin-dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- الشريط الجانبي -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="logo.png" alt="رؤية 2030">
                    <h3>لوحة التحكم</h3>
                </div>
                <button class="sidebar-toggle-btn" id="sidebarToggle" title="تصغير/توسيع القائمة">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item">
                        <a href="admin-dashboard.html" class="nav-link">
                            <i class="fas fa-home"></i>
                            <span>الصفحة الرئيسية</span>
                        </a>
                    </li>
                    
                    <li class="nav-item has-submenu">
                        <a href="#" class="nav-link">
                            <i class="fas fa-chalkboard-teacher"></i>
                            <span>إدارة المدربين</span>
                            <i class="fas fa-chevron-down submenu-arrow"></i>
                        </a>
                        <ul class="submenu">
                            <li><a href="trainer-requests.html">طلبات المدربين</a></li>
                            <li><a href="all-trainers.html">جميع المدربين</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item has-submenu open">
                        <a href="#" class="nav-link">
                            <i class="fas fa-book"></i>
                            <span>إدارة الدورات</span>
                            <i class="fas fa-chevron-down submenu-arrow"></i>
                        </a>
                        <ul class="submenu">
                            <li><a href="course-requests.html">طلبات الدورات</a></li>
                            <li><a href="all-courses.html">جميع الدورات</a></li>
                            <li><a href="approved-courses.html">الدورات المقبولة</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item">
                        <a href="settings.html" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>تسجيل الخروج</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- الهيدر العلوي -->
            <header class="main-header">
                <div class="header-content">
                    <div class="header-left">
                        <button class="btn btn-secondary" onclick="goBack()">
                            <i class="fas fa-arrow-right"></i>
                            رجوع
                        </button>
                        <h1>طلاب دورة: تطوير مواقع الويب الشاملة</h1>
                    </div>
                    <div class="user-info">
                        <div class="user-avatar">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="المدير">
                        </div>
                        <div class="user-details">
                            <span class="user-name">أحمد محمد</span>
                            <span class="user-role">مدير النظام</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- المحتوى -->
            <div class="dashboard-content">
                <!-- معلومات الدورة -->
                <div class="course-info-banner">
                    <div class="course-details">
                        <h2>تطوير مواقع الويب الشاملة</h2>
                        <div class="course-meta">
                            <span><i class="fas fa-user"></i> المدرب: أحمد علي محمد</span>
                            <span><i class="fas fa-calendar"></i> تاريخ البدء: 2024-02-01</span>
                            <span><i class="fas fa-clock"></i> المدة: 8 أسابيع</span>
                            <span><i class="fas fa-users"></i> عدد الطلاب: 25 طالب</span>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="quick-stats">
                    <div class="stat-item">
                        <span class="stat-number">25</span>
                        <span class="stat-label">إجمالي الطلاب</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">18</span>
                        <span class="stat-label">طلاب مقبولين</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">5</span>
                        <span class="stat-label">قيد المراجعة</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">2</span>
                        <span class="stat-label">طلاب مرفوضين</span>
                    </div>
                </div>

                <!-- قائمة الطلاب -->
                <div class="students-section">
                    <div class="section-header">
                        <h2>قائمة الطلاب المسجلين</h2>
                        <div class="header-actions">
                            <button class="btn btn-primary" onclick="exportStudentsList()">
                                <i class="fas fa-download"></i>
                                تصدير القائمة
                            </button>
                        </div>
                    </div>

                    <!-- أدوات البحث والفلترة -->
                    <div class="search-filters">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="search-input" placeholder="البحث باسم الطالب أو البريد الإلكتروني...">
                        </div>
                        
                        <div class="filter-group">
                            <select id="status-filter">
                                <option value="">جميع الحالات</option>
                                <option value="approved">مقبول</option>
                                <option value="pending">قيد المراجعة</option>
                                <option value="rejected">مرفوض</option>
                            </select>
                        </div>
                    </div>

                    <!-- شبكة كروت الطلاب -->
                    <div class="students-grid">
                        <!-- طالب 1 -->
                        <div class="student-card" data-status="approved">
                            <div class="student-header">
                                <div class="student-avatar">
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face" alt="علي أحمد محمد">
                                </div>
                                <div class="student-info">
                                    <h3>علي أحمد محمد</h3>
                                    <span class="student-id">ID: ST001</span>
                                </div>
                                <div class="student-status approved">
                                    <i class="fas fa-check"></i>
                                    مقبول
                                </div>
                            </div>
                            
                            <div class="student-content">
                                <div class="info-item">
                                    <i class="fas fa-envelope"></i>
                                    <span><EMAIL></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-phone"></i>
                                    <span>+218 91 234 5678</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-calendar-plus"></i>
                                    <span>تاريخ التسجيل: 2024-01-20</span>
                                </div>
                            </div>
                            
                            <div class="student-actions">
                                <button class="btn btn-info" onclick="viewStudentAnswers(1)">
                                    <i class="fas fa-file-alt"></i>
                                    عرض الإجابات
                                </button>
                                <button class="btn btn-success" onclick="approveStudent(1)">
                                    <i class="fas fa-check"></i>
                                    قبول
                                </button>
                                <button class="btn btn-danger" onclick="rejectStudent(1)">
                                    <i class="fas fa-times"></i>
                                    رفض
                                </button>
                            </div>
                        </div>

                        <!-- طالب 2 -->
                        <div class="student-card" data-status="pending">
                            <div class="student-header">
                                <div class="student-avatar">
                                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face" alt="فاطمة سالم">
                                </div>
                                <div class="student-info">
                                    <h3>فاطمة سالم أحمد</h3>
                                    <span class="student-id">ID: ST002</span>
                                </div>
                                <div class="student-status pending">
                                    <i class="fas fa-clock"></i>
                                    قيد المراجعة
                                </div>
                            </div>
                            
                            <div class="student-content">
                                <div class="info-item">
                                    <i class="fas fa-envelope"></i>
                                    <span><EMAIL></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-phone"></i>
                                    <span>+218 92 345 6789</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-calendar-plus"></i>
                                    <span>تاريخ التسجيل: 2024-01-22</span>
                                </div>
                            </div>
                            
                            <div class="student-actions">
                                <button class="btn btn-info" onclick="viewStudentAnswers(2)">
                                    <i class="fas fa-file-alt"></i>
                                    عرض الإجابات
                                </button>
                                <button class="btn btn-success" onclick="approveStudent(2)">
                                    <i class="fas fa-check"></i>
                                    قبول
                                </button>
                                <button class="btn btn-danger" onclick="rejectStudent(2)">
                                    <i class="fas fa-times"></i>
                                    رفض
                                </button>
                            </div>
                        </div>

                        <!-- طالب 3 -->
                        <div class="student-card" data-status="approved">
                            <div class="student-header">
                                <div class="student-avatar">
                                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face" alt="محمد عبدالله">
                                </div>
                                <div class="student-info">
                                    <h3>محمد عبدالله سالم</h3>
                                    <span class="student-id">ID: ST003</span>
                                </div>
                                <div class="student-status approved">
                                    <i class="fas fa-check"></i>
                                    مقبول
                                </div>
                            </div>
                            
                            <div class="student-content">
                                <div class="info-item">
                                    <i class="fas fa-envelope"></i>
                                    <span><EMAIL></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-phone"></i>
                                    <span>+218 93 456 7890</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-calendar-plus"></i>
                                    <span>تاريخ التسجيل: 2024-01-25</span>
                                </div>
                            </div>
                            
                            <div class="student-actions">
                                <button class="btn btn-info" onclick="viewStudentAnswers(3)">
                                    <i class="fas fa-file-alt"></i>
                                    عرض الإجابات
                                </button>
                                <button class="btn btn-success" onclick="approveStudent(3)">
                                    <i class="fas fa-check"></i>
                                    قبول
                                </button>
                                <button class="btn btn-danger" onclick="rejectStudent(3)">
                                    <i class="fas fa-times"></i>
                                    رفض
                                </button>
                            </div>
                        </div>

                        <!-- طالب 4 -->
                        <div class="student-card" data-status="rejected">
                            <div class="student-header">
                                <div class="student-avatar">
                                    <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop&crop=face" alt="خالد محمود">
                                </div>
                                <div class="student-info">
                                    <h3>خالد محمود علي</h3>
                                    <span class="student-id">ID: ST004</span>
                                </div>
                                <div class="student-status rejected">
                                    <i class="fas fa-times"></i>
                                    مرفوض
                                </div>
                            </div>
                            
                            <div class="student-content">
                                <div class="info-item">
                                    <i class="fas fa-envelope"></i>
                                    <span><EMAIL></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-phone"></i>
                                    <span>+218 94 567 8901</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-calendar-plus"></i>
                                    <span>تاريخ التسجيل: 2024-01-28</span>
                                </div>
                            </div>
                            
                            <div class="student-actions">
                                <button class="btn btn-info" onclick="viewStudentAnswers(4)">
                                    <i class="fas fa-file-alt"></i>
                                    عرض الإجابات
                                </button>
                                <button class="btn btn-success" onclick="approveStudent(4)">
                                    <i class="fas fa-check"></i>
                                    قبول
                                </button>
                                <button class="btn btn-danger" onclick="rejectStudent(4)">
                                    <i class="fas fa-times"></i>
                                    رفض
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="admin-dashboard.js"></script>
    <script>
        function goBack() {
            window.history.back();
        }
        
        function viewStudentAnswers(studentId) {
            window.location.href = `student-answers.html?studentId=${studentId}`;
        }
        
        function approveStudent(studentId) {
            if(confirm('هل أنت متأكد من قبول هذا الطالب؟')) {
                alert('تم قبول الطالب بنجاح!');
                // هنا يمكن إضافة كود لإرسال الطلب للخادم
            }
        }
        
        function rejectStudent(studentId) {
            if(confirm('هل أنت متأكد من رفض هذا الطالب؟')) {
                alert('تم رفض الطالب!');
                // هنا يمكن إضافة كود لإرسال الطلب للخادم
            }
        }
        
        function exportStudentsList() {
            alert('سيتم تصدير قائمة الطلاب قريباً');
        }
    </script>
</body>
</html>

﻿@model Course
@{
    ViewData["Title"] = "حذف كورس";
}

<h2>هل أنت متأكد من حذف @Model.CourseName" ؟</h2>

<div class="alert alert-danger">
    <strong>@Model.CourseName</strong>
</div>

<form asp-action="DeleteConfirmed" enctype="multipart/form-data" method="post">
    <input type="hidden" asp-for="CourseId" />
    <button type="submit" value="Delete" class="btn btn-danger">نعم، احذف</button>
    <a asp-action="Index" class="btn btn-secondary">إلغاء</a>
</form>



  
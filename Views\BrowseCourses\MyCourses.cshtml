﻿@model List<Roya.ViewModels.MyCoursesVM>
@{
    ViewData["Title"] = "دوراتي";
    var selectedStatus = Context.Request.Query["status"].ToString();
}

<style>
    .my-courses-title {
        margin-top: 100px;
        font-weight: bold;
    }

    .my-courses-table {
        max-width: 1000px;
        margin: auto;
        border-radius: 12px;
        overflow: hidden;
    }

    .filter-buttons {
        text-align: center;
        margin-bottom: 20px;
    }

        .filter-buttons a {
            margin: 0 5px;
        }

    .details-btn {
        border-radius: 20px;
        padding: 6px 16px;
    }
</style>

<div class="container" dir="rtl">
    <h2 class="text-center text-primary my-courses-title mb-4">
        📚 الدورات التي اشتركت فيها
    </h2>

    <div class="filter-buttons mb-4">
        <a asp-action="MyCourses" asp-route-status="" class="btn btn-outline-dark @(string.IsNullOrEmpty(selectedStatus) ? "active" : "")">الكل</a>
        <a asp-action="MyCourses" asp-route-status="Approved" class="btn btn-outline-success @(selectedStatus == "Approved" ? "active" : "")">المقبولة</a>
        <a asp-action="MyCourses" asp-route-status="Pending" class="btn btn-outline-warning @(selectedStatus == "Pending" ? "active" : "")">قيد المراجعة</a>
        <a asp-action="MyCourses" asp-route-status="Rejected" class="btn btn-outline-danger @(selectedStatus == "Rejected" ? "active" : "")">المرفوضة</a>
    </div>

    @if (Model.Any())
    {
        <div class="table-responsive my-courses-table shadow-sm">
            <table class="table table-bordered text-center align-middle">
                <thead class="table-light">
                    <tr>
                        <th>اسم الدورة</th>
                        <th>المدرب</th>
                        <th>تاريخ البدء</th>
                        <th>حالة الاشتراك</th>
                        <th>تفاصيل</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var course in Model)
                    {
                        <tr>
                            <td>@course.CourseName</td>
                            <td>@course.TrainerName</td>
                            <td>@course.StartDate.ToString("yyyy/MM/dd")</td>
                            <td>
                                @switch (course.Status)
                                {
                                    case RegistrationStatus.Pending:
                                        <span class="badge bg-warning text-dark">قيد المراجعة</span>
                                        break;
                                    case RegistrationStatus.Approved:
                                        <span class="badge bg-success">مقبول</span>
                                        break;
                                    case RegistrationStatus.Rejected:
                                        <span class="badge bg-danger">مرفوض</span>
                                        break;
                                }
                            </td>
                            <td>
                                <a asp-controller="BrowseCourses" asp-action="Details" asp-route-id="@course.CourseId"
                                   class="btn btn-outline-primary btn-sm details-btn">
                                    👁️ عرض التفاصيل
                                </a>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
    else
    {
        <div class="alert alert-info text-center mt-5">
            لم يتم العثور على أي دورات وفقًا للتصفية المحددة.
        </div>
    }
</div>

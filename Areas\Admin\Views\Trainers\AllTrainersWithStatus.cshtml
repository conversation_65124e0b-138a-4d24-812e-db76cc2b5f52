﻿@model List<Trainer>

@{
    ViewData["Title"] = "إدارة حالة المدربين";
}

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<div class="container mt-5" style="max-width: 1100px;">
    <h2 class="text-center mb-4" style="color: #333; font-weight: bold;">
        🧑‍🏫 إدارة حالة المدربين
    </h2>

    @if (TempData["Message"] != null)
    {
        <div class="alert alert-success text-center shadow-sm">@TempData["Message"]</div>
    }

    <form asp-action="AllTrainersWithStatus" method="get"
          class="mb-4 d-flex justify-content-center align-items-center gap-3 flex-wrap shadow-sm p-3 rounded"
          style="background-color: #fdfdfd; border: 1px solid #eee;">
        <input type="text" name="searchString"
               class="form-control w-50"
               placeholder="ابحث بالاسم أو البريد الإلكتروني"
               value="@Context.Request.Query["searchString"]" />

        <select name="statusFilter" class="form-select w-auto">
            <option value="">كل الحالات</option>
            <option value="Pending" selected="@(Context.Request.Query["statusFilter"] == "Pending")">قيد المراجعة</option>
            <option value="Approved" selected="@(Context.Request.Query["statusFilter"] == "Approved")">مقبول</option>
            <option value="Rejected" selected="@(Context.Request.Query["statusFilter"] == "Rejected")">مرفوض</option>
        </select>

        <button type="submit" class="btn text-white fw-bold"
                style="background: linear-gradient(135deg, #ffa94d, #ff6b00); border: none;">
            🔍 
        </button>

        <a asp-action="AllTrainersWithStatus" class="btn btn-outline-secondary">🔄  </a>
    </form>

    @if (!Model.Any())
    {
        <div class="alert alert-warning text-center mt-3">لا توجد نتائج مطابقة لبحثك أو الفلترة المختارة.</div>
    }
    else
    {
        <div class="table-responsive shadow rounded">
            <table class="table table-bordered text-center align-middle mb-0"
                   style="background-color: #fff;">
                <thead style="background-color: #f8f9fa; color: #333;">
                    <tr style="font-weight: bold;">
                        <th>الاسم الكامل</th>
                        <th>البريد الإلكتروني</th>
                        <th>الحالة</th>
                        <th>تفاصيل</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var t in Model)
                    {
                        <tr>
                            <td>@t.FullName</td>
                            <td>@t.Email</td>
                            <td>
                                @switch (t.Status)
                                {
                                    case TrainerStatus.Pending:
                                        <span class="badge rounded-pill px-3 py-2"
                                              style="background-color: #fff3cd; color: #856404; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            ⏳ قيد المراجعة
                                        </span>
                                        break;
                                    case TrainerStatus.Approved:
                                        <span class="badge rounded-pill px-3 py-2"
                                              style="background-color: #d4edda; color: #155724; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            ✔ مقبول
                                        </span>
                                        break;
                                    case TrainerStatus.Rejected:
                                        <span class="badge rounded-pill px-3 py-2"
                                              style="background-color: #f8d7da; color: #721c24; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            ✘ مرفوض
                                        </span>
                                        break;
                                }
                            </td>
                            <td>
                                <a asp-action="Details" asp-route-id="@t.Id"
                                   class="btn btn-outline-info btn-sm rounded-pill shadow-sm">
                                    📄 عرض التفاصيل
                                </a>
                            </td>
                            <td class="d-flex justify-content-center gap-2 flex-wrap">
                                <form asp-action="UpdateStatus" method="post" class="confirm-form" data-current-status="@t.Status" data-trainer-name="@t.FullName">
                                    <input type="hidden" name="id" value="@t.Id" />
                                    <input type="hidden" name="newStatus" value="Approved" />
                                    <button class="btn btn-outline-success btn-sm rounded-pill px-3 shadow-sm">
                                        ✔ موافقة
                                    </button>
                                </form>

                                <form asp-action="UpdateStatus" method="post" class="confirm-form" data-current-status="@t.Status" data-trainer-name="@t.FullName">
                                    <input type="hidden" name="id" value="@t.Id" />
                                    <input type="hidden" name="newStatus" value="Rejected" />
                                    <button class="btn btn-outline-danger btn-sm rounded-pill px-3 shadow-sm">
                                        ✘ رفض
                                    </button>
                                </form>

                                <form asp-action="UpdateStatus" method="post" class="confirm-form" data-current-status="@t.Status" data-trainer-name="@t.FullName">
                                    <input type="hidden" name="id" value="@t.Id" />
                                    <input type="hidden" name="newStatus" value="Pending" />
                                    <button class="btn btn-outline-warning btn-sm text-dark rounded-pill px-3 shadow-sm">
                                        ⏳ مراجعة
                                    </button>
                                </form>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
</div>
<script>
      document.addEventListener("DOMContentLoaded", function () {
        const forms = document.querySelectorAll(".confirm-form");

        forms.forEach(form => {
            form.addEventListener("submit", function (e) {
                e.preventDefault();

                const currentStatus = form.dataset.currentStatus;
                const targetStatus = form.querySelector("input[name='newStatus']").value;
                const trainerName = form.dataset.trainerName;

                if (currentStatus === targetStatus) {
                    Swal.fire({
                        icon: 'info',
                        title: 'لا حاجة للإجراء',
                        text: `المدرب ${trainerName} بالفعل ${getArabicStatusName(targetStatus)}.`,
                        confirmButtonColor: '#28a745'
                    });
                    return;
                }

                let message = "";
                if (targetStatus === "Approved") {
                    message = ` ${trainerName}هل أنت متأكد من قبول المدرب؟`;
                } else if (targetStatus === "Rejected") {
                    message = ` ${trainerName}هل أنت متأكد من رفض  المدرب؟`;
                } else if (targetStatus === "Pending") {
                    message = `   الى قيد المراجعة ؟${trainerName}هل تريد إعادة المدرب `;
                }

                Swal.fire({
                    title: 'تأكيد الإجراء',
                    text: message,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#28a745',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'نعم، تأكيد',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        form.submit();
                    }
                });
            });
        });

        function getArabicStatusName(status) {
            switch (status) {
                case "Approved": return "مقبول";
                case "Rejected": return "مرفوض";
                case "Pending": return "قيد المراجعة";
                default: return "";
            }
        }
    });

</script>

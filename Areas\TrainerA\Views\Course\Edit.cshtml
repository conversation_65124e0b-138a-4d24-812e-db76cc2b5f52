﻿@model Course
@{
    ViewData["Title"] = "تعديل كورس";
}

<h2>تعديل كورس</h2>

<form asp-action="Edit" enctype="multipart/form-data" method="post">
    <input type="hidden" asp-for="CourseId" />

    <div class="mb-3">
        <label asp-for="CourseName" class="form-label"> اسم الدورة</label>
        <input asp-for="CourseName" class="form-control" />
        <span asp-validation-for="CourseName" class="text-danger"></span>
    </div>

    <div class="mb-3">
        <label asp-for="Location" class="form-label">الموقع</label>
        <input asp-for="Location" class="form-control" />
        <span asp-validation-for="Location" class="text-danger"></span>
    </div>

    <div class="mb-3">
        <label asp-for="CourseTopics" class="form-label">محاور الدورة</label>
        <input asp-for="CourseTopics" class="form-control" />
        <span asp-validation-for="CourseTopics" class="text-danger"></span>

    </div>

    <div class="mb-3">
        <label asp-for="Description" class="form-label">وصف الدورة</label>
        <input asp-for="Description" class="form-control" />
        <span asp-validation-for="Description" class="text-danger"></span>

    </div>

    <div class="mb-3">
        <label asp-for="Targetpeople" class="form-label"> الشخاص المستهدفون </label>
        <input asp-for="Targetpeople" class="form-control" />
        <span asp-validation-for="Targetpeople" class="text-danger"></span>

    </div>


    <div class="mb-3">
        <label asp-for="CategoryId" class="form-label">التصنيف</label>
        <select asp-for="CategoryId" class="form-select" asp-items="ViewBag.Categories">
            <option value="">-- Select Category --</option>
        </select>
        <span asp-validation-for="CategoryId" class="text-danger"></span>
    </div>

    <div class="mb-3">
        <label asp-for="StartDate" class="form-label">البداية</label>
        <input asp-for="StartDate" type="date" class="form-control" />
        <span asp-validation-for="StartDate" class="text-danger"></span>

    </div>

    <div class="mb-3">
        <label asp-for="EndDate" class="form-label">النهاية</label>
        <input asp-for="EndDate" type="date" class="form-control" />
        <span asp-validation-for="EndDate" class="text-danger"></span>

    </div>

    <div class="mb-3">
        <label asp-for="Time" class="form-label">الوقت </label>
        <input type="Time" asp-for="Time" class="form-control" />
        <span asp-validation-for="Time" class="text-danger"></span>

    </div>

    <div class="mb-3">
        <label asp-for="Duration" class="form-label"> الفترة</label>
        <input asp-for="Duration" class="form-control" />
        <span asp-validation-for="Duration" class="text-danger"></span>

    </div>
    <div class="mb-3">
        <label asp-for="PictureFile" class="form-label"></label>
        <input asp-for="PictureFile" class="form-control" type="file" />
        <span asp-validation-for="Image" class="text-danger"></span>
    </div>

    <button type="submit" class="btn btn-primary">تحديث</button>
    <a asp-action="Index" class="btn btn-secondary">رجوع</a>
</form>

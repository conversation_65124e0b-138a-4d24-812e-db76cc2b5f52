﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Roya.Data;
using Roya.Models;
using Roya.ViewModels;

namespace Roya.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class CoursesReviewController : Controller
    {
        private readonly AppDbContext _context;
        private readonly IWebHostEnvironment _env;

        public CoursesReviewController(AppDbContext context, IWebHostEnvironment env)
        {
            _context = context;
            _env = env;
        }

        public async Task<IActionResult> Index()
        {
            var pendingCourses = await _context.Courses
                .Include(c => c.Trainer)
                .Include(c => c.Category)
                .Where(c => c.Status == CourseStatus.Pending)
                .ToListAsync();

            return View(pendingCourses);
        }
        [HttpPost]
        public async Task<IActionResult> Approve(int id)
        {
            var course = await _context.Courses
                .Include(c => c.Trainer)
                .FirstOrDefaultAsync(c => c.CourseId == id);

            if (course != null)
            {
                course.Status = CourseStatus.Approved;

                // إضافة سجل التعديل
                var log = new CourseStatusLog
                {
                    CourseId = course.CourseId,
                    CourseName = course.CourseName,
                    TrainerName = course.Trainer?.FullName,
                    ActionType = "Approved",
                    ActionDate = DateTime.Now,
                    PerformedBy = "Admin" // غيره إذا كان هناك حساب فعلي
                };

                _context.CourseStatusLogs.Add(log);
                await _context.SaveChangesAsync();
            }

            string phone = course.Trainer?.PhoneNum;
            string message = Uri.EscapeDataString($"✔️ تم قبول دورتك ({course.CourseName})، يمكنك الآن متابعتها داخل المنصة.");
            string url = $"https://wa.me/{phone}?text={message}";
            return Redirect(url);
        }

        [HttpPost]
        public async Task<IActionResult> Reject(int id)
        {
            var course = await _context.Courses
                .Include(c => c.Trainer)
                .FirstOrDefaultAsync(c => c.CourseId == id);

            if (course != null)
            {
                course.Status = CourseStatus.Rejected;

                var log = new CourseStatusLog
                {
                    CourseId = course.CourseId,
                    CourseName = course.CourseName,
                    TrainerName = course.Trainer?.FullName,
                    ActionType = "Rejected",
                    ActionDate = DateTime.Now,
                    PerformedBy = "Admin"
                };

                _context.CourseStatusLogs.Add(log);
                await _context.SaveChangesAsync();
            }

            // إرسال رسالة واتساب إلى المدرب بعد الرفض
            string phone = course.Trainer?.PhoneNum;
            string message = Uri.EscapeDataString($"❌ تم رفض دورتك ({course.CourseName})، نرجو مراجعة ملاحظات المنصة أو التواصل معنا.");
            string url = $"https://wa.me/{phone}?text={message}";
            return Redirect(url);
        }



        // Admin/CoursesController.cs
        public async Task<IActionResult> AllCourses(string trainerName, string field, string status)
        {
            var courses = _context.Courses
                                  .Include(c => c.Trainer)
                                  .AsQueryable();

            // حفظ قيم البحث في ViewBag
            ViewBag.TrainerName = trainerName;
            ViewBag.SelectedField = field;
            ViewBag.SelectedStatus = status;

            // فلترة بالحالة إن وجدت
            if (!string.IsNullOrEmpty(status))
            {
                if (Enum.TryParse(status, out CourseStatus parsedStatus))
                {
                    courses = courses.Where(c => c.Status == parsedStatus);
                }
            }

            // فلترة حسب اسم المدرب أو الكورس
            if (!string.IsNullOrEmpty(trainerName))
            {
                if (field == "Trainer")
                {
                    courses = courses.Where(c => c.Trainer != null && c.Trainer.FullName.Contains(trainerName));
                }
                else if (field == "Course")
                {
                    courses = courses.Where(c => c.CourseName.Contains(trainerName));
                }
            }

            var courseList = await courses.OrderByDescending(c => c.CourseId).ToListAsync();
            return View(courseList);
        }

        public async Task<IActionResult> StatusLog()
        {
            var logs = await _context.CourseStatusLogs
                .OrderByDescending(l => l.ActionDate)
                .ToListAsync();

            return View(logs);
        }



        [HttpGet]
        public async Task<IActionResult> Edit(int id)
        {
            var course = await _context.Courses
                .Include(c => c.Questions)
                    .ThenInclude(q => q.Options)
                .FirstOrDefaultAsync(c => c.CourseId == id);

            if (course == null)
                return NotFound();

            var vm = new CourseWithQuestionsVM
            {
                Course = course,
                Questions = course.Questions.Select(q => new QuestionVM
                {
                    QuestionId = q.QuestionId,
                    QuestionText = q.QuestionText,
                    QuestionType = q.Type,
                    OptionsText = q.Type == QuestionType.MultipleChoice
                        ? string.Join(", ", q.Options.Select(o => o.OptionText))
                        : null
                }).ToList()
            };

            ViewBag.Categories = await _context.Categories.ToListAsync();
            return View(vm);
        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, CourseWithQuestionsVM vm)
        {
            var course = await _context.Courses
                .Include(c => c.Questions)
                    .ThenInclude(q => q.Options)
                .FirstOrDefaultAsync(c => c.CourseId == id);

            if (course == null) return NotFound();

            // ✅ تحديث بيانات الكورس الأساسية
            course.CourseName = vm.Course.CourseName;
            course.Description = vm.Course.Description;
            course.Targetpeople = vm.Course.Targetpeople;
            course.Location = vm.Course.Location;
            course.StartDate = vm.Course.StartDate;
            course.EndDate = vm.Course.EndDate;
            course.Duration = vm.Course.Duration;
            course.Time = vm.Course.Time;
            course.CategoryId = vm.Course.CategoryId;
            course.CourseTopics = vm.Course.CourseTopics;

            // ✅ تعديل الصورة إذا وُجدت صورة جديدة
            if (vm.Course.PictureFile != null)
            {
                // اسم جديد للصورة
                string imageName = Guid.NewGuid() + Path.GetExtension(vm.Course.PictureFile.FileName);
                string imagePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot/uploads/CourseImages", imageName);

                // إنشاء المجلد إذا لم يكن موجودًا
                Directory.CreateDirectory(Path.GetDirectoryName(imagePath)!);

                using (var stream = new FileStream(imagePath, FileMode.Create))
                {
                    await vm.Course.PictureFile.CopyToAsync(stream);
                }

                // حذف الصورة القديمة (اختياري)
                if (!string.IsNullOrEmpty(course.Image))
                {
                    string oldPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", course.Image.TrimStart('/'));
                    if (System.IO.File.Exists(oldPath))
                        System.IO.File.Delete(oldPath);
                }

                // حفظ مسار الصورة
                course.Image = "/uploads/CourseImages/" + imageName;
            }

            // ✅ حذف الأسئلة القديمة
            _context.Questions.RemoveRange(course.Questions);

            // ✅ إضافة الأسئلة الجديدة
            foreach (var q in vm.Questions)
            {
                var newQ = new Question
                {
                    CourseId = course.CourseId,
                    QuestionText = q.QuestionText,
                    Type = q.QuestionType
                };

                if (q.QuestionType == QuestionType.MultipleChoice && !string.IsNullOrEmpty(q.OptionsText))
                {
                    newQ.Options = q.OptionsText.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                                .Select(opt => new ChoiceOption { OptionText = opt.Trim() }).ToList();
                }

                _context.Questions.Add(newQ);
            }

            await _context.SaveChangesAsync();
            TempData["SuccessMessage"] = "✅ تم تعديل الكورس، الصورة، والأسئلة بنجاح.";
            return RedirectToAction("Index");
        }


     


    }
}

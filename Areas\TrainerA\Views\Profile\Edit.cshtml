﻿@model Roya.Models.Trainer
@{
    ViewData["Title"] = "تعديل البيانات الشخصية";
}

<div class="container mt-4">
    <h2 class="mb-4 text-center">تعديل البيانات الشخصية</h2>

    @if (TempData["Message"] != null)
    {
        <div class="alert alert-success text-center">
            @TempData["Message"]
        </div>
    }

    @if (!ViewData.ModelState.IsValid)
    {
        <div class="alert alert-danger">
            <ul>
                @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                {
                    <li>@error.ErrorMessage</li>
                }
            </ul>
        </div>
    }

    <form asp-action="Edit" enctype="multipart/form-data" method="post" class="shadow p-4 rounded bg-light">

        <!-- صورة المدرب -->
        <div class="text-center mb-4">
            @if (!string.IsNullOrEmpty(Model.Image))
            {
                <img src="@Model.Image" alt="الصورة الشخصية" class="rounded-circle" style="width: 150px; height: 150px; object-fit: cover;" />
            }
            else
            {
                <img src="~/images/default-avatar.png" alt="الصورة الشخصية" class="rounded-circle" style="width: 150px; height: 150px; object-fit: cover;" />
            }

            <div class="mt-2">
                <label class="btn btn-sm btn-outline-primary">
                    تغيير الصورة <input type="file" name="ImageFile" hidden />
                </label>
            </div>
        </div>

        <!-- السيرة الذاتية -->
        <div class="form-group mb-3 text-center">
            <label>السيرة الذاتية الحالية:</label>
            @if (!string.IsNullOrEmpty(Model.CV))
            {
                <a href="@Model.CV" target="_blank" class="d-block text-info">📄 عرض السيرة الذاتية</a>
            }
            else
            {
                <span class="text-muted">لم يتم رفع سيرة ذاتية.</span>
            }

            <div class="mt-2">
                <label class="btn btn-sm btn-outline-secondary">
                    تغيير السيرة الذاتية <input type="file" name="CVFile" hidden />
                </label>
            </div>
        </div>

        <!-- باقي البيانات -->
        <div class="form-group mb-3">
            <label>الاسم الكامل</label>
            <input asp-for="FullName" class="form-control" />
        </div>

        <div class="form-group mb-3">
            <label>رقم الهاتف</label>
            <input asp-for="PhoneNum" class="form-control" />
        </div>

        <div class="form-group mb-3">
            <label>المدينة</label>
            <input asp-for="City" class="form-control" />
        </div>

        <div class="form-group mb-3">
            <label>العمر</label>
            <input asp-for="Age" type="number" class="form-control" />
        </div>

        <div class="form-group mb-3">
            <label>سنوات الخبرة</label>
            <input asp-for="ExperienceYears" class="form-control" />
        </div>

        <div class="text-center">
            <button type="button" id="confirmButton" class="btn btn-success px-4">💾 حفظ التعديلات</button>
        </div>
    </form>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        document.getElementById("confirmButton").addEventListener("click", function () {
            Swal.fire({
                title: 'هل أنت متأكد؟',
                text: 'بعد حفظ التعديلات، سيتم تعليق حسابك مؤقتًا حتى تقوم الإدارة بمراجعتها والموافقة عليها.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احفظ التعديلات',
                cancelButtonText: 'إلغاء',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    document.forms[0].submit();
                }
            });
        });
    </script>
}

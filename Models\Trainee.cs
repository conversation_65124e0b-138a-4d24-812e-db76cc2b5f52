﻿using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Roya.Models
{
    public class Trainee : IdentityUser
    {

       


        public string? FullName { get; set; }


        public string? PhoneNum { get; set; }
        public string? City { get; set; }
        public int Age { get; set; }
        public string? MajorOrProfession { get; set; }

        public List<Register> Registers { get; set; } = new List<Register>();
        public List<Answer> Answers { get; set; } = new List<Answer>();


    }
}

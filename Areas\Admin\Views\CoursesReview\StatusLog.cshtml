﻿@model IEnumerable<CourseStatusLog>

<h3 class="text-center my-4">📋 سجل تعديلات حالة الكورسات</h3>

<table class="table table-bordered table-hover text-center">
    <thead class="table-light">
        <tr>
            <th>📘 اسم الدورة</th>
            <th>👨‍🏫 اسم المدرب</th>
            <th>📌 الحالة</th>
            <th>📅 تاريخ التعديل</th>
            <th>👤 تم بواسطة</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var log in Model)
        {
            <tr>
                <td>@log.CourseName</td>
                <td>@log.TrainerName</td>
                <td>
                    @if (log.ActionType == "Approved")
                    {
                        <span class="badge bg-success">مقبولة</span>
                    }
                    else if (log.ActionType == "Rejected")
                    {
                        <span class="badge bg-danger">مرفوضة</span>
                    }
                </td>
                <td>@log.ActionDate.ToString("yyyy/MM/dd HH:mm")</td>
                <td>@log.PerformedBy</td>
            </tr>
        }
    </tbody>
</table>

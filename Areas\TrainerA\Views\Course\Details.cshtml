﻿@model Roya.Models.Course

@{
    ViewData["Title"] = "تفاصيل الكورس";
}

<div class="container my-5" style="max-width: 900px;">
    <h2 class="mb-4 text-center">📘 تفاصيل الكورس</h2>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="row g-4 align-items-center">
                <!-- ✅ الصورة في اليمين -->
                @if (!string.IsNullOrEmpty(Model.Image))
                {
                    <div class="col-md-5 text-center">
                        <img src="@Model.Image" class="img-fluid rounded shadow-sm" style="max-height: 300px;" />
                    </div>
                }

                <!-- ✅ التفاصيل في اليسار -->
                <div class="col-md-7">
                    <h4 class="fw-bold mb-3">@Model.CourseName</h4>
                    <p><strong>📍 المكان:</strong> @Model.Location</p>
                    <p><strong>📅 من:</strong> @Model.StartDate.ToShortDateString() <strong>إلى:</strong> @Model.EndDate.ToShortDateString()</p>
                    <p><strong>🕒 الوقت:</strong> @Model.Time</p>
                    <p><strong>⏳ المدة:</strong> @Model.Duration</p>
                    <p><strong>🎯 الفئة المستهدفة:</strong> @Model.Targetpeople</p>
                    <p><strong>🗂️ التصنيف:</strong> @Model.Category?.CategoryName</p>
                    <p><strong>📄 الوصف:</strong> @Model.Description</p>
                    <p><strong>🧾 المواضيع:</strong> @Model.CourseTopics</p>

                    <p>
                        <strong>📌 الحالة:</strong>
                        @switch (Model.Status)
                        {
                            case Roya.Models.CourseStatus.Approved:
                                <span class="badge bg-success">مقبول</span>
                                break;
                            case Roya.Models.CourseStatus.Pending:
                                <span class="badge bg-warning text-dark">قيد المراجعة</span>
                                break;
                            case Roya.Models.CourseStatus.Rejected:
                                <span class="badge bg-danger">مرفوض</span>
                                break;
                        }
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- ✅ الأسئلة المرتبطة -->
    @if (Model.Questions?.Any() == true)
    {
        <div class="card shadow-sm">
            <div class="card-header fw-bold">📝 الأسئلة</div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    @foreach (var q in Model.Questions)
                    {
                        <li class="list-group-item">
                            <p class="mb-1"><strong>📌 السؤال:</strong> @q.QuestionText</p>
                            <p><strong>🔹 النوع:</strong> @(q.Type == Roya.Models.QuestionType.MultipleChoice ? "اختياري" : "نصي")</p>

                            @if (q.Type == Roya.Models.QuestionType.MultipleChoice && q.Options?.Any() == true)
                            {
                                <p><strong>🔘 الخيارات:</strong></p>
                                <ul>
                                    @foreach (var opt in q.Options)
                                    {
                                        <li>@opt.OptionText</li>
                                    }
                                </ul>
                            }
                        </li>
                    }
                </ul>
            </div>
        </div>
    }
    else
    {
        <div class="alert alert-info">لا توجد أسئلة مضافة لهذا الكورس.</div>
    }

    <div class="text-end mt-4">
        <a asp-action="MyCourses" class="btn btn-outline-secondary">🔙 العودة للكورسات</a>
    </div>
</div>

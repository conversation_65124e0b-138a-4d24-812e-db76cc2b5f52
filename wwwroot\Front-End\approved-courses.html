<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الدورات المقبولة - رؤية 2030</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="admin-dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- الشريط الجانبي -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="logo.png" alt="رؤية 2030">
                    <h3>لوحة التحكم</h3>
                </div>
                <button class="sidebar-toggle-btn" id="sidebarToggle" title="تصغير/توسيع القائمة">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item">
                        <a href="admin-dashboard.html" class="nav-link">
                            <i class="fas fa-home"></i>
                            <span>الصفحة الرئيسية</span>
                        </a>
                    </li>
                    
                    <li class="nav-item has-submenu">
                        <a href="#" class="nav-link">
                            <i class="fas fa-chalkboard-teacher"></i>
                            <span>إدارة المدربين</span>
                            <i class="fas fa-chevron-down submenu-arrow"></i>
                        </a>
                        <ul class="submenu">
                            <li><a href="trainer-requests.html">طلبات المدربين</a></li>
                            <li><a href="all-trainers.html">جميع المدربين</a></li>
                            <li><a href="trainer-modifications.html">سجل التعديلات</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item has-submenu open">
                        <a href="#" class="nav-link">
                            <i class="fas fa-book"></i>
                            <span>إدارة الدورات</span>
                            <i class="fas fa-chevron-down submenu-arrow"></i>
                        </a>
                        <ul class="submenu">
                            <li><a href="course-requests.html">طلبات الدورات</a></li>
                            <li><a href="all-courses.html">جميع الدورات</a></li>
                            <li><a href="approved-courses.html" class="active">الدورات المقبولة</a></li>
                            <li><a href="course-modifications.html">سجل التعديلات</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item">
                        <a href="settings.html" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>تسجيل الخروج</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- الهيدر العلوي -->
            <header class="main-header">
                <div class="header-content">
                    <h1>الدورات المقبولة والطلاب</h1>
                    <div class="user-info">
                        <div class="user-avatar">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="المدير">
                        </div>
                        <div class="user-details">
                            <span class="user-name">أحمد محمد</span>
                            <span class="user-role">مدير النظام</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- المحتوى -->
            <div class="dashboard-content">
                <!-- إحصائيات سريعة -->
                <div class="quick-stats">
                    <div class="stat-item">
                        <span class="stat-number">85</span>
                        <span class="stat-label">دورات مقبولة</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">1,250</span>
                        <span class="stat-label">إجمالي الطلاب</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">320</span>
                        <span class="stat-label">طلاب جدد</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">45</span>
                        <span class="stat-label">طلبات قيد المراجعة</span>
                    </div>
                </div>

                <!-- جدول الدورات والطلاب -->
                <div class="table-section">
                    <div class="section-header">
                        <h2>الدورات المقبولة وطلابها</h2>
                        <div class="header-actions">
                            <button class="btn btn-primary" onclick="exportStudentsData()">
                                <i class="fas fa-download"></i>
                                تصدير بيانات الطلاب
                            </button>
                        </div>
                    </div>

                    <!-- أدوات البحث والفلترة -->
                    <div class="search-filters">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="search-input" placeholder="البحث باسم الدورة أو المدرب...">
                        </div>
                        
                        <div class="filter-group">
                            <select id="category-filter">
                                <option value="">جميع التصنيفات</option>
                                <option value="tech">تقنية المعلومات</option>
                                <option value="marketing">التسويق</option>
                                <option value="design">التصميم</option>
                                <option value="business">إدارة الأعمال</option>
                            </select>
                            
                            <select id="students-filter">
                                <option value="">جميع الدورات</option>
                                <option value="has-students">دورات بها طلاب</option>
                                <option value="no-students">دورات بدون طلاب</option>
                            </select>
                        </div>
                    </div>

                    <!-- الجدول -->
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>اسم الدورة</th>
                                    <th>اسم المدرب</th>
                                    <th>التصنيف</th>
                                    <th>الحالة</th>
                                    <th>عدد الطلاب</th>
                                    <th>تاريخ البدء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="table-row" data-category="tech">
                                    <td><strong>تطوير مواقع الويب الشاملة</strong></td>
                                    <td>أحمد علي محمد</td>
                                    <td><span class="category-badge tech">تقنية المعلومات</span></td>
                                    <td><span class="status-badge approved">نشطة</span></td>
                                    <td>
                                        <span class="students-count">
                                            <i class="fas fa-users"></i>
                                            25 طالب
                                        </span>
                                    </td>
                                    <td>2024-02-01</td>
                                    <td class="actions">
                                        <button class="btn-icon btn-info" onclick="viewStudents(1)" title="عرض الطلاب">
                                            <i class="fas fa-users"></i>
                                        </button>
                                        <button class="btn-icon btn-success" onclick="viewCourseDetails(1)" title="تفاصيل الدورة">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                
                                <tr class="table-row" data-category="marketing">
                                    <td><strong>التسويق الرقمي المتقدم</strong></td>
                                    <td>فاطمة أحمد سالم</td>
                                    <td><span class="category-badge marketing">التسويق</span></td>
                                    <td><span class="status-badge approved">نشطة</span></td>
                                    <td>
                                        <span class="students-count">
                                            <i class="fas fa-users"></i>
                                            18 طالب
                                        </span>
                                    </td>
                                    <td>2024-02-05</td>
                                    <td class="actions">
                                        <button class="btn-icon btn-info" onclick="viewStudents(2)" title="عرض الطلاب">
                                            <i class="fas fa-users"></i>
                                        </button>
                                        <button class="btn-icon btn-success" onclick="viewCourseDetails(2)" title="تفاصيل الدورة">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                
                                <tr class="table-row" data-category="design">
                                    <td><strong>التصميم الجرافيكي الاحترافي</strong></td>
                                    <td>سارة أحمد علي</td>
                                    <td><span class="category-badge design">التصميم</span></td>
                                    <td><span class="status-badge approved">نشطة</span></td>
                                    <td>
                                        <span class="students-count">
                                            <i class="fas fa-users"></i>
                                            32 طالب
                                        </span>
                                    </td>
                                    <td>2024-02-10</td>
                                    <td class="actions">
                                        <button class="btn-icon btn-info" onclick="viewStudents(3)" title="عرض الطلاب">
                                            <i class="fas fa-users"></i>
                                        </button>
                                        <button class="btn-icon btn-success" onclick="viewCourseDetails(3)" title="تفاصيل الدورة">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                
                                <tr class="table-row" data-category="tech">
                                    <td><strong>البرمجة بـ Python المتقدمة</strong></td>
                                    <td>خالد محمود أحمد</td>
                                    <td><span class="category-badge tech">تقنية المعلومات</span></td>
                                    <td><span class="status-badge approved">نشطة</span></td>
                                    <td>
                                        <span class="students-count">
                                            <i class="fas fa-users"></i>
                                            15 طالب
                                        </span>
                                    </td>
                                    <td>2024-02-15</td>
                                    <td class="actions">
                                        <button class="btn-icon btn-info" onclick="viewStudents(4)" title="عرض الطلاب">
                                            <i class="fas fa-users"></i>
                                        </button>
                                        <button class="btn-icon btn-success" onclick="viewCourseDetails(4)" title="تفاصيل الدورة">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                
                                <tr class="table-row" data-category="business">
                                    <td><strong>إدارة المشاريع الاحترافية</strong></td>
                                    <td>محمد سالم عبدالله</td>
                                    <td><span class="category-badge business">إدارة الأعمال</span></td>
                                    <td><span class="status-badge approved">نشطة</span></td>
                                    <td>
                                        <span class="students-count">
                                            <i class="fas fa-users"></i>
                                            28 طالب
                                        </span>
                                    </td>
                                    <td>2024-02-20</td>
                                    <td class="actions">
                                        <button class="btn-icon btn-info" onclick="viewStudents(5)" title="عرض الطلاب">
                                            <i class="fas fa-users"></i>
                                        </button>
                                        <button class="btn-icon btn-success" onclick="viewCourseDetails(5)" title="تفاصيل الدورة">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                
                                <tr class="table-row" data-category="marketing">
                                    <td><strong>إدارة وسائل التواصل الاجتماعي</strong></td>
                                    <td>نور الدين أحمد</td>
                                    <td><span class="category-badge marketing">التسويق</span></td>
                                    <td><span class="status-badge approved">نشطة</span></td>
                                    <td>
                                        <span class="students-count no-students">
                                            <i class="fas fa-users"></i>
                                            0 طالب
                                        </span>
                                    </td>
                                    <td>2024-02-25</td>
                                    <td class="actions">
                                        <button class="btn-icon btn-info" onclick="viewStudents(6)" title="عرض الطلاب">
                                            <i class="fas fa-users"></i>
                                        </button>
                                        <button class="btn-icon btn-success" onclick="viewCourseDetails(6)" title="تفاصيل الدورة">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- الترقيم -->
                    <div class="pagination">
                        <button class="pagination-btn disabled" data-action="prev" disabled>
                            <i class="fas fa-chevron-right"></i> السابق
                        </button>
                        <span class="page-info">
                            صفحة <span class="current-page">1</span> من <span class="total-pages">6</span>
                        </span>
                        <button class="pagination-btn" data-action="next">
                            التالي <i class="fas fa-chevron-left"></i>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="admin-dashboard.js"></script>
    <script>
        function viewStudents(courseId) {
            window.location.href = `course-students.html?courseId=${courseId}`;
        }
        
        function exportStudentsData() {
            alert('سيتم تصدير بيانات الطلاب قريباً');
        }
    </script>
</body>
</html>

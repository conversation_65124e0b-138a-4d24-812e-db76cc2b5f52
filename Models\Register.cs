﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace Roya.Models
{
    public enum RegistrationStatus
    {
        Pending,
        Approved,
        Rejected
    }
    public class Register
    {
        [Key]
        public int RegisterId { get; set; }

   
        public string userId { get; set; }

        [ValidateNever]
        public Trainee Trainee { get; set; }


     
        public int CourseId { get; set; }

        [ValidateNever]
        public Course Course { get; set; }


        public DateTime RegistrationDate { get; set; }
        public RegistrationStatus RegistrationStatus { get; set; } // bool?
    }
}

﻿@model Roya.ViewModels.CourseWithQuestionsVM
@using Roya.Models

@{
    ViewData["Title"] = "إضافة كورس جديد";
    var categories = ViewBag.Categories as List<Category>;
}

<!-- ✅ Bootstrap + SweetAlert -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<div class="container my-5" style="max-width: 900px;">
    <h2 class="text-center mb-4">📚 إضافة كورس جديد</h2>

    <div class="card shadow-sm">
        <div class="card-body">
            <form asp-action="Create" method="post" enctype="multipart/form-data">
                <div asp-validation-summary="All" class="text-danger mb-3"></div>

                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">اسم الكورس</label>
                        <input asp-for="Course.CourseName" class="form-control" />
                        <span asp-validation-for="Course.CourseName" class="text-danger"></span>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">الموقع</label>
                        <input asp-for="Course.Location" class="form-control" />
                        <span asp-validation-for="Course.Location" class="text-danger"></span>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">تاريخ البدء</label>
                        <input asp-for="Course.StartDate" type="date" class="form-control" />
                        <span asp-validation-for="Course.StartDate" class="text-danger"></span>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">تاريخ الانتهاء</label>
                        <input asp-for="Course.EndDate" type="date" class="form-control" />
                        <span asp-validation-for="Course.EndDate" class="text-danger"></span>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">المدة</label>
                        <input asp-for="Course.Duration" class="form-control" />
                        <span asp-validation-for="Course.Duration" class="text-danger"></span>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">الوقت</label>
                        <input asp-for="Course.Time" class="form-control" />
                        <span asp-validation-for="Course.Time" class="text-danger"></span>
                    </div>

                    <div class="col-md-12">
                        <label class="form-label">الفئة المستهدفة</label>
                        <input asp-for="Course.Targetpeople" class="form-control" />
                        <span asp-validation-for="Course.Targetpeople" class="text-danger"></span>
                    </div>

                    <div class="col-md-12">
                        <label class="form-label">تفاصيل الكورس</label>
                        <textarea asp-for="Course.Description" class="form-control"></textarea>
                        <span asp-validation-for="Course.Description" class="text-danger"></span>
                    </div>

                    <div class="col-md-12">
                        <label class="form-label">مواضيع الكورس</label>
                        <textarea asp-for="Course.CourseTopics" class="form-control"></textarea>
                        <span asp-validation-for="Course.CourseTopics" class="text-danger"></span>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">التصنيف</label>
                        <select asp-for="Course.CategoryId" class="form-select">
                            <option value="">-- اختر تصنيف --</option>
                            @foreach (var c in categories)
                            {
                                <option value="@c.Id">@c.CategoryName</option>
                            }
                        </select>
                        <span asp-validation-for="Course.CategoryId" class="text-danger"></span>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">صورة الكورس</label>
                        <input asp-for="Course.PictureFile" type="file" class="form-control" />
                        <span asp-validation-for="Course.PictureFile" class="text-danger"></span>
                    </div>
                </div>

                <hr class="my-4" />

                <!-- ✅ قسم الأسئلة -->
                <h5 class="mb-3 fw-bold">📝 أسئلة الاشتراك</h5>
                <div id="questions-container" class="mb-4"></div>
                <button type="button" class="btn btn-outline-secondary mb-3" onclick="addQuestion()">➕ إضافة سؤال</button>

                <div class="text-end mt-3">
                    <button type="submit" class="btn btn-dark px-4">💾 حفظ الكورس</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- ✅ قالب مخفي للسؤال -->
<div id="question-template" class="d-none">
    <div class="question-block border rounded p-3 mb-3 shadow-sm bg-light">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <label class="form-label mb-0">السؤال</label>
            <button type="button" class="btn btn-sm btn-danger" onclick="removeQuestion(this)">🗑 حذف</button>
        </div>
        <input name="Questions[__index__].QuestionText" class="form-control mb-2" placeholder="اكتب نص السؤال" required />

        <select name="Questions[__index__].QuestionType" class="form-select" onchange="toggleOptions(this)">
            <option value="Text">سؤال نصي</option>
            <option value="MultipleChoice">سؤال اختياري</option>
        </select>

        <div class="options-container mt-2 d-none">
            <label class="form-label mt-2">الخيارات (افصل بينها بفاصلة)</label>
            <input name="Questions[__index__].OptionsText" class="form-control" placeholder="مثال: نعم, لا, ربما" />
        </div>
    </div>
</div>

<!-- ✅ سكربت ديناميكي -->
<script>
    document.querySelector("form").addEventListener("submit", function (e) {
        const questionCount = document.querySelectorAll(".question-block").length;
        if (questionCount === 0) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'يجب إضافة سؤال واحد على الأقل قبل حفظ الكورس.'
            });
        }
    });
</script>



<script>
    let questionIndex = 0;

    function addQuestion() {
        const template = document.getElementById("question-template").innerHTML;
        const html = template.replace(/__index__/g, questionIndex);
        document.getElementById("questions-container").insertAdjacentHTML("beforeend", html);
        questionIndex++;
    }

    function removeQuestion(button) {
        button.closest(".question-block").remove();
    }

    function toggleOptions(select) {
        const optionsContainer = select.closest(".question-block").querySelector(".options-container");
        optionsContainer.classList.toggle("d-none", select.value !== "MultipleChoice");
    }
</script>
<script>
    document.querySelector("form").addEventListener("submit", function (e) {
        const startDate = new Date(document.querySelector("[name='Course.StartDate']").value);
        const endDate = new Date(document.querySelector("[name='Course.EndDate']").value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (startDate < today || endDate < today) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: 'تاريخ غير صالح',
                text: 'لا يمكن إدخال تاريخ في الماضي.'
            });
        }
    });
</script>

﻿@model Roya.ViewModels.CourseJoinVM
@{
    ViewData["Title"] = "الاشتراك في الدورة";
}

<div class="container mt-5" dir="rtl">
    <h2 class="text-center text-primary mb-4">📝 الاشتراك في الدورة</h2>

    <div class="card mb-4 shadow-sm">
        <div class="card-body text-center">
            <h4 class="mb-3">@Model.Course.CourseName</h4>
            <p class="text-muted">@Model.Course.Description</p>
        </div>
    </div>

    <form asp-action="Join" method="post">
        @Html.AntiForgeryToken()
        <input type="hidden" name="id" value="@Model.Course.CourseId" />

        @if (Model.Questions != null && Model.Questions.Any())
        {
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <strong>📋 الأسئلة:</strong>
                </div>
                <div class="card-body">
                    @for (int i = 0; i < Model.Questions.Count; i++)
                    {
                        var question = Model.Questions[i];
                        <div class="mb-3">
                            <label class="form-label d-block fw-bold">@($"{i + 1}. {question.QuestionText}")</label>

                            @if (question.Type == Roya.Models.QuestionType.Text)
                            {
                                <input type="text" class="form-control" name="Answers[@question.QuestionId]" placeholder="أدخل إجابتك هنا..." required />
                            }
                            else if (question.Type == Roya.Models.QuestionType.MultipleChoice && question.Options != null && question.Options.Any())
                            {
                                foreach (var option in question.Options)
                                {
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="Answers[@question.QuestionId]" value="@option.OptionText" required />
                                        <label class="form-check-label">@option.OptionText</label>
                                    </div>
                                }
                            }
                        </div>
                    }
                </div>
            </div>
        }

        <div class="text-center">
            <button type="submit" class="btn btn-success btn-lg">📨 إرسال الإجابات والاشتراك</button>
        </div>
    </form>
</div>
